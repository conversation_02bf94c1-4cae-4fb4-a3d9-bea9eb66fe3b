# WebSocket Technical Specifications

## WebSocket Endpoint
```
ws://localhost:8000/ws/exam/{exam_id}/{student_id}
```

## Message Protocol

### Client → Server Messages

#### 1. Join <PERSON>am Session
```json
{
    "type": "join_exam",
    "exam_id": "uuid",
    "student_id": "uuid",
    "timestamp": "2024-01-15T14:00:00Z"
}
```

#### 2. Save Answer
```json
{
    "type": "save_answer",
    "question_id": "uuid",
    "answer": "selected_option_text",
    "timestamp": "2024-01-15T14:00:00Z"
}
```

#### 3. Submit Exam
```json
{
    "type": "submit_exam",
    "exam_id": "uuid",
    "student_id": "uuid",
    "final_answers": [
        {
            "question_id": "uuid",
            "answer": "selected_option",
            "timestamp": "2024-01-15T14:00:00Z"
        }
    ],
    "timestamp": "2024-01-15T14:00:00Z"
}
```

#### 4. Heartbeat
```json
{
    "type": "heartbeat",
    "student_id": "uuid",
    "timestamp": "2024-01-15T14:00:00Z"
}
```

#### 5. Suspicious Activity Report
```json
{
    "type": "suspicious_activity",
    "activity": "tab_switch|dev_tools|copy_paste",
    "details": "Additional context",
    "timestamp": "2024-01-15T14:00:00Z"
}
```

### Server → Client Messages

#### 1. Exam Started
```json
{
    "type": "exam_started",
    "exam_id": "uuid",
    "start_time": "2024-01-15T14:00:00Z",
    "duration_minutes": 90,
    "message": "Exam has started. Good luck!"
}
```

#### 2. Answer Saved Confirmation
```json
{
    "type": "answer_saved",
    "question_id": "uuid",
    "status": "success|failed",
    "timestamp": "2024-01-15T14:00:00Z"
}
```

#### 3. Time Warning
```json
{
    "type": "time_warning",
    "remaining_minutes": 10,
    "message": "10 minutes remaining",
    "timestamp": "2024-01-15T14:00:00Z"
}
```

#### 4. Exam Ended
```json
{
    "type": "exam_ended",
    "reason": "time_up|submitted|force_ended",
    "final_score": 85,
    "total_questions": 20,
    "answered_questions": 18,
    "timestamp": "2024-01-15T14:00:00Z"
}
```

#### 5. Heartbeat Response
```json
{
    "type": "heartbeat_response",
    "status": "connected",
    "server_time": "2024-01-15T14:00:00Z"
}
```

#### 6. Error Messages
```json
{
    "type": "error",
    "error_code": "EXAM_NOT_FOUND|UNAUTHORIZED|EXAM_ENDED",
    "message": "Human readable error message",
    "timestamp": "2024-01-15T14:00:00Z"
}
```

## Connection Management

### 1. Connection States
```javascript
const CONNECTION_STATES = {
    CONNECTING: 'connecting',
    CONNECTED: 'connected',
    DISCONNECTED: 'disconnected',
    RECONNECTING: 'reconnecting',
    FAILED: 'failed'
};
```

### 2. Reconnection Strategy
```javascript
class ExamWebSocket {
    constructor(examId, studentId) {
        this.examId = examId;
        this.studentId = studentId;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 2000; // Start with 2 seconds
    }
    
    connect() {
        this.socket = new WebSocket(`ws://localhost:8000/ws/exam/${this.examId}/${this.studentId}`);
        this.setupEventHandlers();
    }
    
    reconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.handleMaxReconnectAttemptsReached();
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
        
        setTimeout(() => {
            this.connect();
        }, delay);
    }
}
```

### 3. Message Queue for Offline Mode
```javascript
class MessageQueue {
    constructor() {
        this.queue = [];
        this.isOnline = false;
    }
    
    enqueue(message) {
        this.queue.push({
            ...message,
            queued_at: new Date().toISOString()
        });
        
        // Try to send if online
        if (this.isOnline) {
            this.flush();
        }
    }
    
    flush() {
        while (this.queue.length > 0 && this.isOnline) {
            const message = this.queue.shift();
            this.sendMessage(message);
        }
    }
}
```

## Data Persistence

### 1. Local Storage Schema
```javascript
const EXAM_STORAGE_KEY = `exam_${examId}_${studentId}`;

const examState = {
    exam_id: "uuid",
    student_id: "uuid",
    start_time: "2024-01-15T14:00:00Z",
    answers: {
        "question_uuid_1": {
            value: "selected_option",
            timestamp: "2024-01-15T14:00:00Z",
            saved_to_server: true
        }
    },
    time_remaining: 5400, // seconds
    is_submitted: false,
    last_activity: "2024-01-15T14:00:00Z"
};
```

### 2. State Management
```javascript
function saveExamState() {
    const state = {
        exam_id: examId,
        student_id: studentId,
        answers: Object.fromEntries(answers),
        time_remaining: remainingTime,
        is_submitted: isSubmitted,
        last_activity: new Date().toISOString()
    };
    
    localStorage.setItem(EXAM_STORAGE_KEY, JSON.stringify(state));
}

function loadExamState() {
    const saved = localStorage.getItem(EXAM_STORAGE_KEY);
    if (saved) {
        return JSON.parse(saved);
    }
    return null;
}

function clearExamState() {
    localStorage.removeItem(EXAM_STORAGE_KEY);
}
```

## Security Implementation

### 1. Anti-Cheating Measures
```javascript
class ExamSecurity {
    constructor() {
        this.violations = [];
        this.warningCount = 0;
        this.maxWarnings = 3;
    }
    
    initializeSecurity() {
        this.preventRightClick();
        this.preventKeyboardShortcuts();
        this.detectTabSwitching();
        this.preventTextSelection();
        this.detectDevTools();
    }
    
    preventRightClick() {
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.logViolation('right_click_attempt');
        });
    }
    
    preventKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Prevent F12, Ctrl+Shift+I, Ctrl+U, etc.
            const forbiddenKeys = [
                'F12',
                { ctrl: true, shift: true, key: 'I' },
                { ctrl: true, key: 'U' },
                { ctrl: true, shift: true, key: 'C' },
                { ctrl: true, key: 'A' },
                { ctrl: true, key: 'C' },
                { ctrl: true, key: 'V' }
            ];
            
            if (this.isForbiddenKey(e, forbiddenKeys)) {
                e.preventDefault();
                this.logViolation('keyboard_shortcut_attempt');
            }
        });
    }
    
    detectTabSwitching() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.logViolation('tab_switch');
                this.showWarning('Please stay on the exam page');
            }
        });
    }
    
    logViolation(type) {
        this.violations.push({
            type: type,
            timestamp: new Date().toISOString()
        });
        
        // Send to server
        if (examSocket && examSocket.readyState === WebSocket.OPEN) {
            examSocket.send(JSON.stringify({
                type: 'suspicious_activity',
                activity: type,
                timestamp: new Date().toISOString()
            }));
        }
        
        this.warningCount++;
        if (this.warningCount >= this.maxWarnings) {
            this.forceSubmitExam('security_violations');
        }
    }
}
```

### 2. Browser Detection
```javascript
function detectUnsupportedBrowser() {
    const userAgent = navigator.userAgent;
    const isSupported = 
        window.WebSocket && 
        window.localStorage && 
        window.JSON;
    
    if (!isSupported) {
        throw new Error('Browser not supported for exam session');
    }
}
```

## Performance Optimization

### 1. Message Batching
```javascript
class MessageBatcher {
    constructor(batchSize = 10, flushInterval = 5000) {
        this.batch = [];
        this.batchSize = batchSize;
        this.flushInterval = flushInterval;
        this.timer = null;
    }
    
    add(message) {
        this.batch.push(message);
        
        if (this.batch.length >= this.batchSize) {
            this.flush();
        } else if (!this.timer) {
            this.timer = setTimeout(() => this.flush(), this.flushInterval);
        }
    }
    
    flush() {
        if (this.batch.length > 0) {
            this.sendBatch(this.batch);
            this.batch = [];
        }
        
        if (this.timer) {
            clearTimeout(this.timer);
            this.timer = null;
        }
    }
}
```

### 2. Memory Management
```javascript
function cleanupExamSession() {
    // Clear timers
    if (examTimer) clearInterval(examTimer);
    if (autoSaveTimer) clearInterval(autoSaveTimer);
    if (heartbeatInterval) clearInterval(heartbeatInterval);
    
    // Close WebSocket
    if (examSocket) {
        examSocket.close();
        examSocket = null;
    }
    
    // Clear event listeners
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    document.removeEventListener('beforeunload', handleBeforeUnload);
    
    // Clear local storage
    clearExamState();
    
    // Reset variables
    answers.clear();
    violations = [];
    isSubmitted = false;
}
```

This technical specification provides the detailed implementation requirements for a robust exam session system with WebSocket communication, security measures, and proper error handling.
