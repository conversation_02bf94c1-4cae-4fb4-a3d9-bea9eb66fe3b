# Exam Submission API Update

## Overview

The exam submission API has been fully implemented according to the official API specification. The submission now includes complete exam data, questions, and student answers as required by the backend API.

## Complete API Format

### POST /exam-session/submit

**Complete Request Body (API Specification):**
```json
{
  "session_id": "session_12345_67890",
  "exam": {
    "exam_id": "exam_001",
    "title": "Mathematics Final Exam",
    "description": "Comprehensive mathematics exam covering algebra and geometry",
    "total_marks": 100,
    "total_duration": 120,
    "start_time": "2024-01-15T10:00:00Z"
  },
  "questions": [
    {
      "question_id": "q1",
      "question_text": "What is 2+2?",
      "question_type": "mcq",
      "options": {
        "A": "3",
        "B": "4",
        "C": "5",
        "D": "6"
      },
      "marks": 10
    },
    {
      "question_id": "q2",
      "question_text": "Explain the Pythagorean theorem.",
      "question_type": "long_answer",
      "marks": 20
    }
  ],
  "student_answers": [
    {
      "question_id": "q1",
      "answer": "B",
      "time_spent_seconds": 30
    },
    {
      "question_id": "q2",
      "answer": "The Pythagorean theorem states that in a right triangle, the square of the hypotenuse equals the sum of squares of the other two sides: a² + b² = c²",
      "time_spent_seconds": 300
    }
  ]
}
```

### Required Fields (API Specification)
- `session_id` - Session UUID (string, required)
- `exam` - Complete exam object (ExamData, required)
- `questions` - Array of question objects (List[QuestionData], required, minimum 1)
- `student_answers` - Array of answer objects (List[StudentAnswer], required, minimum 1)

### ExamData Fields
- `exam_id` - Unique exam identifier (string, required)
- `title` - Exam title (string, required)
- `description` - Exam description (string, optional)
- `total_marks` - Total exam marks (integer, required, ≥ 0)
- `total_duration` - Duration in minutes (integer, required, > 0)
- `start_time` - Exam start time (datetime, optional, ISO format)

### QuestionData Fields
- `question_id` - Unique question identifier (string, required)
- `question_text` - Question text (string, required, minimum 1 character)
- `question_type` - Question type: `mcq`, `short_answer`, `long_answer` (string, required)
- `options` - For MCQs only: `{"A": "option1", "B": "option2", ...}` (object, optional)
- `marks` - Question marks (integer, required, ≥ 0)

### StudentAnswer Fields
- `question_id` - Question identifier (string, required)
- `answer` - Student's answer text (string, required)
- `time_spent_seconds` - Time spent on question (integer, optional, ≥ 0)

## Implementation Changes

### 1. Redux Slice Updates (`src/store/slices/exam/examSessionSlice.js`)

**Complete Helper Functions Added:**
```javascript
// Format exam data according to API specification
const formatExamData = (exam) => {
  return {
    exam_id: exam.id || exam.exam_id,
    title: exam.title,
    description: exam.description || null,
    total_marks: exam.total_marks || 0,
    total_duration: exam.total_duration || exam.duration || 0,
    start_time: exam.start_time || exam.startTime || null
  };
};

// Format questions data according to API specification
const formatQuestionsData = (questions) => {
  return questions.map(question => ({
    question_id: question.id || question.question_id,
    question_text: question.text || question.question_text,
    question_type: mapQuestionType(question.Type || question.type),
    options: formatQuestionOptions(question),
    marks: question.marks || 1
  }));
};

// Format student answers according to API specification
const formatStudentAnswers = (studentAnswers) => {
  return Object.entries(studentAnswers).map(([questionId, answerData]) => ({
    question_id: questionId,
    answer: typeof answerData === 'string' ? answerData : (answerData.answer || answerData.text || ''),
    time_spent_seconds: typeof answerData === 'object' ? (answerData.time_spent_seconds || 0) : 0
  }));
};
```

**Updated submitExamSession Action:**
- Now accepts `exam`, `questions`, and `studentAnswers` parameters
- Validates required fields according to API specification
- Formats all data according to API schema
- Includes complete validation and error handling

### 2. Component Updates

**StudentTakeExam.jsx:**
```javascript
const result = await dispatch(submitExamSession({
  sessionId: examSession.sessionId,
  exam: currentExam,
  questions: currentExam?.questions || [],
  studentAnswers: examSession.currentAnswers || {}
})).unwrap();
```

**ExamInterface.jsx:**
```javascript
const result = await dispatch(submitExamSession({
  sessionId: examSession.sessionId,
  exam: examSession.examData,
  questions: examSession.questions || [],
  studentAnswers: submissionData.answers
})).unwrap();
```

### 3. Service Updates

**ExamSessionManager.js:**
- Updated `submitSession` method to support complete API format
- Added `exam` and `questions` parameters
- Includes complete exam data in submission payload

## Data Flow

1. **Exam Loading**: Complete exam data loaded with questions
2. **Answer Collection**: Student answers collected with timing data
3. **Validation**: Required fields validated according to API specification
4. **Format Conversion**: All data converted to exact API format
5. **Submission**: Complete payload sent to API with exam, questions, and answers
6. **Error Handling**: Comprehensive validation and error messages

## Validation Rules Implemented

### Mandatory Field Validation
- **exam**: Complete exam object is required
- **questions**: At least 1 question must be provided
- **student_answers**: At least 1 answer must be provided

### Data Integrity Validation
- All `question_id` references consistent between questions and answers
- `total_marks` and `total_duration` must be positive values
- `marks` for individual questions must be non-negative
- `time_spent_seconds` must be non-negative if provided

## Testing

Run the complete format test to verify API specification compliance:
```bash
node src/tests/exam/SubmissionFormatTest.js
```

Expected output shows complete submission payload matching API specification exactly.

## Benefits

1. **API Compliance**: Full compliance with backend API specification
2. **Complete Data**: Exam, questions, and answers all included
3. **Validation**: Comprehensive field validation and error handling
4. **Data Integrity**: Consistent question IDs and proper data types
5. **Timing Analytics**: Time spent per question tracked accurately
6. **Error Prevention**: Validates all required fields before submission

## Error Handling

The implementation includes proper error handling for:
- Missing exam object
- Empty questions array
- Empty student answers
- Invalid data types
- Network errors
- API validation errors
