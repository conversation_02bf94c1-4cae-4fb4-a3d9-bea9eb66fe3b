# EduFair Exam System - Developer Guide

## Overview

The EduFair Exam System is a comprehensive, real-time examination platform built with React, Redux, and WebSocket technology. This guide provides detailed information for developers working on the system.

## Architecture Overview

### Core Principles

1. **Real-time Communication**: WebSocket-based architecture for instant updates
2. **Security-First**: Multi-layered security with anti-cheating measures
3. **Modular Design**: Component-based architecture for maintainability
4. **Performance Optimized**: Efficient state management and rendering
5. **Future-Proof**: Extensible design for easy feature additions

### Technology Stack

- **Frontend**: React 18, Redux Toolkit, React Router
- **Real-time**: WebSocket with automatic reconnection
- **UI Components**: Custom components with Tailwind CSS
- **State Management**: Redux with async thunks
- **Testing**: Jest, React Testing Library
- **Security**: Custom anti-cheating service, browser lockdown

## Project Structure

```
src/
├── components/
│   ├── exam/
│   │   ├── student/           # Student exam components
│   │   │   ├── ExamInterface/
│   │   │   ├── QuestionDisplay/
│   │   │   ├── ExamNavigation/
│   │   │   ├── ExamTimer/
│   │   │   └── SecurityWarnings/
│   │   └── teacher/           # Teacher monitoring components
│   │       └── MonitoringDashboard/
│   └── shared/                # Shared components
│       └── ConnectionStatus/
├── services/
│   └── exam/
│       ├── websocket/         # WebSocket management
│       ├── security/          # Security services
│       ├── session/           # Session management
│       └── reconnection/      # Reconnection handling
├── store/
│   └── slices/
│       └── exam/              # Redux slices
├── hooks/
│   └── exam/                  # Custom hooks
├── tests/
│   └── exam/                  # Test suites
└── docs/                      # Documentation
```

## Core Services

### 1. ExamWebSocketService

**Location**: `src/services/exam/websocket/ExamWebSocketService.js`

**Purpose**: Manages real-time communication between client and server.

**Key Features**:
- Automatic reconnection with exponential backoff
- Message queuing during disconnections
- Heartbeat system (20-second intervals)
- Event-driven architecture

**Usage**:
```javascript
import ExamWebSocketService, { WS_EVENTS } from './ExamWebSocketService';

// Connect to exam session
await ExamWebSocketService.connect(sessionId, token);

// Listen for events
ExamWebSocketService.on(WS_EVENTS.CONNECTED, () => {
  console.log('Connected to exam session');
});

// Send messages
ExamWebSocketService.sendMessage({
  type: 'answer_update',
  data: { questionId: 'q1', answer: 'test' }
});
```

### 2. AntiCheatService

**Location**: `src/services/exam/security/AntiCheatService.js`

**Purpose**: Implements comprehensive anti-cheating measures.

**Key Features**:
- Browser lockdown (disable dev tools, right-click, shortcuts)
- Tab/window monitoring
- Fullscreen enforcement
- Behavioral analysis
- Real-time violation reporting

**Usage**:
```javascript
import AntiCheatService from './AntiCheatService';

// Activate during exam
AntiCheatService.activate();

// Listen for violations
window.addEventListener('securityViolation', (event) => {
  console.log('Security violation:', event.detail);
});

// Deactivate after exam
AntiCheatService.deactivate();
```

### 3. ExamSessionManager

**Location**: `src/services/exam/session/ExamSessionManager.js`

**Purpose**: Manages exam session lifecycle.

**Key Features**:
- Session start/pause/resume/submit
- Timer management
- Session state tracking
- Cleanup handling

**Usage**:
```javascript
import examSessionManager from './ExamSessionManager';

// Start session
const session = await examSessionManager.startSession(examId, studentData, token);

// Listen for events
examSessionManager.on('sessionExpired', ({ sessionId }) => {
  console.log('Session expired:', sessionId);
});
```

## Redux State Management

### ExamSessionSlice

**Location**: `src/store/slices/exam/examSessionSlice.js`

**State Structure**:
```javascript
{
  sessionId: string | null,
  examData: object | null,
  questions: array,
  answers: object,
  currentQuestionIndex: number,
  remainingTime: number,
  strikes: number,
  connectionStatus: 'connected' | 'disconnected' | 'reconnecting',
  status: 'not_started' | 'active' | 'paused' | 'submitted' | 'expired',
  loading: boolean,
  error: string | null,
  isSubmitting: boolean,
  isSubmitted: boolean,
  isDisqualified: boolean,
  showWarningModal: boolean,
  currentWarning: object | null
}
```

**Key Actions**:
- `startExamSession`: Initialize exam session
- `updateAnswer`: Update question answer
- `submitExam`: Submit exam for grading
- `handleWebSocketMessage`: Process WebSocket messages
- `setConnectionStatus`: Update connection status

## Component Architecture

### ExamInterface (Main Component)

**Location**: `src/components/exam/student/ExamInterface/ExamInterface.jsx`

**Purpose**: Main exam taking interface for students.

**Key Features**:
- Real-time exam state management
- WebSocket integration
- Anti-cheating integration
- Timer management
- Answer synchronization

**Props**: None (uses URL params and Redux state)

### QuestionDisplay

**Location**: `src/components/exam/student/QuestionDisplay/QuestionDisplay.jsx`

**Purpose**: Renders different question types with appropriate inputs.

**Props**:
```javascript
{
  question: object,        // Question data
  answer: string,          // Current answer
  onAnswerChange: function, // Answer change handler
  isReadOnly: boolean      // Read-only mode
}
```

**Supported Question Types**:
- Multiple Choice (MCQS)
- Descriptive (DESCRIPTIVE)

### ExamNavigation

**Location**: `src/components/exam/student/ExamNavigation/ExamNavigation.jsx`

**Purpose**: Question navigation and exam submission controls.

**Props**:
```javascript
{
  questions: array,        // All questions
  answers: object,         // Current answers
  currentIndex: number,    // Current question index
  onQuestionSelect: function, // Question selection handler
  onSubmit: function,      // Submission handler
  isSubmitting: boolean,   // Submission state
  canSubmit: boolean       // Submission permission
}
```

## Custom Hooks

### useExamSession

**Location**: `src/hooks/exam/useExamSession.js`

**Purpose**: Centralized exam session management hook.

**Usage**:
```javascript
import { useExamSession } from './useExamSession';

const ExamComponent = () => {
  const {
    examSession,
    initializeSession,
    updateQuestionAnswer,
    submitExamSession,
    handleTimerUpdate,
    cleanup
  } = useExamSession(examId);

  // Use hook methods...
};
```

## Testing

### Test Structure

Tests are organized by feature and component:

```
src/tests/
├── exam/
│   ├── ExamInterface.test.jsx
│   ├── ExamWebSocketService.test.js
│   ├── AntiCheatService.test.js
│   └── examSessionSlice.test.js
```

### Running Tests

```bash
# Run all tests
npm test

# Run specific test file
npm test ExamInterface.test.jsx

# Run tests with coverage
npm test -- --coverage
```

### Test Utilities

**Mock Services**: All external services are mocked in tests.

**Test Wrapper**: Custom wrapper provides Redux store and router context.

```javascript
const TestWrapper = ({ children, initialState = {} }) => {
  const store = createTestStore(initialState);
  return (
    <Provider store={store}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </Provider>
  );
};
```

## Security Implementation

### Multi-Layer Security

1. **Browser Lockdown**:
   - Disable developer tools
   - Prevent right-click and shortcuts
   - Force fullscreen mode
   - Disable text selection and copy/paste

2. **Behavioral Monitoring**:
   - Tab switching detection
   - Window focus monitoring
   - Mouse movement tracking
   - Keystroke pattern analysis

3. **Network Security**:
   - Monitor external requests
   - Detect network disconnections
   - Validate WebSocket messages

4. **Real-time Reporting**:
   - Instant violation reporting
   - Strike system (3 strikes = disqualification)
   - Teacher notifications

### Security Headers

The system implements comprehensive security headers:

```javascript
{
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'...",
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()...'
}
```

## Performance Optimizations

### React Optimizations

1. **Memoization**: Use `useMemo` and `useCallback` for expensive operations
2. **Component Splitting**: Break large components into smaller ones
3. **Lazy Loading**: Load components only when needed
4. **Virtual Scrolling**: For large question lists

### State Management

1. **Normalized State**: Flat state structure for better performance
2. **Selective Updates**: Update only changed parts of state
3. **Debounced Actions**: Prevent excessive API calls

### WebSocket Optimizations

1. **Message Queuing**: Queue messages during disconnections
2. **Batch Updates**: Group multiple updates together
3. **Compression**: Compress large messages
4. **Heartbeat Optimization**: Efficient connection monitoring

## API Integration

### WebSocket Messages

**Message Format**:
```javascript
{
  type: string,           // Message type
  data: object,           // Message payload
  timestamp: number,      // Unix timestamp
  sessionId: string       // Session identifier
}
```

**Message Types**:
- `heartbeat`: Keep connection alive
- `answer_update`: Update question answer
- `answer_sync`: Synchronize all answers
- `cheating_report`: Report security violation
- `session_status`: Session status update
- `time_warning`: Time remaining warning
- `disqualification`: Student disqualification

### REST API Endpoints

**Exam Management**:
- `POST /api/exams/{examId}/start-session`: Start exam session
- `POST /exam-session/submit`: Submit exam
- `GET /api/exams/{examId}/active-sessions`: Get active sessions

**Reconnection**:
- `POST /api/exams/session/{sessionId}/request-reconnection`: Request reconnection
- `POST /api/exams/reconnection/{requestId}/approve`: Approve reconnection
- `POST /api/exams/reconnection/{requestId}/deny`: Deny reconnection

## Deployment Considerations

### Environment Variables

```bash
REACT_APP_API_URL=http://localhost:5000
REACT_APP_WS_URL=ws://localhost:5000
REACT_APP_EXAM_TIMEOUT=3600
REACT_APP_MAX_RECONNECT_ATTEMPTS=5
```

### Build Optimization

```bash
# Production build
npm run build

# Analyze bundle size
npm run analyze

# Test production build locally
npm run serve
```

### Security Considerations

1. **HTTPS Only**: Always use HTTPS in production
2. **WebSocket Security**: Use WSS (WebSocket Secure)
3. **Token Management**: Secure token storage and rotation
4. **CORS Configuration**: Proper CORS setup for API calls

## Troubleshooting

### Common Issues

1. **WebSocket Connection Fails**:
   - Check network connectivity
   - Verify WebSocket URL
   - Check authentication token

2. **Anti-Cheat False Positives**:
   - Review security thresholds
   - Check browser compatibility
   - Verify fullscreen support

3. **Performance Issues**:
   - Monitor memory usage
   - Check for memory leaks
   - Optimize re-renders

### Debug Tools

1. **Redux DevTools**: Monitor state changes
2. **React DevTools**: Inspect component tree
3. **Network Tab**: Monitor WebSocket messages
4. **Console Logs**: Detailed logging throughout system

## Contributing

### Code Style

- Use ESLint and Prettier for consistent formatting
- Follow React best practices
- Write comprehensive tests for new features
- Document complex logic with comments

### Pull Request Process

1. Create feature branch from main
2. Implement changes with tests
3. Update documentation if needed
4. Submit pull request with detailed description
5. Address review feedback

### Release Process

1. Update version numbers
2. Run full test suite
3. Build production bundle
4. Deploy to staging environment
5. Perform integration testing
6. Deploy to production

## Support

For technical support or questions:

1. Check this documentation first
2. Review existing issues in the repository
3. Create detailed bug reports with reproduction steps
4. Include relevant logs and error messages

## Future Enhancements

### Planned Features

1. **AI Proctoring**: Advanced AI-based cheating detection
2. **Mobile Support**: Responsive design for tablets
3. **Offline Mode**: Limited offline functionality
4. **Analytics Dashboard**: Detailed exam analytics
5. **Plugin System**: Extensible architecture for custom features

### Technical Debt

1. **TypeScript Migration**: Gradual migration to TypeScript
2. **Component Library**: Extract reusable components
3. **Performance Monitoring**: Add performance tracking
4. **Accessibility**: Improve WCAG compliance
