# Institute API Documentation

## Overview
This document outlines the API endpoints required for the Institute Dashboard and management system. All endpoints require authentication with a Bear<PERSON> token and institute-level permissions.

## Authentication
All endpoints require the following header:
```http
Authorization: Bearer <jwt_token>
```

---

## 🏫 **Institute Dashboard APIs**

### **Get Dashboard Summary**
```http
GET /api/institute/dashboard/summary
```

**Description:** Fetch overview statistics for the institute dashboard.

**Response (200):**
```json
{
  "totalStudents": 1250,
  "totalTeachers": 85,
  "totalCompetitions": 12,
  "totalMentors": 45,
  "activeCompetitions": 3,
  "pendingApplications": 8,
  "monthlyGrowth": {
    "students": 12.5,
    "teachers": 5.2,
    "competitions": 25.0
  }
}
```

### **Get Dashboard Analytics**
```http
GET /api/institute/dashboard/analytics
```

**Description:** Fetch detailed analytics data for charts and graphs.

**Response (200):**
```json
{
  "studentPerformance": [
    {
      "month": "2024-01",
      "averageScore": 85.2,
      "participationRate": 92.5,
      "completionRate": 88.7
    }
  ],
  "competitionStats": [
    {
      "competitionId": "comp-123",
      "title": "Math Championship",
      "participants": 150,
      "completionRate": 95.2,
      "averageScore": 78.5
    }
  ],
  "mentorEffectiveness": [
    {
      "mentorId": "mentor-456",
      "name": "Dr. Smith",
      "studentsAssigned": 25,
      "averageImprovement": 15.3,
      "satisfactionRating": 4.8
    }
  ],
  "monthlyGrowth": [
    {
      "month": "2024-01",
      "newStudents": 45,
      "newTeachers": 3,
      "newCompetitions": 2
    }
  ]
}
```

### **Get Recent Activities**
```http
GET /api/institute/dashboard/recent-activities
```

**Query Parameters:**
- `limit` (optional): Number of activities to return (default: 10, max: 50)

**Response (200):**
```json
{
  "activities": [
    {
      "id": "activity-123",
      "type": "competition_created",
      "description": "New competition 'Physics Challenge' was created",
      "timestamp": "2024-01-15T10:30:00Z",
      "userId": "user-456",
      "userName": "Prof. Johnson",
      "metadata": {
        "competitionId": "comp-789",
        "competitionTitle": "Physics Challenge"
      }
    },
    {
      "id": "activity-124",
      "type": "mentor_application",
      "description": "New mentor application received from Dr. Williams",
      "timestamp": "2024-01-15T09:15:00Z",
      "userId": "user-789",
      "userName": "Dr. Williams",
      "metadata": {
        "applicationId": "app-456",
        "status": "pending"
      }
    }
  ]
}
```

---

## 👥 **Institute Teachers Management**

### **Get Institute Teachers**
```http
GET /api/institute/dashboard/teachers
```

**Query Parameters:**
- `skip` (optional): Pagination offset (default: 0)
- `limit` (optional): Items per page (default: 20, max: 100)
- `search` (optional): Search by name or email
- `status` (optional): Filter by status (`active`, `inactive`, `pending`)
- `subject` (optional): Filter by subject expertise

**Response (200):**
```json
{
  "data": [
    {
      "id": "teacher-123",
      "firstName": "John",
      "lastName": "Smith",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "status": "active",
      "joinDate": "2023-09-01T00:00:00Z",
      "subjects": ["Mathematics", "Physics"],
      "classesCount": 5,
      "studentsCount": 125,
      "averageRating": 4.7,
      "lastActive": "2024-01-15T14:30:00Z",
      "profilePicture": "https://example.com/profiles/teacher-123.jpg"
    }
  ],
  "total": 85,
  "skip": 0,
  "limit": 20,
  "hasMore": true
}
```

### **Get Teacher Details**
```http
GET /api/institute/dashboard/teachers/{teacher_id}
```

**Response (200):**
```json
{
  "id": "teacher-123",
  "firstName": "John",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "status": "active",
  "joinDate": "2023-09-01T00:00:00Z",
  "subjects": ["Mathematics", "Physics"],
  "qualifications": [
    {
      "degree": "PhD in Mathematics",
      "institution": "MIT",
      "year": 2018
    }
  ],
  "classes": [
    {
      "id": "class-456",
      "name": "Advanced Calculus",
      "studentsCount": 25,
      "schedule": "Mon, Wed, Fri 10:00-11:30"
    }
  ],
  "performance": {
    "totalStudents": 125,
    "averageRating": 4.7,
    "completionRate": 92.3,
    "satisfactionScore": 4.6
  },
  "recentActivities": [
    {
      "type": "class_conducted",
      "description": "Conducted Advanced Calculus class",
      "timestamp": "2024-01-15T10:00:00Z"
    }
  ]
}
```

---

## 🎓 **Institute Students Management**

### **Get Institute Students**
```http
GET /api/institute/dashboard/students
```

**Query Parameters:**
- `skip` (optional): Pagination offset (default: 0)
- `limit` (optional): Items per page (default: 20, max: 100)
- `search` (optional): Search by name or email
- `status` (optional): Filter by status (`active`, `inactive`, `graduated`)
- `class` (optional): Filter by class ID
- `grade` (optional): Filter by grade level

**Response (200):**
```json
{
  "data": [
    {
      "id": "student-123",
      "firstName": "Alice",
      "lastName": "Johnson",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "status": "active",
      "enrollmentDate": "2023-09-01T00:00:00Z",
      "grade": "12",
      "classes": [
        {
          "id": "class-456",
          "name": "Advanced Calculus",
          "teacher": "Prof. Smith"
        }
      ],
      "performance": {
        "gpa": 3.8,
        "attendanceRate": 95.2,
        "completedAssignments": 45,
        "totalAssignments": 48
      },
      "lastActive": "2024-01-15T16:45:00Z",
      "profilePicture": "https://example.com/profiles/student-123.jpg"
    }
  ],
  "total": 1250,
  "skip": 0,
  "limit": 20,
  "hasMore": true
}
```

---

## 🏆 **Institute Competitions Management**

### **Get Institute Competitions**
```http
GET /api/institute/dashboard/competitions
```

**Query Parameters:**
- `skip` (optional): Pagination offset (default: 0)
- `limit` (optional): Items per page (default: 20, max: 100)
- `status` (optional): Filter by status (`upcoming`, `ongoing`, `completed`, `cancelled`)
- `category` (optional): Filter by category

**Response (200):**
```json
{
  "data": [
    {
      "id": "comp-123",
      "title": "Mathematics Championship 2024",
      "description": "Annual mathematics competition for all grade levels",
      "status": "upcoming",
      "startDate": "2024-02-15T09:00:00Z",
      "endDate": "2024-02-15T17:00:00Z",
      "registrationDeadline": "2024-02-10T23:59:59Z",
      "maxParticipants": 200,
      "currentParticipants": 156,
      "category": {
        "id": "cat-456",
        "name": "Mathematics"
      },
      "prizePool": 5000.00,
      "difficultyLevel": "intermediate",
      "createdAt": "2024-01-10T10:00:00Z",
      "mentorsAssigned": 5,
      "requiredMentors": 8
    }
  ],
  "total": 12,
  "skip": 0,
  "limit": 20,
  "hasMore": false
}
```

---

## 🎯 **Institute Mentors Management**

### **Get Institute Mentors and Applications**
```http
GET /api/institute/dashboard/mentors
```

**Query Parameters:**
- `skip` (optional): Pagination offset (default: 0)
- `limit` (optional): Items per page (default: 20, max: 100)
- `status` (optional): Filter by status (`active`, `pending`, `rejected`)
- `expertise` (optional): Filter by subject expertise

**Response (200):**
```json
{
  "data": [
    {
      "id": "mentor-123",
      "firstName": "Dr. Sarah",
      "lastName": "Williams",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "status": "active",
      "joinDate": "2023-10-15T00:00:00Z",
      "expertise": ["Mathematics", "Statistics"],
      "hourlyRate": 75.00,
      "hoursPerWeek": 20,
      "studentsAssigned": 15,
      "averageRating": 4.9,
      "completedSessions": 245,
      "profilePicture": "https://example.com/profiles/mentor-123.jpg"
    }
  ],
  "applications": [
    {
      "id": "app-456",
      "applicantId": "user-789",
      "applicantName": "Dr. Michael Brown",
      "applicantEmail": "<EMAIL>",
      "status": "pending",
      "applicationDate": "2024-01-12T14:30:00Z",
      "applicationMessage": "I would like to join as a mathematics mentor...",
      "expertise": ["Advanced Mathematics", "Calculus"],
      "proposedHourlyRate": 80.00,
      "availabilityHours": 25,
      "experience": "15+ years teaching experience",
      "qualifications": [
        {
          "degree": "PhD in Mathematics",
          "institution": "Stanford University",
          "year": 2008
        }
      ]
    }
  ],
  "total": 45,
  "skip": 0,
  "limit": 20,
  "hasMore": true
}
```

---

## 🔧 **Institute Management Actions**

### **Approve/Reject Mentor Application**
```http
POST /api/institute/mentors/applications/{application_id}/respond
```

**Request Body:**
```json
{
  "response": "approved",
  "responseMessage": "Welcome to our institute! We're excited to have you join our team.",
  "finalHourlyRate": 78.00,
  "finalHoursPerWeek": 22,
  "startDate": "2024-02-01T00:00:00Z"
}
```

**Response (200):**
```json
{
  "id": "app-456",
  "status": "approved",
  "responseDate": "2024-01-15T15:30:00Z",
  "responseMessage": "Welcome to our institute!",
  "finalTerms": {
    "hourlyRate": 78.00,
    "hoursPerWeek": 22,
    "startDate": "2024-02-01T00:00:00Z"
  }
}
```

### **Invite External Mentor**
```http
POST /api/institute/mentors/invite
```

**Request Body:**
```json
{
  "mentorId": "mentor-external-123",
  "invitationMessage": "We would like to invite you to join our institute as a senior mathematics mentor.",
  "offeredHourlyRate": 85.00,
  "expectedHoursPerWeek": 30,
  "subjectsToCover": ["Advanced Mathematics", "Calculus", "Linear Algebra"],
  "startDate": "2024-02-15T00:00:00Z"
}
```

**Response (201):**
```json
{
  "id": "invitation-789",
  "mentorId": "mentor-external-123",
  "status": "sent",
  "sentDate": "2024-01-15T16:00:00Z",
  "expiryDate": "2024-01-29T23:59:59Z",
  "terms": {
    "hourlyRate": 85.00,
    "hoursPerWeek": 30,
    "subjects": ["Advanced Mathematics", "Calculus", "Linear Algebra"]
  }
}
```

---

## 📊 **Error Responses**

### **400 Bad Request**
```json
{
  "error": "validation_error",
  "message": "Invalid request parameters",
  "details": {
    "field": "limit",
    "issue": "Must be between 1 and 100"
  }
}
```

### **401 Unauthorized**
```json
{
  "error": "unauthorized",
  "message": "Invalid or expired token"
}
```

### **403 Forbidden**
```json
{
  "error": "forbidden",
  "message": "Insufficient permissions. Institute role required."
}
```

### **404 Not Found**
```json
{
  "error": "not_found",
  "message": "Resource not found"
}
```

### **500 Internal Server Error**
```json
{
  "error": "internal_error",
  "message": "An unexpected error occurred"
}
```

---

## 🔄 **Rate Limiting**

All endpoints are subject to rate limiting:
- **Dashboard endpoints**: 100 requests per minute
- **Management endpoints**: 50 requests per minute
- **Bulk operations**: 10 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642694400
```
