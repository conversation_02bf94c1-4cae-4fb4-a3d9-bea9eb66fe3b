# New Systems Documentation

This document provides comprehensive documentation for the newly implemented systems in the EduFair platform.

## Overview

The following new systems have been implemented:

1. **Enhanced Subscription System** - Improved subscription management with better API integration
2. **Events System** - Complete event management and registration system
3. **Mentors System** - Mentor discovery, registration, and management
4. **Competition System** - Competition creation and management from exams
5. **Mentor-Institute Association System** - Application and invitation system
6. **Competition Mentor Checking System** - Answer checking and scoring by mentors

## 1. Enhanced Subscription System

### Overview
The subscription system has been enhanced to better integrate with the existing API and provide improved user experience.

### Key Features
- Subscription plan management
- Payment processing integration
- Usage tracking and limits
- Automatic renewals
- Plan upgrades/downgrades

### API Endpoints Used
- `GET /api/subscriptions/plans` - Get available plans
- `POST /api/subscriptions/subscribe` - Subscribe to a plan
- `GET /api/subscriptions/current` - Get current subscription
- `POST /api/subscriptions/cancel` - Cancel subscription

### Components
- `SubscriptionPlans.jsx` - Display available plans
- `CurrentSubscription.jsx` - Show current subscription status
- `PaymentForm.jsx` - Handle payment processing

## 2. Events System

### Overview
Complete event management system allowing creation, discovery, and registration for educational events and competitions.

### Key Features
- Event creation and management
- Event discovery with search and filters
- Registration system with tickets
- Featured events
- Competition events
- Location and speaker management

### Redux Store Structure
```javascript
{
  publicEvents: [],
  featuredEvents: [],
  currentEvent: null,
  myEvents: [],
  myRegistrations: [],
  filters: {
    category_id: null,
    location_id: null,
    is_featured: null,
    is_competition: null
  }
}
```

### API Endpoints
- `GET /api/events/public` - Get public events
- `GET /api/events/featured` - Get featured events
- `POST /api/events` - Create event
- `GET /api/events/{id}` - Get event details
- `POST /api/events/{id}/register` - Register for event

### Components
- `EventsPage.jsx` - Main events listing
- `EventCard.jsx` - Event display component
- `EventDetailsPage.jsx` - Event details view
- `CreateEventPage.jsx` - Event creation form

### Custom Hook
- `useEvents.js` - Provides easy access to events functionality

## 3. Mentors System

### Overview
Comprehensive mentor management system for mentor discovery, registration, and profile management.

### Key Features
- Mentor registration and profile creation
- Public mentor discovery with search and filters
- Mentor-student matching
- Availability management
- Rating and review system
- Expertise area management

### Redux Store Structure
```javascript
{
  publicMentors: [],
  currentMentor: null,
  myProfile: null,
  assignments: [],
  answersToCheck: [],
  statistics: null,
  searchFilters: {
    expertise_areas: [],
    experience_years_min: null,
    hourly_rate_max: null,
    rating_min: null,
    verified_only: false
  }
}
```

### API Endpoints
- `GET /api/mentors/public` - Get public mentors
- `POST /api/mentors/register` - Register as mentor
- `GET /api/mentors/{id}` - Get mentor details
- `PUT /api/mentors/profile` - Update mentor profile

### Components
- `MentorsPage.jsx` - Main mentors listing
- `MentorCard.jsx` - Mentor display component
- `MentorDetailsPage.jsx` - Mentor profile view
- `MentorRegistrationPage.jsx` - Mentor registration form

### Custom Hook
- `useMentors.js` - Provides easy access to mentors functionality

## 4. Competition System

### Overview
System for creating competitions from existing exams and managing competition lifecycle.

### Key Features
- Create competitions from exams
- Competition registration and participation
- Mentor assignment for checking
- Competition status management
- Prize and difficulty management

### Redux Store Structure
```javascript
{
  competitions: [],
  filters: {
    category_id: null,
    status: null
  },
  createLoading: false,
  assignMentorLoading: false
}
```

### API Endpoints
- `POST /api/competitions/from-exam/{exam_id}` - Create competition from exam
- `GET /api/competitions` - Get competitions
- `POST /api/competitions/{id}/assign-mentor` - Assign mentor

### Components
- `CompetitionsPage.jsx` - Main competitions listing
- `CompetitionCard.jsx` - Competition display component
- `CreateCompetitionPage.jsx` - Competition creation form

## 5. Mentor-Institute Association System

### Overview
System for managing relationships between mentors and educational institutes through applications and invitations.

### Key Features
- Mentor application to institutes
- Institute invitation to mentors
- Application/invitation response system
- Association status management

### API Endpoints
- `POST /api/mentors/apply-to-institute` - Apply to institute
- `POST /api/mentors/invite` - Invite mentor
- `POST /api/mentors/associations/{id}/respond` - Respond to association

### Components
- `MentorAssociations.jsx` - Mentor-side association management
- `InstituteMentors.jsx` - Institute-side mentor management

## 6. Competition Mentor Checking System

### Overview
System for mentors to check and score competition answers with statistics tracking.

### Key Features
- Answer checking and scoring
- Bulk scoring functionality
- Mentor assignment to competitions
- Performance statistics
- Feedback system

### API Endpoints
- `GET /api/mentors/assignments` - Get mentor assignments
- `GET /api/mentors/answers-to-check` - Get answers to check
- `POST /api/mentors/submit-score` - Submit answer score
- `POST /api/mentors/bulk-score` - Submit bulk scores
- `GET /api/mentors/statistics` - Get mentor statistics

### Components
- `MentorAssignments.jsx` - Assignment management
- `AnswerChecking.jsx` - Answer checking interface
- `MentorStatistics.jsx` - Performance statistics

## Integration Points

### Dashboard Layout
All new systems are integrated into the existing dashboard layout with appropriate role-based access:

- **Students**: Events, Mentors
- **Teachers**: Events, Competitions, Mentors
- **Mentors**: Assignments, Answer Checking, Statistics, Associations
- **Admins**: Events, Mentors, Competitions, Analytics

### Sidebar Configuration
Updated sidebar configurations include new navigation items for each user role.

### Redux Store Integration
All new systems are integrated into the main Redux store with proper state management.

## Testing

### Integration Tests
Comprehensive integration tests have been created in `src/tests/integration/NewSystemsIntegration.test.js` covering:

- Component rendering
- User interactions
- Redux state management
- API integration
- Error handling
- Loading states

### Test Coverage
- Events System: Component rendering, search, filters, tab switching
- Mentors System: Profile display, search, filters, registration
- Competitions System: Competition listing, creation, mentor assignment
- Answer Checking: Score submission, bulk operations, statistics

## Usage Examples

### Using the Events System
```javascript
import { useEvents } from '../hooks/useEvents';

const MyComponent = () => {
  const {
    publicEvents,
    loading,
    loadPublicEvents,
    registerForEventAction
  } = useEvents();

  // Load events
  useEffect(() => {
    loadPublicEvents();
  }, []);

  // Register for event
  const handleRegister = async (eventId) => {
    await registerForEventAction(eventId, { ticket_id: 'general' });
  };
};
```

### Using the Mentors System
```javascript
import { useMentors } from '../hooks/useMentors';

const MyComponent = () => {
  const {
    publicMentors,
    loading,
    loadPublicMentors,
    updateFilters
  } = useMentors();

  // Filter mentors
  const handleFilter = (filters) => {
    updateFilters(filters);
    loadPublicMentors(filters);
  };
};
```

## Future Enhancements

### Planned Features
1. Real-time notifications for mentor assignments
2. Advanced analytics dashboard
3. Mobile app integration
4. Video conferencing integration for mentoring sessions
5. AI-powered mentor-student matching
6. Automated competition result processing

### Performance Optimizations
1. Implement caching for frequently accessed data
2. Add pagination for large datasets
3. Optimize image loading for mentor profiles
4. Implement lazy loading for components

## Troubleshooting

### Common Issues
1. **API Connection Issues**: Check environment variables and API endpoints
2. **Redux State Issues**: Verify store configuration and reducer integration
3. **Component Rendering Issues**: Check prop types and component dependencies
4. **Authentication Issues**: Ensure proper token handling in API calls

### Debug Mode
Enable debug mode by setting `REACT_APP_DEBUG=true` in environment variables for detailed logging.

## Conclusion

The new systems provide a comprehensive platform for educational event management, mentor-student connections, and competition management. All systems are designed to be scalable, maintainable, and user-friendly while integrating seamlessly with the existing EduFair platform.
