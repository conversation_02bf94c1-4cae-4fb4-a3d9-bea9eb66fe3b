🎯 NEW MENTOR MANAGEMENT APIs - PROPERLY SEPARATED!
I've created a comprehensive set of APIs that clearly separate Applications from Active Mentors. Here's the complete breakdown:

📋 MENTOR APPLICATIONS APIs
Prefix: /api/institute/mentors

1. 📥 Applications RECEIVED from Mentors:
✅ GET /applications/received?status=pending - Pending applications from mentors
✅ GET /applications/received?status=approved - Approved applications
✅ GET /applications/received?status=rejected - Rejected applications
✅ GET /applications/received - All received applications
2. 📊 Application Management:
✅ GET /applications/pending - Only pending applications (most common use)
✅ GET /applications/all - All applications (pending + approved + rejected)
✅ GET /applications/history - Processed applications (approved + rejected history)
3. 📤 Invitations SENT by Institute:
✅ GET /invitations/sent?status=pending - Pending invitations sent to mentors
✅ GET /invitations/sent?status=accepted - Accepted invitations
✅ GET /invitations/sent?status=rejected - Rejected invitations
✅ GET /invitations/sent - All sent invitations
👨‍🏫 ACTIVE MENTORS APIs
Prefix: /api/institute/mentors

4. ✅ Active Mentors (Working with Institute):
✅ GET /active - Only active/approved mentors (no applications)
✅ GET / - Default route - Returns active mentors only
✅ GET /{mentor_id} - Individual mentor details
✅ PUT /{mentor_id} - Update mentor details
✅ POST /{mentor_id}/activate - Activate mentor
✅ POST /{mentor_id}/deactivate - Deactivate mentor
🔄 APPLICATION PROCESSING APIs
Prefix: /api/institute/mentors

5. 📝 Application Actions:
✅ POST /applications/{app_id}/approve - Approve application
✅ POST /applications/{app_id}/reject - Reject application
✅ POST /invite - Send invitation to mentor
📈 MENTOR PERFORMANCE APIs
Prefix: /api/institute/mentors

6. 📊 Performance & Analytics:
✅ GET /{mentor_id}/performance - Individual mentor performance
✅ GET /{mentor_id}/assignments - Mentor's competition assignments
✅ POST /{mentor_id}/assign-competition - Assign mentor to competition
🎯 KEY DISTINCTIONS:
📋 Applications vs 👨‍🏫 Active Mentors
Applications	Active Mentors
GET /applications/pending	GET /active
GET /applications/received	GET / (default)
GET /invitations/sent	GET /{mentor_id}
Status: pending, rejected	Status: approved, active
Need approval	Already working
📥 Received vs 📤 Sent
Received (from mentors)	Sent (by institute)
GET /applications/received	GET /invitations/sent
Mentors applied to institute	Institute invited mentors
association_type: "mentor_applied"	association_type: "institute_invited"
