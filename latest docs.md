🎯 MENTOR MANAGEMENT APIs - COMPLETE & ACCURATE!
❌ ISSUE FIXED: The frontend was calling `/api/institute/dashboard/mentors` which doesn't exist!
✅ CORRECT ENDPOINTS: All actual API endpoints documented below.

📋 MENTOR APPLICATIONS APIs
Prefix: /api/institute/mentors

1. 📥 Application Management:
✅ GET /applications - Get mentor applications for institute
✅ GET /applications/pending - Get pending applications only
✅ GET /applications/all - Get all applications (pending + approved + rejected)
✅ GET /applications/history - Get applications history (processed applications)
✅ GET /applications/received - Get received applications from mentors

2. 📤 Invitation Management:
✅ GET /invitations - Get invitations route
✅ GET /invitations/sent - Get sent invitations route
✅ POST /invite - Invite mentor route

3. 🔄 Application Processing:
✅ POST /applications/{app_id}/approve - Approve application route
✅ POST /applications/{app_id}/reject - Reject application route

👨‍🏫 ACTIVE MENTORS APIs
Prefix: /api/institute/mentors

4. ✅ Mentor Management:
✅ GET / - Get institute mentors route (default)
✅ GET /active - Get active mentors route
✅ GET /{mentor_id} - Get mentor details route
✅ PUT /{mentor_id} - Update mentor route
✅ POST /{mentor_id}/activate - Activate mentor route
✅ POST /{mentor_id}/deactivate - Deactivate mentor route

📈 MENTOR PERFORMANCE & ASSIGNMENTS APIs
Prefix: /api/institute/mentors

5. 📊 Performance & Analytics:
✅ GET /{mentor_id}/performance - Get mentor performance route
✅ GET /{mentor_id}/assignments - Get mentor assignments route
✅ POST /{mentor_id}/assign-competition - Assign competition route

🎯 NEW MENTOR SEARCH DROPDOWN FEATURE:
✅ COMPONENT: MentorSearchDropdown.jsx
- Searchable dropdown for selecting mentors
- Real-time search with debouncing
- Displays mentor profile pictures, ratings, experience
- Shows verification status and expertise areas
- Integrates with /api/mentors/public endpoint
- Responsive design with loading states

✅ INTEGRATION: InstituteMentors.jsx
- Replaced text input with searchable dropdown
- Enhanced mentor selection experience
- Better UX for inviting mentors

✅ FRONTEND FIXES COMPLETED:
✅ FIXED: InstituteMentorsSlice.js - All API endpoints updated
✅ FIXED: TestMentorAPI.jsx - Documentation updated
✅ ADDED: New API functions for all endpoints
✅ NEW: MentorSearchDropdown component for mentor selection
✅ UPDATED: Invite Mentor form with searchable dropdown

📝 CHANGES MADE:
- ✅ fetchMentorApplications: /mentors/applications
- ✅ fetchInstituteMentors: /mentors/active
- ✅ approveMentorApplication: /mentors/applications/{id}/approve
- ✅ rejectMentorApplication: /mentors/applications/{id}/reject
- ✅ inviteMentor: /mentors/invite
- ✅ fetchMentorById: /mentors/{id}
- ✅ updateMentor: /mentors/{id}
- ✅ activateMentor: /mentors/{id}/activate
- ✅ deactivateMentor: /mentors/{id}/deactivate
- ✅ fetchMentorPerformance: /mentors/{id}/performance
- ✅ fetchMentorAssignments: /mentors/{id}/assignments
- ✅ assignCompetitionToMentor: /mentors/{id}/assign-competition
- ✅ fetchMentorInvitations: /mentors/invitations
- ✅ fetchPendingApplications: /mentors/applications/pending
- ✅ fetchAllApplications: /mentors/applications/all
- ✅ fetchApplicationsHistory: /mentors/applications/history
- ✅ fetchReceivedApplications: /mentors/applications/received
- ✅ fetchSentInvitations: /mentors/invitations/sent
🎯 KEY DISTINCTIONS:
📋 Applications vs 👨‍🏫 Active Mentors
Applications	Active Mentors
GET /applications?status=pending	GET /active
GET /applications?status=approved	GET / (default)
GET /invitations/sent	GET /{mentor_id}
Status: pending, rejected	Status: approved, active
Need approval	Already working

📥 Received vs 📤 Sent
Received (from mentors)	Sent (by institute)
GET /applications/received	GET /invitations/sent
Mentors applied to institute	Institute invited mentors
association_type: "mentor_applied"	association_type: "institute_invited"

🔧 IMPLEMENTATION NOTES:
1. All endpoints use `/api/institute/mentors` as base URL
2. Query parameters: ?skip=0&limit=20&status=pending
3. Authentication: Bearer token required
4. Response format: { data: [...], total: number, skip: number, limit: number }

📋 COMPLETE API ENDPOINT LIST:

🔍 APPLICATION ENDPOINTS:
✅ GET /api/institute/mentors/applications - Get mentor applications
✅ GET /api/institute/mentors/applications/pending - Get pending applications
✅ GET /api/institute/mentors/applications/all - Get all applications
✅ GET /api/institute/mentors/applications/history - Get applications history
✅ GET /api/institute/mentors/applications/received - Get received applications
✅ POST /api/institute/mentors/applications/{app_id}/approve - Approve application
✅ POST /api/institute/mentors/applications/{app_id}/reject - Reject application

🎯 INVITATION ENDPOINTS:
✅ GET /api/institute/mentors/invitations - Get invitations
✅ GET /api/institute/mentors/invitations/sent - Get sent invitations
✅ POST /api/institute/mentors/invite - Invite mentor

👨‍🏫 MENTOR MANAGEMENT ENDPOINTS:
✅ GET /api/institute/mentors - Get institute mentors (default)
✅ GET /api/institute/mentors/active - Get active mentors
✅ GET /api/institute/mentors/{mentor_id} - Get mentor details
✅ PUT /api/institute/mentors/{mentor_id} - Update mentor
✅ POST /api/institute/mentors/{mentor_id}/activate - Activate mentor
✅ POST /api/institute/mentors/{mentor_id}/deactivate - Deactivate mentor

📊 PERFORMANCE & ASSIGNMENT ENDPOINTS:
✅ GET /api/institute/mentors/{mentor_id}/performance - Get mentor performance
✅ GET /api/institute/mentors/{mentor_id}/assignments - Get mentor assignments
✅ POST /api/institute/mentors/{mentor_id}/assign-competition - Assign competition
