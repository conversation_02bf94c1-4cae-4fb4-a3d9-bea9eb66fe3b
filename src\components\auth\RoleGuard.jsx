import React from 'react';
import { Navigate } from 'react-router-dom';
import { getUserRole } from '../../utils/helpers/authHelpers';

/**
 * RoleGuard component for protecting routes based on user roles
 * @param {Object} props
 * @param {Array<string>} props.allowedRoles - Array of roles that can access this component
 * @param {React.ReactNode} props.children - Component to render if access is granted
 * @param {string} props.redirectTo - Path to redirect to if access is denied (optional)
 * @param {React.ReactNode} props.fallback - Component to render if access is denied (optional)
 */
const RoleGuard = ({ 
  allowedRoles = [], 
  children, 
  redirectTo = '/unauthorized', 
  fallback = null 
}) => {
  const userRole = getUserRole();

  // If no user role is found, redirect to login
  if (!userRole) {
    return <Navigate to="/Login" replace />;
  }

  // Check if user role is in allowed roles
  const hasAccess = allowedRoles.includes(userRole.toLowerCase());

  if (!hasAccess) {
    // If fallback component is provided, render it
    if (fallback) {
      return fallback;
    }
    
    // Otherwise redirect to specified path
    return <Navigate to={redirectTo} replace />;
  }

  // User has access, render children
  return children;
};

/**
 * Hook for checking user role access
 * @param {Array<string>} allowedRoles - Array of roles to check against
 * @returns {Object} - Object containing access status and user role
 */
export const useRoleAccess = (allowedRoles = []) => {
  const userRole = getUserRole();
  
  const hasAccess = userRole && allowedRoles.includes(userRole.toLowerCase());
  
  return {
    hasAccess,
    userRole: userRole?.toLowerCase(),
    isAuthenticated: !!userRole
  };
};

/**
 * Component for conditionally rendering content based on user role
 * @param {Object} props
 * @param {Array<string>} props.allowedRoles - Array of roles that can see this content
 * @param {React.ReactNode} props.children - Content to render if access is granted
 * @param {React.ReactNode} props.fallback - Content to render if access is denied (optional)
 */
export const RoleBasedContent = ({ allowedRoles = [], children, fallback = null }) => {
  const { hasAccess } = useRoleAccess(allowedRoles);
  
  if (!hasAccess) {
    return fallback;
  }
  
  return children;
};

/**
 * Higher-order component for wrapping components with role-based access control
 * @param {React.Component} Component - Component to wrap
 * @param {Array<string>} allowedRoles - Array of roles that can access this component
 * @param {Object} options - Additional options
 * @returns {React.Component} - Wrapped component with role guard
 */
export const withRoleGuard = (Component, allowedRoles = [], options = {}) => {
  const WrappedComponent = (props) => {
    return (
      <RoleGuard 
        allowedRoles={allowedRoles} 
        redirectTo={options.redirectTo}
        fallback={options.fallback}
      >
        <Component {...props} />
      </RoleGuard>
    );
  };
  
  WrappedComponent.displayName = `withRoleGuard(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

/**
 * Component for displaying role-specific messages or UI elements
 * @param {Object} props
 * @param {string} props.role - Specific role to check for
 * @param {React.ReactNode} props.children - Content to render if user has the role
 * @param {React.ReactNode} props.fallback - Content to render if user doesn't have the role
 */
export const RoleSpecificContent = ({ role, children, fallback = null }) => {
  const userRole = getUserRole();
  
  if (userRole?.toLowerCase() === role?.toLowerCase()) {
    return children;
  }
  
  return fallback;
};

/**
 * Utility function to check if user has any of the specified roles
 * @param {Array<string>} roles - Array of roles to check
 * @returns {boolean} - True if user has any of the roles
 */
export const hasAnyRole = (roles = []) => {
  const userRole = getUserRole();
  return userRole && roles.includes(userRole.toLowerCase());
};

/**
 * Utility function to check if user has a specific role
 * @param {string} role - Role to check
 * @returns {boolean} - True if user has the role
 */
export const hasRole = (role) => {
  const userRole = getUserRole();
  return userRole?.toLowerCase() === role?.toLowerCase();
};

/**
 * Component for creating role-based navigation items
 * @param {Object} props
 * @param {Array<string>} props.allowedRoles - Roles that can see this navigation item
 * @param {React.ReactNode} props.children - Navigation item content
 */
export const RoleBasedNavItem = ({ allowedRoles = [], children }) => {
  const { hasAccess } = useRoleAccess(allowedRoles);
  
  if (!hasAccess) {
    return null;
  }
  
  return children;
};

export default RoleGuard;
