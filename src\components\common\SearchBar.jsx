import { useState, useRef, useEffect, memo } from "react";
import { FiSearch, FiX } from "react-icons/fi";

const SearchBar = memo(({
  value = "",
  onChange,
  onSearch,
  onClear,
  placeholder = "Search...",
  showKeyboardShortcut = true,
  showResultsCount = false,
  resultsCount = 0,
  resultsType = "items",
  className = "",
  disabled = false,
  autoFocus = false,
  size = "default" // "small", "default", "large"
}) => {
  const [internalValue, setInternalValue] = useState(value);
  const searchInputRef = useRef(null);

  // Sync internal value with external value
  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  // Auto focus if requested
  useEffect(() => {
    if (autoFocus && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [autoFocus]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl/Cmd + K to focus search
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        if (searchInputRef.current && !disabled) {
          searchInputRef.current.focus();
        }
      }
      // ESC to clear search
      if (event.key === 'Escape' && internalValue && searchInputRef.current === document.activeElement) {
        handleClear();
      }
    };

    if (showKeyboardShortcut) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [internalValue, disabled, showKeyboardShortcut]);

  // Handle input change
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    if (onChange) {
      onChange(newValue);
    }
  };

  // Handle search action (Enter key or search button)
  const handleSearch = () => {
    if (onSearch) {
      onSearch(internalValue);
    }
  };

  // Handle clear action
  const handleClear = () => {
    setInternalValue("");
    if (onChange) {
      onChange("");
    }
    if (onClear) {
      onClear();
    }
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
  };

  // Size classes
  const getSizeClasses = () => {
    switch (size) {
      case "small":
        return "py-1.5 text-sm";
      case "large":
        return "py-3 text-lg";
      default:
        return "py-2";
    }
  };

  // Get placeholder with keyboard shortcut hint
  const getPlaceholderText = () => {
    if (showKeyboardShortcut && !disabled) {
      const shortcut = navigator.userAgent.includes('Mac') ? '⌘K' : 'Ctrl+K';
      return `${placeholder} (${shortcut})`;
    }
    return placeholder;
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-4 ${className}`}>
      {/* Header with keyboard shortcut */}
      {showKeyboardShortcut && (
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Search</h3>
          <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center space-x-1">
            <kbd className="px-1.5 py-0.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
              {navigator.userAgent.includes('Mac') ? '⌘' : 'Ctrl'}
            </kbd>
            <span>+</span>
            <kbd className="px-1.5 py-0.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600">
              K
            </kbd>
          </div>
        </div>
      )}

      {/* Search Input */}
      <div className="relative">
        <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        <input
          ref={searchInputRef}
          type="text"
          placeholder={getPlaceholderText()}
          value={internalValue}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          disabled={disabled}
          className={`w-full pl-10 pr-10 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${getSizeClasses()}`}
        />
        
        {/* Clear button */}
        {internalValue && !disabled && (
          <button
            onClick={handleClear}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
            title="Clear search (ESC)"
            type="button"
          >
            <FiX size={16} />
          </button>
        )}
      </div>

      {/* Results count */}
      {showResultsCount && internalValue && (
        <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          Found {resultsCount} {resultsCount === 1 ? resultsType.slice(0, -1) : resultsType} matching "{internalValue}"
        </div>
      )}
    </div>
  );
});

SearchBar.displayName = 'SearchBar';

export default SearchBar;
