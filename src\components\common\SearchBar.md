# SearchBar Component

A reusable, feature-rich search bar component with keyboard shortcuts, real-time search, and customizable styling.

## Features

- ✅ **Real-time Search**: Instant filtering as you type
- ✅ **Enter Key Support**: Submit search on Enter key press
- ✅ **Keyboard Shortcuts**: Ctrl/Cmd+K to focus, ESC to clear
- ✅ **Clear Button**: X button to quickly clear search
- ✅ **Results Counter**: Show number of matching results
- ✅ **Multiple Sizes**: Small, default, and large sizes
- ✅ **Auto Focus**: Automatically focus on mount
- ✅ **Disabled State**: Support for disabled state
- ✅ **Dark Mode**: Full dark theme support
- ✅ **Responsive**: Works on all screen sizes
- ✅ **Accessible**: Keyboard navigation and screen reader friendly

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `string` | `""` | Current search value |
| `onChange` | `function` | - | Called when search value changes |
| `onSearch` | `function` | - | Called when Enter is pressed or search is submitted |
| `onClear` | `function` | - | Called when search is cleared |
| `placeholder` | `string` | `"Search..."` | Placeholder text |
| `showKeyboardShortcut` | `boolean` | `true` | Show keyboard shortcut hint |
| `showResultsCount` | `boolean` | `false` | Show results count |
| `resultsCount` | `number` | `0` | Number of results found |
| `resultsType` | `string` | `"items"` | Type of results (e.g., "users", "posts") |
| `className` | `string` | `""` | Additional CSS classes |
| `disabled` | `boolean` | `false` | Disable the search input |
| `autoFocus` | `boolean` | `false` | Auto focus on mount |
| `size` | `string` | `"default"` | Size: "small", "default", "large" |

## Basic Usage

```jsx
import { SearchBar } from '../../components/common';

const MyComponent = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearchChange = (value) => {
    setSearchTerm(value);
    // Perform real-time filtering
  };

  const handleSearchSubmit = (value) => {
    // Handle search submission (Enter key)
    console.log('Search submitted:', value);
  };

  const handleSearchClear = () => {
    setSearchTerm('');
    // Handle search clear
  };

  return (
    <SearchBar
      value={searchTerm}
      onChange={handleSearchChange}
      onSearch={handleSearchSubmit}
      onClear={handleSearchClear}
      placeholder="Search users..."
    />
  );
};
```

## Advanced Usage

```jsx
<SearchBar
  value={searchTerm}
  onChange={handleSearchChange}
  onSearch={handleSearchSubmit}
  onClear={handleSearchClear}
  placeholder="Search materials..."
  showKeyboardShortcut={true}
  showResultsCount={true}
  resultsCount={filteredItems.length}
  resultsType="materials"
  size="large"
  className="mb-6"
/>
```

## Keyboard Shortcuts

- **Ctrl/Cmd + K**: Focus the search input
- **Enter**: Submit search (calls `onSearch`)
- **ESC**: Clear search (calls `onClear`)

## Styling

The component uses Tailwind CSS classes and supports dark mode automatically. You can customize the appearance by:

1. Passing additional classes via `className` prop
2. Modifying the component's internal styles
3. Using different sizes: `small`, `default`, `large`

## Integration Examples

### Material Management (Current Usage)
```jsx
<SearchBar
  value={searchTerm}
  onChange={handleSearchChange}
  onSearch={handleSearchSubmit}
  onClear={handleSearchClear}
  placeholder={getSearchPlaceholder()}
  showKeyboardShortcut={true}
  showResultsCount={true}
  resultsCount={getSearchResults().count}
  resultsType={getSearchResults().type}
  className="mb-6"
/>
```

### User Management
```jsx
<SearchBar
  value={userSearchTerm}
  onChange={setUserSearchTerm}
  onSearch={handleUserSearch}
  placeholder="Search users by name or email..."
  showResultsCount={true}
  resultsCount={filteredUsers.length}
  resultsType="users"
/>
```

### Product Search
```jsx
<SearchBar
  value={productSearch}
  onChange={setProductSearch}
  onSearch={handleProductSearch}
  placeholder="Search products..."
  size="large"
  autoFocus={true}
/>
```

## Notes

- The component is memoized for performance
- Keyboard shortcuts are automatically handled
- The component maintains its own internal state but syncs with external value
- All event handlers are optional
- The component is fully accessible and responsive
