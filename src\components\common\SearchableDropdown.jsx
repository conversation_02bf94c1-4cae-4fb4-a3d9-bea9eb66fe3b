import { useState, useEffect, useRef, useCallback } from 'react';
import { FiChevronDown, FiSearch, FiX, FiLoader, FiCheck } from 'react-icons/fi';
import { useThemeProvider } from '../../providers/ThemeContext';

/**
 * SearchableDropdown Component
 * A reusable dropdown with search functionality and auto-pagination
 * 
 * @param {Object} props
 * @param {Array} props.options - Array of options to display
 * @param {Array} props.selectedValues - Array of selected option IDs
 * @param {Function} props.onSelectionChange - Callback when selection changes
 * @param {Function} props.onSearch - Callback for search functionality
 * @param {Function} props.onLoadMore - Callback for loading more items
 * @param {boolean} props.loading - Loading state
 * @param {boolean} props.hasMore - Whether more items are available
 * @param {boolean} props.multiple - Allow multiple selection
 * @param {string} props.placeholder - Placeholder text
 * @param {string} props.searchPlaceholder - Search input placeholder
 * @param {string} props.displayKey - Key to display from option object (default: 'name')
 * @param {string} props.valueKey - Key to use as value from option object (default: 'id')
 * @param {string} props.label - Label for the dropdown
 * @param {boolean} props.disabled - Disable the dropdown
 * @param {string} props.className - Additional CSS classes
 */
const SearchableDropdown = ({
  options = [],
  selectedValues = [],
  onSelectionChange,
  onSearch,
  onLoadMore,
  loading = false,
  hasMore = false,
  multiple = false,
  placeholder = "Select an option",
  searchPlaceholder = "Search...",
  displayKey = 'name',
  valueKey = 'id',
  label,
  disabled = false,
  className = ""
}) => {
  const { currentTheme } = useThemeProvider();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchTimeout, setSearchTimeout] = useState(null);
  const dropdownRef = useRef(null);
  const listRef = useRef(null);
  const searchInputRef = useRef(null);

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300';
  const hoverBg = currentTheme === 'dark' ? 'hover:bg-gray-600' : 'hover:bg-gray-100';

  // Get selected options for display
  const selectedOptions = options.filter(option => 
    selectedValues.includes(option[valueKey])
  );

  // Handle search with debouncing
  const handleSearch = useCallback((value) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      if (onSearch) {
        onSearch(value);
      }
    }, 300); // 300ms debounce

    setSearchTimeout(timeout);
  }, [onSearch, searchTimeout]);

  // Handle search input change
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    handleSearch(value);
  };

  // Handle option selection
  const handleOptionSelect = (option) => {
    if (disabled) return;

    let newSelectedValues;
    
    if (multiple) {
      if (selectedValues.includes(option[valueKey])) {
        // Remove from selection
        newSelectedValues = selectedValues.filter(id => id !== option[valueKey]);
      } else {
        // Add to selection
        newSelectedValues = [...selectedValues, option[valueKey]];
      }
    } else {
      // Single selection
      newSelectedValues = [option[valueKey]];
      setIsOpen(false);
    }

    if (onSelectionChange) {
      onSelectionChange(newSelectedValues);
    }
  };

  // Handle removing selected item (for multiple selection)
  const handleRemoveSelected = (optionId, e) => {
    e.stopPropagation();
    if (disabled) return;

    const newSelectedValues = selectedValues.filter(id => id !== optionId);
    if (onSelectionChange) {
      onSelectionChange(newSelectedValues);
    }
  };

  // Handle scroll for pagination
  const handleScroll = useCallback((e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    
    // Load more when scrolled to bottom
    if (scrollHeight - scrollTop <= clientHeight + 50 && hasMore && !loading && onLoadMore) {
      onLoadMore();
    }
  }, [hasMore, loading, onLoadMore]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  // Display text for selected values
  const getDisplayText = () => {
    if (selectedOptions.length === 0) {
      return placeholder;
    }

    if (multiple) {
      if (selectedOptions.length === 1) {
        return selectedOptions[0][displayKey];
      }
      return `${selectedOptions.length} selected`;
    }

    return selectedOptions[0][displayKey];
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {label && (
        <label className={`block text-sm font-medium ${textPrimary} mb-2`}>
          {label}
        </label>
      )}

      {/* Dropdown Trigger */}
      <div
        onClick={() => !disabled && setIsOpen(!isOpen)}
        className={`
          relative w-full px-3 py-2 border rounded-lg cursor-pointer transition-colors
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          ${isOpen ? 'border-blue-500 ring-1 ring-blue-500' : borderColor}
          ${bgPrimary} ${textPrimary}
        `}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 flex items-center gap-2 min-h-[20px]">
            {multiple && selectedOptions.length > 0 ? (
              <div className="flex flex-wrap gap-1">
                {selectedOptions.slice(0, 3).map((option) => (
                  <span
                    key={option[valueKey]}
                    className={`inline-flex items-center gap-1 px-2 py-1 rounded text-xs ${bgSecondary} ${textPrimary}`}
                  >
                    {option[displayKey]}
                    <button
                      onClick={(e) => handleRemoveSelected(option[valueKey], e)}
                      className={`${textSecondary} hover:text-red-500`}
                    >
                      <FiX className="w-3 h-3" />
                    </button>
                  </span>
                ))}
                {selectedOptions.length > 3 && (
                  <span className={`px-2 py-1 rounded text-xs ${bgSecondary} ${textSecondary}`}>
                    +{selectedOptions.length - 3} more
                  </span>
                )}
              </div>
            ) : (
              <span className={selectedOptions.length === 0 ? textSecondary : textPrimary}>
                {getDisplayText()}
              </span>
            )}
          </div>
          
          <FiChevronDown 
            className={`w-4 h-4 transition-transform ${textSecondary} ${
              isOpen ? 'transform rotate-180' : ''
            }`} 
          />
        </div>
      </div>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className={`
          absolute z-50 w-full mt-1 border rounded-lg shadow-lg
          ${bgPrimary} ${borderColor}
        `}>
          {/* Search Input */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="relative">
              <FiSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${textSecondary}`} />
              <input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={handleSearchChange}
                placeholder={searchPlaceholder}
                className={`
                  w-full pl-10 pr-3 py-2 border rounded-md
                  ${bgSecondary} ${textPrimary} ${borderColor}
                  focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500
                `}
              />
            </div>
          </div>

          {/* Options List */}
          <div
            ref={listRef}
            onScroll={handleScroll}
            className="max-h-60 overflow-y-auto"
          >
            {options.length === 0 && !loading ? (
              <div className={`p-3 text-center ${textSecondary}`}>
                No options found
              </div>
            ) : (
              options.map((option) => {
                const isSelected = selectedValues.includes(option[valueKey]);
                
                return (
                  <div
                    key={option[valueKey]}
                    onClick={() => handleOptionSelect(option)}
                    className={`
                      flex items-center justify-between px-3 py-2 cursor-pointer transition-colors
                      ${hoverBg} ${textPrimary}
                      ${isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                    `}
                  >
                    <span>{option[displayKey]}</span>
                    {isSelected && (
                      <FiCheck className="w-4 h-4 text-blue-600" />
                    )}
                  </div>
                );
              })
            )}

            {/* Loading indicator */}
            {loading && (
              <div className={`p-3 text-center ${textSecondary}`}>
                <FiLoader className="w-4 h-4 animate-spin mx-auto mb-1" />
                <span className="text-sm">Loading...</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchableDropdown;
