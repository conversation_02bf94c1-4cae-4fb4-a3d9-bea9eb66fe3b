import React from 'react';
import {
  FiAward,
  FiCalendar,
  FiUsers,
  FiClock,
  FiExternalLink,
  FiPlay,
  FiCheckCircle,
  FiXCircle
} from 'react-icons/fi';
import { format } from 'date-fns';

const CompetitionCard = ({ 
  competition, 
  onViewDetails, 
  onJoin, 
  onStart,
  isParticipant = false,
  showActions = true,
  variant = 'default' // 'default', 'compact', 'detailed'
}) => {
  const {
    id,
    title,
    description,
    start_date,
    end_date,
    registration_deadline,
    max_participants,
    current_participants,
    status,
    category,
    prize_pool,
    difficulty_level,
    exam_id,
    created_by
  } = competition;

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'MMM dd, yyyy');
  };

  const formatTime = (dateString) => {
    return format(new Date(dateString), 'h:mm a');
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      upcoming: { color: 'bg-blue-100 text-blue-800', label: 'Upcoming', icon: FiC<PERSON> },
      ongoing: { color: 'bg-green-100 text-green-800', label: 'Ongoing', icon: FiPlay },
      completed: { color: 'bg-gray-100 text-gray-800', label: 'Completed', icon: FiCheckCircle },
      cancelled: { color: 'bg-red-100 text-red-800', label: 'Cancelled', icon: FiXCircle }
    };

    const config = statusConfig[status] || statusConfig.upcoming;
    const Icon = config.icon;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </span>
    );
  };

  const getDifficultyBadge = (level) => {
    const difficultyConfig = {
      beginner: { color: 'bg-green-100 text-green-800', label: 'Beginner' },
      intermediate: { color: 'bg-yellow-100 text-yellow-800', label: 'Intermediate' },
      advanced: { color: 'bg-red-100 text-red-800', label: 'Advanced' },
      expert: { color: 'bg-purple-100 text-purple-800', label: 'Expert' }
    };

    const config = difficultyConfig[level] || difficultyConfig.beginner;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const isRegistrationOpen = new Date() < new Date(registration_deadline);
  const isFull = current_participants >= max_participants;
  const participationPercentage = (current_participants / max_participants) * 100;

  const handleJoin = (e) => {
    e.stopPropagation();
    if (onJoin && !isParticipant && !isFull && isRegistrationOpen) {
      onJoin(competition);
    }
  };

  const handleStart = (e) => {
    e.stopPropagation();
    if (onStart && isParticipant && status === 'ongoing') {
      onStart(competition);
    }
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(competition);
    }
  };

  if (variant === 'compact') {
    return (
      <div 
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
        onClick={handleViewDetails}
      >
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <FiAward className="h-8 w-8 text-yellow-500" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              {getStatusBadge(status)}
              {difficulty_level && getDifficultyBadge(difficulty_level)}
            </div>
            <h3 className="text-sm font-semibold text-gray-900 truncate">{title}</h3>
            <div className="flex items-center text-xs text-gray-500 mt-1">
              <FiCalendar className="h-3 w-3 mr-1" />
              {formatDate(start_date)}
              <FiUsers className="h-3 w-3 ml-2 mr-1" />
              {current_participants}/{max_participants}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
      onClick={handleViewDetails}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <FiAward className="h-8 w-8 text-yellow-500" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
              {category && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 mt-1">
                  {category.name}
                </span>
              )}
            </div>
          </div>
          <div className="flex flex-col items-end space-y-2">
            {getStatusBadge(status)}
            {difficulty_level && getDifficultyBadge(difficulty_level)}
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">{description}</p>

        {/* Competition Details */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="space-y-2">
            <div className="flex items-center text-sm text-gray-500">
              <FiCalendar className="h-4 w-4 mr-2" />
              <div>
                <p className="font-medium">Start Date</p>
                <p>{formatDate(start_date)} at {formatTime(start_date)}</p>
              </div>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <FiClock className="h-4 w-4 mr-2" />
              <div>
                <p className="font-medium">Registration Deadline</p>
                <p>{formatDate(registration_deadline)}</p>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center text-sm text-gray-500">
              <FiUsers className="h-4 w-4 mr-2" />
              <div>
                <p className="font-medium">Participants</p>
                <p>{current_participants} / {max_participants}</p>
              </div>
            </div>
            {prize_pool && (
              <div className="flex items-center text-sm text-gray-500">
                <FiAward className="h-4 w-4 mr-2" />
                <div>
                  <p className="font-medium">Prize Pool</p>
                  <p>${prize_pool}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Participation Progress */}
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span>Participation</span>
            <span>{Math.round(participationPercentage)}% full</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${
                participationPercentage >= 90 ? 'bg-red-500' : 
                participationPercentage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min(participationPercentage, 100)}%` }}
            />
          </div>
        </div>

        {/* Organizer */}
        {created_by && (
          <div className="mb-4 text-sm text-gray-500">
            <span>Organized by: </span>
            <span className="font-medium text-gray-700">{created_by.full_name}</span>
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <button
              onClick={handleViewDetails}
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              View Details
              <FiExternalLink className="h-4 w-4 ml-1" />
            </button>
            
            <div className="flex items-center space-x-2">
              {/* Join Competition */}
              {!isParticipant && isRegistrationOpen && !isFull && status === 'upcoming' && (
                <button
                  onClick={handleJoin}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Join Competition
                </button>
              )}

              {/* Start Competition */}
              {isParticipant && status === 'ongoing' && (
                <button
                  onClick={handleStart}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <FiPlay className="h-4 w-4 mr-2" />
                  Start
                </button>
              )}

              {/* Already Joined */}
              {isParticipant && status === 'upcoming' && (
                <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-green-800 bg-green-100 rounded-md">
                  <FiCheckCircle className="h-4 w-4 mr-2" />
                  Joined
                </span>
              )}

              {/* Registration Closed */}
              {!isRegistrationOpen && !isParticipant && (
                <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-gray-100 rounded-md">
                  Registration Closed
                </span>
              )}

              {/* Competition Full */}
              {isFull && !isParticipant && isRegistrationOpen && (
                <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-red-800 bg-red-100 rounded-md">
                  Full
                </span>
              )}

              {/* Completed */}
              {status === 'completed' && (
                <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-gray-100 rounded-md">
                  Completed
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CompetitionCard;
