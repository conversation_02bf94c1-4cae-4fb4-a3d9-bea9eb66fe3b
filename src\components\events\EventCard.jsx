import React from 'react';
import {
  FiCalendar,
  FiMapPin,
  FiUsers,
  FiClock,
  FiStar,
  FiAward,
  FiExternalLink
} from 'react-icons/fi';
import { format } from 'date-fns';

const EventCard = ({ 
  event, 
  onViewDetails, 
  onRegister, 
  isRegistered = false,
  showActions = true,
  variant = 'default' // 'default', 'featured', 'compact'
}) => {
  const {
    id,
    title,
    short_description,
    start_datetime,
    end_datetime,
    banner_image_url,
    is_featured,
    is_competition,
    current_attendees,
    max_attendees,
    category,
    location,
    registration_end
  } = event;

  const isRegistrationOpen = new Date() < new Date(registration_end);
  const isFull = current_attendees >= max_attendees;
  const attendancePercentage = (current_attendees / max_attendees) * 100;

  const formatDate = (dateString) => {
    return format(new Date(dateString), 'MMM dd, yyyy');
  };

  const formatTime = (dateString) => {
    return format(new Date(dateString), 'h:mm a');
  };

  const handleRegister = (e) => {
    e.stopPropagation();
    if (onRegister && !isRegistered && !isFull && isRegistrationOpen) {
      onRegister(event);
    }
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(event);
    }
  };

  if (variant === 'compact') {
    return (
      <div 
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
        onClick={handleViewDetails}
      >
        <div className="flex items-start space-x-3">
          {banner_image_url && (
            <img
              src={banner_image_url}
              alt={title}
              className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
            />
          )}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              {is_featured && (
                <FiStar className="h-4 w-4 text-yellow-500 fill-current" />
              )}
              {is_competition && (
                <FiAward className="h-4 w-4 text-purple-500" />
              )}
              {category && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  {category.name}
                </span>
              )}
            </div>
            <h3 className="text-sm font-semibold text-gray-900 truncate">{title}</h3>
            <div className="flex items-center text-xs text-gray-500 mt-1">
              <FiCalendar className="h-3 w-3 mr-1" />
              {formatDate(start_datetime)}
              <FiClock className="h-3 w-3 ml-2 mr-1" />
              {formatTime(start_datetime)}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer ${
        variant === 'featured' ? 'ring-2 ring-yellow-400' : ''
      }`}
      onClick={handleViewDetails}
    >
      {/* Event Image */}
      {banner_image_url && (
        <div className="relative h-48 bg-gray-200">
          <img
            src={banner_image_url}
            alt={title}
            className="w-full h-full object-cover"
          />
          {is_featured && (
            <div className="absolute top-2 left-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                <FiStar className="h-3 w-3 mr-1 fill-current" />
                Featured
              </span>
            </div>
          )}
          {is_competition && (
            <div className="absolute top-2 right-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                <FiAward className="h-3 w-3 mr-1" />
                Competition
              </span>
            </div>
          )}
        </div>
      )}

      <div className="p-6">
        {/* Category */}
        {category && (
          <div className="mb-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {category.name}
            </span>
          </div>
        )}

        {/* Title and Description */}
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">{short_description}</p>

        {/* Event Details */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-500">
            <FiCalendar className="h-4 w-4 mr-2" />
            {formatDate(start_datetime)} - {formatDate(end_datetime)}
          </div>
          <div className="flex items-center text-sm text-gray-500">
            <FiClock className="h-4 w-4 mr-2" />
            {formatTime(start_datetime)} - {formatTime(end_datetime)}
          </div>
          {location && (
            <div className="flex items-center text-sm text-gray-500">
              <FiMapPin className="h-4 w-4 mr-2" />
              {location.name}
            </div>
          )}
        </div>

        {/* Attendance */}
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span className="flex items-center">
              <FiUsers className="h-4 w-4 mr-1" />
              {current_attendees} / {max_attendees} attendees
            </span>
            <span>{Math.round(attendancePercentage)}% full</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${
                attendancePercentage >= 90 ? 'bg-red-500' : 
                attendancePercentage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min(attendancePercentage, 100)}%` }}
            />
          </div>
        </div>

        {/* Actions */}
        {showActions && (
          <div className="flex items-center justify-between">
            <button
              onClick={handleViewDetails}
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              View Details
              <FiExternalLink className="h-4 w-4 ml-1" />
            </button>
            
            {!isRegistered && isRegistrationOpen && !isFull && (
              <button
                onClick={handleRegister}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Register
              </button>
            )}
            
            {isRegistered && (
              <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-green-800 bg-green-100 rounded-md">
                Registered
              </span>
            )}
            
            {!isRegistrationOpen && (
              <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-gray-100 rounded-md">
                Registration Closed
              </span>
            )}
            
            {isFull && !isRegistered && (
              <span className="inline-flex items-center px-3 py-2 text-sm font-medium text-red-800 bg-red-100 rounded-md">
                Full
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default EventCard;
