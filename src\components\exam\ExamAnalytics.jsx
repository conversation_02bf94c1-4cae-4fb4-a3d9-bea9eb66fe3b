import React from 'react';
import {
  <PERSON><PERSON><PERSON>dingUp,
  FiTarget,
  Fi<PERSON>sers,
  <PERSON>Award,
  FiBarChart2,
  FiPieChart,
  FiTrendingDown,
  FiActivity
} from 'react-icons/fi';

/**
 * Comprehensive exam analytics component showing detailed statistics
 * Based on the new comprehensive exam details API
 */
const ExamAnalytics = ({ 
  statistics, 
  loading = false, 
  className = "",
  showTopPerformers = true,
  showDetailedStats = true 
}) => {
  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
          <div className="h-40 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (!statistics) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="text-center py-8">
          <FiBarChart2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">No statistics available</p>
        </div>
      </div>
    );
  }

  const {
    total_assigned = 0,
    total_attempts = 0,
    completed_attempts = 0,
    completion_rate = 0,
    marks_statistics = {},
    top_performers = []
  } = statistics;

  const {
    total_submissions = 0,
    highest_marks = 0,
    lowest_marks = 0,
    average_marks = 0,
    standard_deviation = 0
  } = marks_statistics;

  // Calculate additional metrics
  const attemptRate = total_assigned > 0 ? Math.round((total_attempts / total_assigned) * 100) : 0;
  const submissionRate = total_attempts > 0 ? Math.round((total_submissions / total_attempts) * 100) : 0;

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
        <div className="flex items-center gap-2">
          <FiBarChart2 className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Exam Analytics
          </h3>
        </div>
      </div>

      <div className="p-6">
        {/* Overview Stats Grid */}
        <div className="grid grid-cols-2 gap-3 mb-6">
          {/* Completion Rate */}
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <FiTrendingUp className="w-5 h-5 text-blue-600 mx-auto mb-1" />
            <div className="text-xl font-bold text-blue-600 dark:text-blue-400">
              {completion_rate}%
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Completion Rate</div>
            <div className="text-xs text-gray-500 dark:text-gray-500">
              {completed_attempts} of {total_assigned}
            </div>
          </div>

          {/* Average Score */}
          <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <FiTarget className="w-5 h-5 text-green-600 mx-auto mb-1" />
            <div className="text-xl font-bold text-green-600 dark:text-green-400">
              {average_marks.toFixed(1)}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Average Score</div>
            <div className="text-xs text-gray-500 dark:text-gray-500">
              Range: {lowest_marks} - {highest_marks}
            </div>
          </div>

          {/* Total Attempts */}
          <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <FiUsers className="w-5 h-5 text-purple-600 mx-auto mb-1" />
            <div className="text-xl font-bold text-purple-600 dark:text-purple-400">
              {total_attempts}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Total Attempts</div>
            <div className="text-xs text-gray-500 dark:text-gray-500">
              {attemptRate}% attempt rate
            </div>
          </div>

          {/* Standard Deviation */}
          <div className="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
            <FiActivity className="w-5 h-5 text-orange-600 mx-auto mb-1" />
            <div className="text-xl font-bold text-orange-600 dark:text-orange-400">
              {standard_deviation.toFixed(1)}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Std Deviation</div>
            <div className="text-xs text-gray-500 dark:text-gray-500">
              Score spread
            </div>
          </div>
        </div>

        {/* Detailed Statistics */}
        {showDetailedStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Performance Distribution */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
                <FiPieChart className="w-4 h-4" />
                Performance Overview
              </h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Assigned Students</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{total_assigned}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Started Attempts</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{total_attempts}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Completed</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{completed_attempts}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Submissions</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{total_submissions}</span>
                </div>
              </div>
            </div>

            {/* Score Statistics */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
                <FiBarChart2 className="w-4 h-4" />
                Score Analysis
              </h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Highest Score</span>
                  <span className="font-medium text-green-600 dark:text-green-400">{highest_marks}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Lowest Score</span>
                  <span className="font-medium text-red-600 dark:text-red-400">{lowest_marks}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Average Score</span>
                  <span className="font-medium text-blue-600 dark:text-blue-400">{average_marks.toFixed(1)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Score Range</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{highest_marks - lowest_marks}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Overall Progress</span>
            <span className="text-sm text-gray-600 dark:text-gray-400">{completion_rate}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500"
              style={{ width: `${completion_rate}%` }}
            ></div>
          </div>
        </div>

        {/* Top Performers */}
        {showTopPerformers && top_performers && top_performers.length > 0 && (
          <div>
            <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
              <FiAward className="w-4 h-4 text-yellow-600" />
              Top Performers
            </h4>
            <div className="space-y-2">
              {top_performers.slice(0, 5).map((performer, index) => (
                <div 
                  key={performer.student_id} 
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <span className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-semibold ${
                      index === 0 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      index === 1 ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' :
                      index === 2 ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                      'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                    }`}>
                      {index + 1}
                    </span>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {performer.student_name}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {performer.student_email}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-gray-900 dark:text-gray-100">
                      {performer.total_score}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(performer.completed_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExamAnalytics;
