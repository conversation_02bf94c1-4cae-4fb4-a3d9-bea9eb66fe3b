import { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  assignExam,
  unassignExam,
  getExamAssignments,
  clearAssignmentError,
  clearAssignmentSuccess,
  fetchAvailableStudents
} from '../../store/slices/ExamSlice';
import {
  fetchAllOwnClasses
} from '../../store/slices/ClassroomSlice';
import {
  FiX,
  FiUser,
  FiUsers,
  FiUserPlus,
  FiUserMinus,
  FiRefreshCw,
  FiSearch,
  FiLoader,
  FiAlertCircle,
  FiCheckCircle
} from 'react-icons/fi';

const ExamAssignmentModal = ({ examId, isOpen, onClose, onSuccess }) => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();

  // Redux state
  const {
    assignments,
    assignmentOperation,
    availableStudents,
    studentsLoading
  } = useSelector((state) => state.exams);

  const {
    classrooms,
    loading: classroomLoading
  } = useSelector((state) => state.classroom);

  // Local state
  const [action, setAction] = useState('add');
  const [assignmentType, setAssignmentType] = useState('students');
  const [selectedStudents, setSelectedStudents] = useState([]);
  const [selectedClassrooms, setSelectedClassrooms] = useState([]);
  const [studentSearch, setStudentSearch] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Theme classes
  const themeClasses = useMemo(() => ({
    bg: currentTheme === "dark" ? "bg-gray-900" : "bg-white",
    text: currentTheme === "dark" ? "text-gray-100" : "text-gray-900",
    border: currentTheme === "dark" ? "border-gray-700" : "border-gray-300",
    input: currentTheme === "dark"
      ? "bg-gray-800 border-gray-700 text-gray-100"
      : "bg-white border-gray-300 text-gray-900"
  }), [currentTheme]);

  // Get current assignments
  const currentAssignments = assignments?.data;

  // Safe access to arrays with defaults - use availableStudents from exam API
  const safeStudents = availableStudents || [];
  const safeClassrooms = classrooms || [];

  // Filter students based on search
  const filteredStudents = useMemo(() => {
    if (!safeStudents || !Array.isArray(safeStudents)) return [];
    return safeStudents.filter(student => {
      const name = student.name || student.username || `${student.first_name || ''} ${student.last_name || ''}`.trim();
      const email = student.email || '';
      const searchTerm = studentSearch.toLowerCase();
      return name.toLowerCase().includes(searchTerm) || email.toLowerCase().includes(searchTerm);
    });
  }, [safeStudents, studentSearch]);

  // Check if form can be submitted
  const canSubmit = useMemo(() => {
    if (assignmentType === 'students') {
      return selectedStudents.length > 0;
    } else {
      return selectedClassrooms.length > 0;
    }
  }, [assignmentType, selectedStudents, selectedClassrooms]);

  // Get submit button text
  const getSubmitButtonText = () => {
    const count = assignmentType === 'students' ? selectedStudents.length : selectedClassrooms.length;
    const type = assignmentType === 'students' ? 'student' : 'classroom';
    const plural = count !== 1 ? 's' : '';

    switch (action) {
      case 'add':
        return `Add ${count} ${type}${plural}`;
      case 'replace':
        return `Replace with ${count} ${type}${plural}`;
      case 'remove':
        return `Remove ${count} ${type}${plural}`;
      default:
        return 'Submit';
    }
  };

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setAction('add');
      setAssignmentType('students');
      setSelectedStudents([]);
      setSelectedClassrooms([]);
      setStudentSearch('');
      setError('');
      dispatch(clearAssignmentError());
      dispatch(clearAssignmentSuccess());

      // Fetch current assignments
      if (examId) {
        dispatch(getExamAssignments(examId));
      }

      // Fetch available students with assignment status and classrooms
      dispatch(fetchAvailableStudents(examId));
      dispatch(fetchAllOwnClasses());
    }
  }, [isOpen, examId, dispatch]);

  // Pre-select already assigned students/classrooms when assignment data is loaded
  useEffect(() => {
    if (currentAssignments && isOpen) {
      // Pre-select assigned students
      if (currentAssignments.students && Array.isArray(currentAssignments.students)) {
        setSelectedStudents(currentAssignments.students.map(student => student.id || student));
      }

      // Pre-select assigned classrooms
      if (currentAssignments.classrooms && Array.isArray(currentAssignments.classrooms)) {
        setSelectedClassrooms(currentAssignments.classrooms.map(classroom => classroom.id || classroom));
      }
    }
  }, [currentAssignments, isOpen]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const assignmentData = {
        action,
        assignment_type: assignmentType,
        [assignmentType]: assignmentType === 'students' ? selectedStudents : selectedClassrooms
      };

      if (action === 'remove') {
        await dispatch(unassignExam({ examId, assignmentData })).unwrap();
      } else {
        await dispatch(assignExam({ examId, assignmentData })).unwrap();
      }

      // Success - notify parent and close modal
      const count = assignmentType === 'students' ? selectedStudents.length : selectedClassrooms.length;
      const type = assignmentType === 'students' ? 'student' : 'classroom';
      const plural = count !== 1 ? 's' : '';

      onSuccess(`Successfully ${action === 'add' ? 'added' : action === 'replace' ? 'replaced' : 'removed'} ${count} ${type}${plural}`);
      onClose();
    } catch (err) {
      setError(err.message || 'Failed to update assignments');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`${themeClasses.bg} rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className={`text-xl font-semibold ${themeClasses.text}`}>
            Manage Exam Assignments
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Error Display */}
          {(error || assignmentOperation.error) && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-center gap-2">
                <FiAlertCircle className="w-4 h-4 text-red-600" />
                <p className="text-red-700 dark:text-red-400 text-sm">
                  {error || assignmentOperation.error}
                </p>
              </div>
            </div>
          )}

          {/* Success Display */}
          {assignmentOperation.success && (
            <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <div className="flex items-center gap-2">
                <FiCheckCircle className="w-4 h-4 text-green-600" />
                <p className="text-green-700 dark:text-green-400 text-sm">
                  {assignmentOperation.success}
                </p>
              </div>
            </div>
          )}

          {/* Loading State */}
          {(assignments.loading || classroomLoading || studentsLoading) && (
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="flex items-center gap-2">
                <FiLoader className="w-4 h-4 animate-spin text-blue-600" />
                <p className="text-gray-600 dark:text-gray-400 text-sm">Loading assignment data...</p>
              </div>
            </div>
          )}

          {/* Current Assignment Info */}
          {currentAssignments && !assignments.loading && (
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Current Assignments</h3>
              <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <p>Students: {currentAssignments.student_count || 0}</p>
                <p>Classrooms: {currentAssignments.classroom_count || 0}</p>
                {selectedStudents.length > 0 && (
                  <p className="text-green-700 dark:text-green-400 font-medium">
                    {selectedStudents.length} student(s) pre-selected
                  </p>
                )}
                {selectedClassrooms.length > 0 && (
                  <p className="text-green-700 dark:text-green-400 font-medium">
                    {selectedClassrooms.length} classroom(s) pre-selected
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Action Selection */}
          <div className="mb-6">
            <label className={`block mb-3 font-medium ${themeClasses.text}`}>
              Assignment Action
            </label>
            <div className="grid grid-cols-3 gap-3">
              {[
                { value: 'add', label: 'Add to existing', icon: FiUserPlus, color: 'green' },
                { value: 'replace', label: 'Replace all', icon: FiRefreshCw, color: 'blue' },
                { value: 'remove', label: 'Remove selected', icon: FiUserMinus, color: 'red' }
              ].map(({ value, label, icon: Icon, color }) => (
                <label
                  key={value}
                  className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                    action === value
                      ? `border-${color}-500 bg-${color}-50 dark:bg-${color}-900/20`
                      : `${themeClasses.border} hover:border-${color}-300`
                  }`}
                >
                  <input
                    type="radio"
                    name="action"
                    value={value}
                    checked={action === value}
                    onChange={(e) => setAction(e.target.value)}
                    className="sr-only"
                  />
                  <Icon className={`w-4 h-4 mr-2 text-${color}-600`} />
                  <span className={`text-sm font-medium ${themeClasses.text}`}>
                    {label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Assignment Type Selection */}
          <div className="mb-6">
            <label className={`block mb-3 font-medium ${themeClasses.text}`}>
              Assignment Type
            </label>
            <div className="grid grid-cols-2 gap-3">
              {[
                { value: 'students', label: 'Individual Students', icon: FiUser },
                { value: 'classrooms', label: 'Entire Classrooms', icon: FiUsers }
              ].map(({ value, label, icon: Icon }) => (
                <label
                  key={value}
                  className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                    assignmentType === value
                      ? 'border-violet-500 bg-violet-50 dark:bg-violet-900/20'
                      : `${themeClasses.border} hover:border-violet-300`
                  }`}
                >
                  <input
                    type="radio"
                    name="assignmentType"
                    value={value}
                    checked={assignmentType === value}
                    onChange={(e) => setAssignmentType(e.target.value)}
                    className="sr-only"
                  />
                  <Icon className="w-4 h-4 mr-2 text-violet-600" />
                  <span className={`text-sm font-medium ${themeClasses.text}`}>
                    {label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Student Selection */}
          {assignmentType === 'students' && (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <label className={`font-medium ${themeClasses.text}`}>
                  Select Students
                </label>
                <div className="flex items-center gap-2">
                  <button
                    type="button"
                    onClick={() => setSelectedStudents(safeStudents.map(s => s.id))}
                    className="text-sm text-blue-600 hover:text-blue-700"
                  >
                    Select All
                  </button>
                  <span className="text-gray-400">|</span>
                  <button
                    type="button"
                    onClick={() => setSelectedStudents([])}
                    className="text-sm text-gray-600 hover:text-gray-700"
                  >
                    Clear All
                  </button>
                </div>
              </div>

              {/* Student Search */}
              <div className="mb-4">
                <div className="relative">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search students..."
                    value={studentSearch}
                    onChange={(e) => setStudentSearch(e.target.value)}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${themeClasses.input}`}
                  />
                </div>
              </div>

              {/* Students List */}
              <div className="max-h-64 overflow-y-auto border rounded-lg">
                {(classroomLoading || studentsLoading) ? (
                  <div className="p-4 text-center text-gray-500">
                    <FiLoader className="w-4 h-4 animate-spin mx-auto mb-2" />
                    Loading students...
                  </div>
                ) : filteredStudents.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    {studentSearch ? 'No students found matching your search.' : 'No students available.'}
                  </div>
                ) : (
                  <div className="divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredStudents.map((student) => {
                      const isCurrentlyAssigned = currentAssignments?.students?.some(
                        assignedStudent => (assignedStudent.id || assignedStudent) === student.id
                      );
                      const isSelected = selectedStudents.includes(student.id);

                      return (
                        <label
                          key={student.id}
                          className={`flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors ${
                            isSelected ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500' : ''
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedStudents([...selectedStudents, student.id]);
                              } else {
                                setSelectedStudents(selectedStudents.filter(id => id !== student.id));
                              }
                            }}
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <div className="flex-1">
                            <div className={`font-medium ${themeClasses.text}`}>
                              {student.name || student.username || `${student.first_name || ''} ${student.last_name || ''}`.trim()}
                            </div>
                            <div className="text-sm text-gray-500">
                              {student.email}
                            </div>
                            {/* Assignment Status Badge */}
                            {student.is_assigned && (
                              <div className="mt-1">
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                  <FiCheckCircle className="w-3 h-3 mr-1" />
                                  Already Assigned
                                </span>
                              </div>
                            )}
                          </div>
                          {isCurrentlyAssigned && (
                            <span className="ml-2 px-2 py-1 text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 rounded-full">
                              Currently Assigned
                            </span>
                          )}
                        </label>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Classroom Selection */}
          {assignmentType === 'classrooms' && (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <label className={`font-medium ${themeClasses.text}`}>
                  Select Classrooms
                </label>
                <div className="flex items-center gap-2">
                  <button
                    type="button"
                    onClick={() => setSelectedClassrooms(safeClassrooms.map(c => c.id))}
                    className="text-sm text-blue-600 hover:text-blue-700"
                  >
                    Select All
                  </button>
                  <span className="text-gray-400">|</span>
                  <button
                    type="button"
                    onClick={() => setSelectedClassrooms([])}
                    className="text-sm text-gray-600 hover:text-gray-700"
                  >
                    Clear All
                  </button>
                </div>
              </div>

              <div className="max-h-64 overflow-y-auto border rounded-lg">
                {classroomLoading ? (
                  <div className="p-4 text-center text-gray-500">
                    <FiLoader className="w-4 h-4 animate-spin mx-auto mb-2" />
                    Loading classrooms...
                  </div>
                ) : safeClassrooms.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    No classrooms available.
                  </div>
                ) : (
                  <div className="divide-y divide-gray-200 dark:divide-gray-700">
                    {safeClassrooms.map((classroom) => {
                      const isCurrentlyAssigned = currentAssignments?.classrooms?.some(
                        assignedClassroom => (assignedClassroom.id || assignedClassroom) === classroom.id
                      );
                      const isSelected = selectedClassrooms.includes(classroom.id);

                      return (
                        <label
                          key={classroom.id}
                          className={`flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors ${
                            isSelected ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500' : ''
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedClassrooms([...selectedClassrooms, classroom.id]);
                              } else {
                                setSelectedClassrooms(selectedClassrooms.filter(id => id !== classroom.id));
                              }
                            }}
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <div className="flex-1">
                            <div className={`font-medium ${themeClasses.text}`}>
                              {classroom.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {classroom.student_count || 0} students
                            </div>
                          </div>
                          {isCurrentlyAssigned && (
                            <span className="ml-2 px-2 py-1 text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 rounded-full">
                              Currently Assigned
                            </span>
                          )}
                        </label>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-500">
              {assignmentType === 'students' ? (
                <span>{selectedStudents.length} student(s) selected</span>
              ) : (
                <span>{selectedClassrooms.length} classroom(s) selected</span>
              )}
            </div>
            <div className="flex items-center gap-3">
              <button
                type="button"
                onClick={onClose}
                disabled={loading || assignmentOperation.loading}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || assignmentOperation.loading || !canSubmit || classroomLoading || studentsLoading}
                className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                  loading || assignmentOperation.loading || !canSubmit || classroomLoading || studentsLoading
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : action === 'remove'
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {(loading || assignmentOperation.loading) && <FiLoader className="w-4 h-4 animate-spin" />}
                {loading || assignmentOperation.loading ? 'Processing...' : getSubmitButtonText()}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ExamAssignmentModal;