import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  FiUsers, 
  FiUser, 
  FiSettings, 
  FiRefreshCw,
  FiInfo,
  FiEdit3
} from 'react-icons/fi';
import { getExamAssignments } from '../../store/slices/ExamSlice';
import ExamAssignmentModal from './ExamAssignmentModal';

const ExamAssignmentStatus = ({ examId, onAssignmentUpdate }) => {
  const dispatch = useDispatch();
  
  // Redux state
  const { assignments } = useSelector(state => state.exams);
  
  // Local state
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);



  // Fetch assignments when component mounts
  useEffect(() => {
    if (examId) {
      dispatch(getExamAssignments(examId));
    }
  }, [examId, dispatch]);

  // Handle refresh
  const handleRefresh = () => {
    if (examId) {
      dispatch(getExamAssignments(examId));
    }
  };

  // Handle assignment modal success
  const handleAssignmentSuccess = (message) => {
    // Refresh assignments
    dispatch(getExamAssignments(examId));
    // Notify parent component
    onAssignmentUpdate?.(message);
  };

  // Get assignment data
  const assignmentData = assignments.data;
  const assignedStudentIds = assignmentData?.assigned_student_ids || [];
  const assignedStudents = assignmentData?.students || [];
  const assignedClassrooms = assignmentData?.classrooms || [];
  const studentCount = assignmentData?.student_count || assignedStudentIds.length || assignedStudents.length;
  const classroomCount = assignmentData?.classroom_count || assignedClassrooms.length;
  const totalAssigned = studentCount + classroomCount;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FiUsers className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Assignment Status
            </h3>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={handleRefresh}
              disabled={assignments.loading}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 transition-colors duration-200"
              title="Refresh assignment status"
            >
              <FiRefreshCw className={`w-4 h-4 text-gray-600 dark:text-gray-400 ${assignments.loading ? 'animate-spin' : ''}`} />
            </button>

            <button
              onClick={() => setShowAssignmentModal(true)}
              className="px-3 py-2 rounded-lg bg-blue-600 hover:bg-blue-700 text-white transition-colors duration-200 flex items-center gap-2"
            >
              <FiEdit3 className="w-4 h-4" />
              Manage
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">

        {/* Loading State */}
        {assignments.loading && (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-gray-600 dark:text-gray-400">Loading assignment status...</span>
            </div>
          </div>
        )}

        {/* Error State */}
        {assignments.error && !assignments.loading && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-center gap-2">
              <FiInfo className="w-4 h-4 text-red-600" />
              <p className="text-red-700 dark:text-red-400">
                Failed to load assignment status: {assignments.error}
              </p>
            </div>
          </div>
        )}

        {/* Assignment Data */}
        {assignmentData && !assignments.loading && !assignments.error && (
          <div className="space-y-4">
            {/* Summary Stats */}
            <div className="grid grid-cols-3 gap-3">
              <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <FiUser className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                <div className="text-xl font-bold text-blue-600 dark:text-blue-400">
                  {studentCount}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">Students</div>
                <div className="text-xs text-gray-500 dark:text-gray-500">
                  {studentCount === 1 ? 'student assigned' : 'students assigned'}
                </div>
              </div>

              <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <FiUsers className="w-5 h-5 text-purple-600 mx-auto mb-1" />
                <div className="text-xl font-bold text-purple-600 dark:text-purple-400">
                  {classroomCount}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">Classrooms</div>
                <div className="text-xs text-gray-500 dark:text-gray-500">
                  {classroomCount === 1 ? 'classroom assigned' : 'classrooms assigned'}
              </div>
              </div>

              <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <FiSettings className="w-5 h-5 text-green-600 mx-auto mb-1" />
                <div className="text-xl font-bold text-green-600 dark:text-green-400">
                  {totalAssigned > 0 ? 'Active' : 'Inactive'}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">Status</div>
                <div className="text-xs text-gray-500 dark:text-gray-500">
                  {totalAssigned > 0 ? 'Exam is assigned' : 'No assignments yet'}
                </div>
              </div>
          </div>

          {/* Assignment Details */}
          {totalAssigned > 0 && (
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
                <FiInfo className="w-4 h-4" />
                Assignment Details
              </h4>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Exam ID:</span>
                  <span className="text-sm font-mono text-gray-900 dark:text-gray-100 truncate max-w-[120px]" title={assignmentData.exam_id}>
                    {assignmentData.exam_id}
                  </span>
                </div>

                {studentCount > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Individual Students:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {studentCount} {studentCount === 1 ? 'student' : 'students'}
                    </span>
                  </div>
                )}

                {classroomCount > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Classrooms:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {classroomCount} {classroomCount === 1 ? 'classroom' : 'classrooms'}
                    </span>
                  </div>
                )}

                <div className="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-600">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total Assignments:</span>
                  <span className="text-sm font-bold text-blue-600 dark:text-blue-400">
                    {totalAssigned}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Quick Actions */}
          {totalAssigned === 0 && (
            <div className="p-6 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-center bg-gray-50 dark:bg-gray-800/50">
              <FiUsers className="w-8 h-8 mx-auto mb-3 text-gray-400" />
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                This exam hasn't been assigned to any students or classrooms yet.
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-4">
                Assign individual students or entire classrooms to make this exam available for taking.
              </p>
              <button
                onClick={() => setShowAssignmentModal(true)}
                className="px-4 py-2 rounded-lg bg-blue-600 hover:bg-blue-700 text-white transition-colors duration-200 font-medium"
              >
                Assign Students & Classrooms
              </button>
            </div>
          )}
        </div>
      )}

      </div>

      {/* Assignment Modal */}
      <ExamAssignmentModal
        examId={examId}
        isOpen={showAssignmentModal}
        onClose={() => setShowAssignmentModal(false)}
        onSuccess={handleAssignmentSuccess}
      />
    </div>
  );
};

export default ExamAssignmentStatus;
