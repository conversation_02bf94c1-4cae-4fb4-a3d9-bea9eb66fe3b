import { FiBookOpen, FiEdit3, FiTarget, FiCalendar, FiClock, FiGlobe } from 'react-icons/fi';
import { DateTimeInput } from '../ui/FormComponents';
import useTimezone from '../../hooks/useTimezone';

const ExamDetailsForm = ({
  exam,
  onExamChange,
  classrooms,
  subjects,
  classes,
  classId,
  subjectId,
  classNumber,
  assignmentType,
  onClassChange,
  onSubjectChange,
  onClassNumberChange,
  themeClasses,
  examDetailsValid
}) => {
  const { timezoneData, loading: timezoneLoading } = useTimezone();


  return (
    <div className="space-y-6">
      {/* Basic Exam Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
            <FiBookOpen className="w-4 h-4" />
            Exam Title <span className="text-red-500">*</span>
          </label>
          <input
            key="exam-title-input"
            type="text"
            name="title"
            value={exam.title}
            onChange={onExamChange}
            className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
            placeholder="Enter exam title (e.g., Mathematics Mid-term Exam)"
            required
          />
        </div>

        <div className="md:col-span-2">
          <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
            <FiEdit3 className="w-4 h-4" />
            Description
          </label>
          <textarea
            key="exam-description-input"
            name="description"
            value={exam.description}
            onChange={onExamChange}
            rows={3}
            className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
            placeholder="Provide exam instructions and description..."
          />
        </div>
      </div>

      {/* Class, Subject, and Class Number Selection */}
      <div className={`grid grid-cols-1 gap-6 ${assignmentType === 'classroom' ? 'md:grid-cols-3' : 'md:grid-cols-2'}`}>
        {/* Only show Classroom field when assigning to entire classroom */}
        {assignmentType === 'classroom' && (
          <div>
            <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
              <FiTarget className="w-4 h-4" />
              Classroom <span className="text-red-500">*</span>
            </label>
            <select
              value={classId}
              onChange={onClassChange}
              className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
              required
            >
              <option value="">Select Classroom</option>
              {classrooms && classrooms.map(cls => (
                <option key={cls.id} value={cls.id}>{cls.name}</option>
              ))}
            </select>
          </div>
        )}

        <div>
          <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
            <FiBookOpen className="w-4 h-4" />
            Subject <span className="text-red-500">*</span>
          </label>
          <select
            value={subjectId}
            onChange={onSubjectChange}
            className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
            required
          >
            <option value="">Select Subject</option>
            {subjects && subjects.map(subject => (
              <option key={subject.id} value={subject.id}>{subject.name}</option>
            ))}
          </select>
        </div>

        {/* Class Number field - always visible and required */}
        <div>
          <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
            <FiTarget className="w-4 h-4" />
            Class Number <span className="text-red-500">*</span>
          </label>
          <select
            value={classNumber}
            onChange={onClassNumberChange}
            className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
            required
          >
            <option value="">Select Class Number</option>
            {classes && classes.map(cls => (
              <option key={cls.id} value={cls.ClassNo}>
                {cls.ClassNo}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Timing Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
            <FiCalendar className="w-4 h-4" />
            Start Date & Time <span className="text-red-500">*</span>
          </label>
          <DateTimeInput
            name="start_time"
            value={exam.start_time}
            onChange={onExamChange}
            className="w-full"
            inputClassName={`rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
            required
          />
          {/* Timezone Information */}
          <div className="mt-2 text-sm text-gray-500 dark:text-gray-400 flex items-center gap-2">
            <FiGlobe className="w-4 h-4" />
            {timezoneLoading ? (
              <span>🌍 Detecting your location...</span>
            ) : timezoneData && timezoneData.detected ? (
              <span>Your timezone: {timezoneData.city}, {timezoneData.country} ({timezoneData.timezone})</span>
            ) : (
              <span>Your timezone: {timezoneData?.timezone || 'Unknown'}</span>
            )}
          </div>
          <div className="mt-1 text-xs text-blue-600 dark:text-blue-400">
            💡 Time will be automatically converted for students in different timezones
          </div>
        </div>

        <div>
          <label className={`block mb-2 font-medium ${themeClasses.label} flex items-center gap-2`}>
            <FiClock className="w-4 h-4" />
            Duration (minutes) <span className="text-red-500">*</span>
          </label>
          <input
            key="exam-duration-input"
            type="number"
            name="total_duration"
            value={exam.total_duration}
            onChange={onExamChange}
            min={1}
            max={480}
            className={`w-full rounded-lg px-4 py-3 border-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200 ${themeClasses.input}`}
            placeholder="e.g., 60"
            required
          />
        </div>
      </div>

      {/* Validation Status */}
      {examDetailsValid && (
        <div className="flex items-center gap-2 text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
          <FiBookOpen className="w-4 h-4" />
          <span className="text-sm font-medium">Exam details are complete!</span>
        </div>
      )}
    </div>
  );
};

export default ExamDetailsForm;
