import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { updateExam } from '../../store/slices/ExamSlice';
import { FiSave, FiX, FiLoader, FiGlobe } from 'react-icons/fi';
import { CustomDateTimePicker } from '../ui/CustomDateTimePicker';
import useTimezone from '../../hooks/useTimezone';

/**
 * Quick update form for basic exam information
 * Uses the simple PUT /api/exams/{id} endpoint
 */
const ExamQuickUpdateForm = ({ exam, onClose, onSuccess }) => {
  const dispatch = useDispatch();
  const { timezoneData, loading: timezoneLoading } = useTimezone();
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateError, setUpdateError] = useState(null);
  const [updateSuccess, setUpdateSuccess] = useState(false);

  const [formData, setFormData] = useState({
    title: exam?.title || '',
    description: exam?.description || '',
    total_duration: exam?.total_duration || 60,
    start_time: exam?.start_time || '' // CustomDateTimePicker expects ISO string
  });

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsUpdating(true);
    setUpdateError(null);
    setUpdateSuccess(false);

    try {
      // Prepare update data with proper datetime handling
      const updateData = {};

      // Only include fields that have values
      if (formData.title.trim()) {
        updateData.title = formData.title.trim();
      }

      if (formData.description.trim()) {
        updateData.description = formData.description.trim();
      }

      if (formData.total_duration) {
        updateData.total_duration = parseInt(formData.total_duration);
      }

      if (formData.start_time) {
        // formData.start_time is already an ISO string from CustomDateTimePicker
        updateData.start_time = formData.start_time;
        console.log('📅 Using datetime:', {
          isoString: updateData.start_time
        });
      }

      console.log('📤 Sending update data:', updateData);

      await dispatch(updateExam({
        id: exam.id,
        examData: updateData
      })).unwrap();

      setUpdateSuccess(true);

      if (onSuccess) {
        onSuccess();
      }

      // Auto-close after success
      setTimeout(() => {
        if (onClose) onClose();
      }, 1500);

    } catch (error) {
      console.error('❌ Failed to update exam:', error);
      setUpdateError(error.message || 'Failed to update exam');
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Quick Update Exam
          </h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Title
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              required
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
            />
          </div>

          {/* Duration */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Duration (minutes)
            </label>
            <input
              type="number"
              min="1"
              value={formData.total_duration}
              onChange={(e) => handleInputChange('total_duration', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              required
            />
          </div>

          {/* Start Time */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Time
            </label>
            <CustomDateTimePicker
              value={formData.start_time}
              onChange={(e) => handleInputChange('start_time', e.target.value)}
              placeholder="Select exam start date and time"
              className="w-full"
              inputClassName="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
            />

            {/* Timezone Information */}
            <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center gap-2">
              <FiGlobe className="w-3 h-3" />
              {timezoneLoading ? (
                <span>🌍 Detecting your location...</span>
              ) : timezoneData && timezoneData.detected ? (
                <span>Your timezone: {timezoneData.city}, {timezoneData.country}</span>
              ) : (
                <span>Your timezone: {timezoneData?.timezone || 'Unknown'}</span>
              )}
            </div>
            <div className="mt-1 text-xs text-blue-600 dark:text-blue-400">
              💡 Time will be automatically converted for students in different timezones
            </div>
          </div>

          {/* Error Message */}
          {updateError && (
            <div className="p-3 bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded-md">
              <p className="text-sm text-red-700 dark:text-red-300">{updateError}</p>
            </div>
          )}

          {/* Success Message */}
          {updateSuccess && (
            <div className="p-3 bg-green-100 dark:bg-green-900 border border-green-300 dark:border-green-700 rounded-md">
              <p className="text-sm text-green-700 dark:text-green-300">Exam updated successfully!</p>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isUpdating}
              className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isUpdating ? (
                <>
                  <FiLoader className="w-4 h-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <FiSave className="w-4 h-4" />
                  Update
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ExamQuickUpdateForm;
