import { useState, useEffect } from 'react';
import { FiX, FiClock, FiCalendar, FiSave, FiAlertCircle, FiInfo, FiGlobe } from 'react-icons/fi';
import { CustomDateTimePicker } from '../ui/CustomDateTimePicker';
import useTimezone from '../../hooks/useTimezone';

/**
 * Simplified Edit Modal for teachers
 * Only allows editing of duration and start time
 * Does NOT allow editing questions or assignments
 */
const LimitedExamEditModal = ({ exam, isOpen, onClose, onSave }) => {
  const { timezoneData, loading: timezoneLoading } = useTimezone();

  // Form state - simplified to only duration and start time
  const [formData, setFormData] = useState({
    total_duration: '',
    start_time: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [hasChanges, setHasChanges] = useState(false);



  // Initialize form data when exam changes
  useEffect(() => {
    if (exam) {
      // CustomDateTimePicker expects ISO string, not datetime-local format
      const startTime = exam.start_time || '';

      const initialData = {
        total_duration: exam.total_duration || '',
        start_time: startTime
      };

      setFormData(initialData);
      setErrors({});
      setHasChanges(false);
    }
  }, [exam]);

  // Validation function
  const validateForm = () => {
    const newErrors = {};

    if (!formData.total_duration || formData.total_duration < 1) {
      newErrors.total_duration = 'Duration must be at least 1 minute';
    }

    if (!formData.start_time) {
      newErrors.start_time = 'Start time is required';
    } else {
      try {
        const startDate = new Date(formData.start_time);
        const now = new Date();
        const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

        if (isNaN(startDate.getTime())) {
          newErrors.start_time = 'Invalid start time format';
        } else if (startDate < now) {
          newErrors.start_time = 'Start time cannot be in the past';
        } else if (startDate < fiveMinutesFromNow) {
          newErrors.start_time = 'Start time should be at least 5 minutes from now to allow student preparation';
        }
      } catch (error) {
        newErrors.start_time = 'Invalid start time';
      }
    }



    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);

    // Clear specific field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Build update data - only duration and start time
      const updateData = {};

      // Only include fields that have changed
      if (formData.total_duration && parseInt(formData.total_duration) !== exam.total_duration) {
        updateData.total_duration = parseInt(formData.total_duration);
      }

      if (formData.start_time) {
        // formData.start_time is already an ISO string from CustomDateTimePicker
        const newStartTime = formData.start_time;
        const currentStartTime = exam.start_time || null;
        if (newStartTime !== currentStartTime) {
          updateData.start_time = newStartTime;
          console.log('📅 Start time changed:', {
            current: currentStartTime,
            new: newStartTime
          });
        }
      }

      console.log('📤 Sending simplified exam update:', updateData);
      await onSave(updateData);
      setHasChanges(false);
      onClose();
    } catch (error) {
      console.error('❌ Error updating exam:', error);
      setErrors({ submit: 'Failed to update exam. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        if (hasChanges && !isSubmitting) {
          handleSubmit(e);
        }
      }
      if (e.key === 'Escape' && !isSubmitting) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, hasChanges, isSubmitting, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-3xl w-full max-h-[95vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <FiCalendar className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Edit Exam Timing
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {exam?.title || 'Exam Configuration'} - Duration & Start Time
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white/50 dark:hover:bg-gray-600 rounded-lg transition-colors"
            disabled={isSubmitting}
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Error Display */}
            {errors.submit && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4 flex items-center gap-3">
                <FiAlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0" />
                <p className="text-sm text-red-800 dark:text-red-200">{errors.submit}</p>
              </div>
            )}

            {/* Duration */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                <FiClock className="w-4 h-4 inline mr-2" />
                Exam Duration
              </label>
              <div className="relative">
                <input
                  type="number"
                  min="1"
                  max="600"
                  step="5"
                  value={formData.total_duration}
                  onChange={(e) => handleInputChange('total_duration', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100 transition-colors ${
                    errors.total_duration
                      ? 'border-red-300 dark:border-red-600'
                      : 'border-gray-300 dark:border-gray-600'
                  }`}
                  placeholder="Enter duration in minutes"
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <span className="text-sm text-gray-500 dark:text-gray-400">minutes</span>
                </div>
              </div>

              {/* Duration Quick Select */}
              <div className="flex flex-wrap gap-2">
                <span className="text-xs text-gray-500 dark:text-gray-400">Quick select:</span>
                {[30, 60, 90, 120, 180].map((duration) => (
                  <button
                    key={duration}
                    type="button"
                    onClick={() => handleInputChange('total_duration', duration.toString())}
                    className={`px-2 py-1 text-xs rounded border transition-colors ${
                      parseInt(formData.total_duration) === duration
                        ? 'bg-blue-100 border-blue-300 text-blue-700 dark:bg-blue-900 dark:border-blue-600 dark:text-blue-300'
                        : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-600'
                    }`}
                  >
                    {duration}m
                  </button>
                ))}
              </div>
              {errors.total_duration && (
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center gap-1">
                  <FiAlertCircle className="w-3 h-3" />
                  {errors.total_duration}
                </p>
              )}
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Recommended: 60-180 minutes for most exams
              </p>
            </div>

            {/* Start Time */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                <FiCalendar className="w-4 h-4 inline mr-2" />
                Exam Start Time
              </label>
              <CustomDateTimePicker
                value={formData.start_time}
                onChange={(e) => handleInputChange('start_time', e.target.value)}
                placeholder="Select exam start date and time"
                className="w-full"
                error={errors.start_time}
                required
              />
              {errors.start_time && (
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center gap-1">
                  <FiAlertCircle className="w-3 h-3" />
                  {errors.start_time}
                </p>
              )}
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Students will be able to start the exam at this time
              </p>

              {/* Timezone Information */}
              <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <FiGlobe className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                    Timezone Information
                  </span>
                </div>
                <div className="space-y-1 text-xs">
                  {timezoneLoading ? (
                    <p className="text-blue-700 dark:text-blue-300">🌍 Detecting your location...</p>
                  ) : timezoneData && timezoneData.detected ? (
                    <>
                      <p className="text-blue-700 dark:text-blue-300">
                        <strong>Your Location:</strong> {timezoneData.city}, {timezoneData.country}
                      </p>
                      <p className="text-blue-600 dark:text-blue-400">
                        💡 Time will be automatically converted for students in different timezones
                      </p>
                    </>
                  ) : (
                    <p className="text-blue-600 dark:text-blue-400">
                      💡 Using browser timezone. Time will be converted for students.
                    </p>
                  )}
                </div>
              </div>
            </div>





          {/* Changes Preview */}
          {hasChanges && (
            <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 animate-pulse"></div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-amber-900 dark:text-amber-100 mb-2">
                    Pending Changes
                  </h4>
                  <div className="space-y-1 text-sm text-amber-800 dark:text-amber-200">
                    {formData.total_duration && parseInt(formData.total_duration) !== exam?.total_duration && (
                      <div>• Duration: {formData.total_duration} minutes</div>
                    )}
                    {formData.start_time && (
                      <div>• Start time: {new Date(formData.start_time).toLocaleString()}</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Information Panel */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <FiInfo className="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                  Teacher Editing Permissions
                </h4>
                <div className="space-y-2">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    You can modify exam duration and start time only.
                    Questions and student assignments are managed separately.
                  </p>
                  <div className="text-xs text-blue-700 dark:text-blue-300 flex items-center gap-4">
                    <span>💡 Tip: Press <kbd className="px-1 py-0.5 bg-blue-200 dark:bg-blue-800 rounded text-xs">Ctrl+S</kbd> to save</span>
                    <span>Press <kbd className="px-1 py-0.5 bg-blue-200 dark:bg-blue-800 rounded text-xs">Esc</kbd> to cancel</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              {hasChanges && (
                <div className="flex items-center gap-2 text-sm text-amber-600 dark:text-amber-400">
                  <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                  Unsaved changes
                </div>
              )}
            </div>

            <div className="flex gap-3">
              <button
                type="button"
                onClick={onClose}
                disabled={isSubmitting}
                className="px-6 py-2.5 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 rounded-lg transition-colors disabled:opacity-50 font-medium"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting || !hasChanges}
                className="px-6 py-2.5 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Saving Changes...
                  </>
                ) : (
                  <>
                    <FiSave className="w-4 h-4" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
};

export default LimitedExamEditModal;
