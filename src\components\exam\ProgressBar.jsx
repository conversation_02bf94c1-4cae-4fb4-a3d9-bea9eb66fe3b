import React from 'react';

const ProgressBar = ({ progress, totalMarks, questionsCount }) => {
  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
      <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">
        Exam Progress
      </h3>
      
      <div className="space-y-4">
        {/* Progress Bar */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Overall Progress
            </span>
            <span className="text-sm font-medium text-violet-600 dark:text-violet-400">
              {Math.round(progress)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-violet-600 to-purple-600 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-violet-50 dark:bg-violet-900/20 p-4 rounded-lg">
            <div className="text-2xl font-bold text-violet-600 dark:text-violet-400">
              {questionsCount}
            </div>
            <div className="text-sm text-violet-700 dark:text-violet-300">
              Questions Added
            </div>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {totalMarks}
            </div>
            <div className="text-sm text-green-700 dark:text-green-300">
              Total Marks
            </div>
          </div>
        </div>

        {/* Progress Indicators */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">Questions</span>
            <span className={`font-medium ${questionsCount > 0 ? 'text-green-600 dark:text-green-400' : 'text-gray-500'}`}>
              {questionsCount > 0 ? '✓ Added' : 'Pending'}
            </span>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">Total Marks</span>
            <span className={`font-medium ${totalMarks > 0 ? 'text-green-600 dark:text-green-400' : 'text-gray-500'}`}>
              {totalMarks > 0 ? '✓ Set' : 'Pending'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressBar;
