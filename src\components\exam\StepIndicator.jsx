import React from 'react';
import { FiFileText, FiPlus, FiCheck } from 'react-icons/fi';

const StepIndicator = ({ currentStep, examDetailsValid, questionsCount }) => {
  const steps = [
    {
      number: 1,
      title: "Exam Details",
      description: "Basic information about the exam",
      icon: FiFileText,
      isComplete: examDetailsValid,
      isActive: currentStep === 1
    },
    {
      number: 2,
      title: "Add Questions",
      description: "Create or generate exam questions",
      icon: FiPlus,
      isComplete: questionsCount > 0,
      isActive: currentStep === 2
    }
  ];

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const IconComponent = step.icon;
          
          return (
            <React.Fragment key={step.number}>
              <div className="flex flex-col items-center">
                <div className={`
                  w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-200
                  ${step.isComplete
                    ? "bg-green-600 border-green-600 text-white"
                    : step.isActive
                    ? "bg-blue-600 border-blue-600 text-white"
                    : "bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500"
                  }
                `}>
                  {step.isComplete ? (
                    <FiCheck className="w-5 h-5" />
                  ) : (
                    <IconComponent className="w-5 h-5" />
                  )}
                </div>
                
                <div className="mt-3 text-center">
                  <h3 className={`text-sm font-medium ${
                    step.isActive || step.isComplete
                      ? "text-gray-900 dark:text-gray-100"
                      : "text-gray-500 dark:text-gray-400"
                  }`}>
                    {step.title}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 max-w-24">
                    {step.description}
                  </p>
                </div>
              </div>
              
              {index < steps.length - 1 && (
                <div className={`flex-1 h-0.5 mx-4 transition-all duration-200 ${
                  step.isComplete 
                    ? "bg-green-600" 
                    : "bg-gray-300 dark:bg-gray-600"
                }`} />
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default StepIndicator;
