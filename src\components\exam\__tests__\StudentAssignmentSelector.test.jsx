import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import StudentAssignmentSelector from '../StudentAssignmentSelector';
import classroomReducer from '../../../store/slices/ClassroomSlice';

// Mock theme classes
const mockThemeClasses = {
  bg: 'bg-white',
  text: 'text-gray-900',
  input: 'bg-gray-50 text-gray-900 border-gray-300',
  label: 'text-gray-700'
};

// Mock store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      classroom: classroomReducer
    },
    preloadedState: {
      classroom: {
        teacherStudents: [
          { id: 1, username: 'student1', email: '<EMAIL>' },
          { id: 2, username: 'student2', email: '<EMAIL>' },
          { id: 3, username: 'student3', email: '<EMAIL>' }
        ],
        loading: false,
        ...initialState.classroom
      }
    }
  });
};

// Mock props
const defaultProps = {
  assignmentType: 'classroom',
  onAssignmentTypeChange: jest.fn(),
  selectedStudentIds: [],
  onSelectedStudentsChange: jest.fn(),
  classId: 'class-1',
  themeClasses: mockThemeClasses,
  disabled: false
};

describe('StudentAssignmentSelector', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders assignment type options', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <StudentAssignmentSelector {...defaultProps} />
      </Provider>
    );

    expect(screen.getByText('Assignment Type')).toBeInTheDocument();
    expect(screen.getByText('Assign to Entire Class')).toBeInTheDocument();
    expect(screen.getByText('Assign to Specific Students')).toBeInTheDocument();
  });

  test('classroom assignment is selected by default', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <StudentAssignmentSelector {...defaultProps} />
      </Provider>
    );

    const classroomRadio = screen.getByDisplayValue('classroom');
    const studentsRadio = screen.getByDisplayValue('students');
    
    expect(classroomRadio).toBeChecked();
    expect(studentsRadio).not.toBeChecked();
  });

  test('shows student list when students assignment is selected', async () => {
    const store = createMockStore();
    const mockOnAssignmentTypeChange = jest.fn();
    
    render(
      <Provider store={store}>
        <StudentAssignmentSelector 
          {...defaultProps} 
          assignmentType="students"
          onAssignmentTypeChange={mockOnAssignmentTypeChange}
        />
      </Provider>
    );

    expect(screen.getByText('Select Students (0 selected)')).toBeInTheDocument();
    expect(screen.getByText('student1')).toBeInTheDocument();
    expect(screen.getByText('student2')).toBeInTheDocument();
    expect(screen.getByText('student3')).toBeInTheDocument();
  });

  test('calls onAssignmentTypeChange when assignment type changes', () => {
    const store = createMockStore();
    const mockOnAssignmentTypeChange = jest.fn();
    
    render(
      <Provider store={store}>
        <StudentAssignmentSelector 
          {...defaultProps} 
          onAssignmentTypeChange={mockOnAssignmentTypeChange}
        />
      </Provider>
    );

    const studentsRadio = screen.getByDisplayValue('students');
    fireEvent.click(studentsRadio);
    
    expect(mockOnAssignmentTypeChange).toHaveBeenCalledWith('students');
  });

  test('allows selecting and deselecting students', async () => {
    const store = createMockStore();
    const mockOnSelectedStudentsChange = jest.fn();
    
    render(
      <Provider store={store}>
        <StudentAssignmentSelector 
          {...defaultProps} 
          assignmentType="students"
          onSelectedStudentsChange={mockOnSelectedStudentsChange}
        />
      </Provider>
    );

    const student1Checkbox = screen.getByRole('checkbox', { name: /student1/ });
    fireEvent.click(student1Checkbox);
    
    expect(mockOnSelectedStudentsChange).toHaveBeenCalledWith([1]);
  });

  test('shows validation message when no students selected', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <StudentAssignmentSelector 
          {...defaultProps} 
          assignmentType="students"
          selectedStudentIds={[]}
        />
      </Provider>
    );

    expect(screen.getByText('Please select at least one student for the exam assignment.')).toBeInTheDocument();
  });

  test('filters students based on search term', async () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <StudentAssignmentSelector 
          {...defaultProps} 
          assignmentType="students"
        />
      </Provider>
    );

    const searchInput = screen.getByPlaceholderText('Search students by name or email...');
    fireEvent.change(searchInput, { target: { value: 'student1' } });

    await waitFor(() => {
      expect(screen.getByText('student1')).toBeInTheDocument();
      expect(screen.queryByText('student2')).not.toBeInTheDocument();
      expect(screen.queryByText('student3')).not.toBeInTheDocument();
    });
  });

  test('select all functionality works correctly', () => {
    const store = createMockStore();
    const mockOnSelectedStudentsChange = jest.fn();
    
    render(
      <Provider store={store}>
        <StudentAssignmentSelector 
          {...defaultProps} 
          assignmentType="students"
          onSelectedStudentsChange={mockOnSelectedStudentsChange}
        />
      </Provider>
    );

    const selectAllButton = screen.getByText('Select All');
    fireEvent.click(selectAllButton);
    
    expect(mockOnSelectedStudentsChange).toHaveBeenCalledWith([1, 2, 3]);
  });

  test('component is disabled when disabled prop is true', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <StudentAssignmentSelector 
          {...defaultProps} 
          disabled={true}
        />
      </Provider>
    );

    const classroomRadio = screen.getByDisplayValue('classroom');
    const studentsRadio = screen.getByDisplayValue('students');
    
    expect(classroomRadio).toBeDisabled();
    expect(studentsRadio).toBeDisabled();
  });
});
