/**
 * Exam Navigation Component
 * Question navigation and exam submission controls
 */

import React from 'react';
import { 
  FiChevronLeft, 
  FiChevronRight, 
  FiSend, 
  FiCheckCircle, 
  FiCircle,
  FiFlag,
  FiClock
} from 'react-icons/fi';

const ExamNavigation = ({ 
  questions, 
  answers, 
  currentIndex, 
  onQuestionSelect, 
  onSubmit, 
  isSubmitting,
  canSubmit = true 
}) => {
  // Calculate statistics
  const totalQuestions = questions.length;
  const answeredCount = Object.keys(answers).length;
  const unansweredCount = totalQuestions - answeredCount;
  const progress = totalQuestions > 0 ? (answeredCount / totalQuestions) * 100 : 0;

  // Get question status
  const getQuestionStatus = (question, index) => {
    const isAnswered = answers[question.id] !== undefined && answers[question.id] !== '';
    const isCurrent = index === currentIndex;
    
    return {
      isAnswered,
      isCurrent,
      isUnanswered: !isAnswered
    };
  };

  // Get question button styling
  const getQuestionButtonClass = (status) => {
    const baseClass = "w-10 h-10 rounded-lg border-2 flex items-center justify-center text-sm font-medium transition-all duration-200 hover:scale-105";
    
    if (status.isCurrent && status.isAnswered) {
      return `${baseClass} bg-blue-600 border-blue-600 text-white shadow-lg`;
    } else if (status.isCurrent) {
      return `${baseClass} bg-blue-100 border-blue-500 text-blue-700 shadow-lg`;
    } else if (status.isAnswered) {
      return `${baseClass} bg-green-100 border-green-500 text-green-700 hover:bg-green-200`;
    } else {
      return `${baseClass} bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200`;
    }
  };

  // Navigation handlers
  const goToPrevious = () => {
    if (currentIndex > 0) {
      onQuestionSelect(currentIndex - 1);
    }
  };

  const goToNext = () => {
    if (currentIndex < totalQuestions - 1) {
      onQuestionSelect(currentIndex + 1);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      {/* Progress Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Exam Progress</h3>
        
        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="flex items-center space-x-2">
            <FiCheckCircle className="h-4 w-4 text-green-500" />
            <span className="text-gray-600">Answered:</span>
            <span className="font-semibold text-green-600">{answeredCount}</span>
          </div>
          <div className="flex items-center space-x-2">
            <FiCircle className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">Remaining:</span>
            <span className="font-semibold text-gray-600">{unansweredCount}</span>
          </div>
        </div>
      </div>

      {/* Question Grid */}
      <div className="p-4 border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Questions</h4>
        <div className="grid grid-cols-5 gap-2">
          {questions.map((question, index) => {
            const status = getQuestionStatus(question, index);
            return (
              <button
                key={question.id || index}
                onClick={() => onQuestionSelect(index)}
                className={getQuestionButtonClass(status)}
                title={`Question ${index + 1} - ${status.isAnswered ? 'Answered' : 'Not answered'}`}
              >
                {status.isAnswered && !status.isCurrent && (
                  <FiCheckCircle className="h-4 w-4" />
                )}
                {!status.isAnswered && !status.isCurrent && (
                  <span>{index + 1}</span>
                )}
                {status.isCurrent && (
                  <span className="font-bold">{index + 1}</span>
                )}
              </button>
            );
          })}
        </div>
        
        {/* Legend */}
        <div className="mt-4 space-y-2 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-600 rounded border"></div>
            <span className="text-gray-600">Current question</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-100 border border-green-500 rounded flex items-center justify-center">
              <FiCheckCircle className="h-2 w-2 text-green-600" />
            </div>
            <span className="text-gray-600">Answered</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-100 border border-gray-300 rounded"></div>
            <span className="text-gray-600">Not answered</span>
          </div>
        </div>
      </div>

      {/* Navigation Controls */}
      <div className="p-4 border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Navigation</h4>
        <div className="flex space-x-2">
          <button
            onClick={goToPrevious}
            disabled={currentIndex === 0}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-lg border transition-colors ${
              currentIndex === 0
                ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <FiChevronLeft className="h-4 w-4" />
            <span className="text-sm">Previous</span>
          </button>
          
          <button
            onClick={goToNext}
            disabled={currentIndex === totalQuestions - 1}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-lg border transition-colors ${
              currentIndex === totalQuestions - 1
                ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <span className="text-sm">Next</span>
            <FiChevronRight className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Quick Actions</h4>
        <div className="space-y-2">
          {/* Jump to first unanswered */}
          {unansweredCount > 0 && (
            <button
              onClick={() => {
                const firstUnanswered = questions.findIndex(q => !answers[q.id]);
                if (firstUnanswered >= 0) onQuestionSelect(firstUnanswered);
              }}
              className="w-full flex items-center justify-center space-x-2 py-2 px-3 text-sm bg-orange-50 text-orange-700 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors"
            >
              <FiFlag className="h-4 w-4" />
              <span>First Unanswered</span>
            </button>
          )}
          
          {/* Review all answers */}
          <button
            onClick={() => onQuestionSelect(0)}
            className="w-full flex items-center justify-center space-x-2 py-2 px-3 text-sm bg-blue-50 text-blue-700 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
          >
            <FiClock className="h-4 w-4" />
            <span>Review from Start</span>
          </button>
        </div>
      </div>

      {/* Submit Section */}
      <div className="p-4">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Submit Exam</h4>
        
        {/* Warning for unanswered questions */}
        {unansweredCount > 0 && (
          <div className="mb-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <FiFlag className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-sm">
                <p className="text-yellow-800 font-medium">
                  {unansweredCount} question{unansweredCount > 1 ? 's' : ''} remaining
                </p>
                <p className="text-yellow-700 mt-1">
                  You can submit now or continue answering.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Submit button */}
        <button
          onClick={onSubmit}
          disabled={!canSubmit || isSubmitting}
          className={`w-full flex items-center justify-center space-x-2 py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
            !canSubmit || isSubmitting
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : answeredCount === totalQuestions
              ? 'bg-green-600 text-white hover:bg-green-700 shadow-lg hover:shadow-xl'
              : 'bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl'
          }`}
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Submitting...</span>
            </>
          ) : (
            <>
              <FiSend className="h-4 w-4" />
              <span>Submit Exam</span>
            </>
          )}
        </button>
        
        {/* Submit info */}
        <p className="text-xs text-gray-500 mt-2 text-center">
          Once submitted, you cannot make changes to your answers.
        </p>
      </div>
    </div>
  );
};

export default ExamNavigation;
