/**
 * Exam Timer Component
 * Real-time countdown timer with visual warnings
 */

import React, { useEffect, useState, useRef } from 'react';
import { FiClock, FiAlertTriangle } from 'react-icons/fi';

const ExamTimer = ({ remainingTime, onTimeUpdate, isActive = true }) => {
  const [displayTime, setDisplayTime] = useState(remainingTime);
  const intervalRef = useRef(null);
  const lastUpdateRef = useRef(Date.now());

  // Format time as HH:MM:SS
  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Get timer color based on remaining time
  const getTimerColor = (seconds) => {
    if (seconds <= 300) return 'text-red-600'; // Last 5 minutes
    if (seconds <= 600) return 'text-orange-600'; // Last 10 minutes
    if (seconds <= 1800) return 'text-yellow-600'; // Last 30 minutes
    return 'text-green-600';
  };

  // Get background color for timer
  const getTimerBg = (seconds) => {
    if (seconds <= 300) return 'bg-red-50 border-red-200'; // Last 5 minutes
    if (seconds <= 600) return 'bg-orange-50 border-orange-200'; // Last 10 minutes
    if (seconds <= 1800) return 'bg-yellow-50 border-yellow-200'; // Last 30 minutes
    return 'bg-green-50 border-green-200';
  };

  // Update timer every second
  useEffect(() => {
    if (!isActive) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    intervalRef.current = setInterval(() => {
      const now = Date.now();
      const elapsed = Math.floor((now - lastUpdateRef.current) / 1000);
      
      setDisplayTime(prevTime => {
        const newTime = Math.max(0, prevTime - elapsed);
        onTimeUpdate(newTime);
        return newTime;
      });
      
      lastUpdateRef.current = now;
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isActive, onTimeUpdate]);

  // Update display time when remainingTime prop changes
  useEffect(() => {
    setDisplayTime(remainingTime);
    lastUpdateRef.current = Date.now();
  }, [remainingTime]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const timeColor = getTimerColor(displayTime);
  const timeBg = getTimerBg(displayTime);
  const isWarning = displayTime <= 1800; // Show warning for last 30 minutes
  const isCritical = displayTime <= 300; // Critical for last 5 minutes

  return (
    <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg border ${timeBg}`}>
      {/* Timer Icon */}
      <div className="flex items-center">
        {isWarning ? (
          <FiAlertTriangle className={`h-4 w-4 ${timeColor} ${isCritical ? 'animate-pulse' : ''}`} />
        ) : (
          <FiClock className={`h-4 w-4 ${timeColor}`} />
        )}
      </div>

      {/* Time Display */}
      <div className="flex flex-col">
        <span className={`text-lg font-mono font-bold ${timeColor} ${isCritical ? 'animate-pulse' : ''}`}>
          {formatTime(displayTime)}
        </span>
        <span className="text-xs text-gray-500">
          {displayTime > 3600 ? 'Time remaining' : 'Minutes left'}
        </span>
      </div>

      {/* Status Indicator */}
      {!isActive && (
        <div className="flex items-center">
          <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
        </div>
      )}
      
      {isActive && (
        <div className="flex items-center">
          <div className={`w-2 h-2 rounded-full ${
            isCritical ? 'bg-red-500 animate-pulse' : 
            isWarning ? 'bg-orange-500' : 'bg-green-500'
          }`}></div>
        </div>
      )}
    </div>
  );
};

export default ExamTimer;
