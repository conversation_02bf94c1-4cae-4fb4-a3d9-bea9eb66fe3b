/**
 * Question Display Component
 * Renders different types of questions with appropriate input methods
 */

import React, { useState, useEffect } from 'react';
import { FiImage, FiType, FiCheckSquare, FiEdit3 } from 'react-icons/fi';

const QuestionDisplay = ({ question, answer, onAnswerChange, isReadOnly = false }) => {
  const [localAnswer, setLocalAnswer] = useState(answer || '');
  const [selectedOption, setSelectedOption] = useState(null);

  // Update local state when answer prop changes
  useEffect(() => {
    if (question?.Type === 'MCQS') {
      // For MCQ, find the selected option
      const selected = question.options?.findIndex(opt => opt.option_text === answer);
      setSelectedOption(selected >= 0 ? selected : null);
    } else {
      setLocalAnswer(answer || '');
    }
  }, [answer, question]);

  // Handle answer changes with debouncing
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localAnswer !== answer && !isReadOnly) {
        onAnswerChange(localAnswer);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [localAnswer, answer, onAnswerChange, isReadOnly]);

  // Handle MCQ option selection
  const handleOptionSelect = (optionIndex) => {
    if (isReadOnly) return;
    
    setSelectedOption(optionIndex);
    const selectedAnswer = question.options[optionIndex]?.option_text || '';
    onAnswerChange(selectedAnswer);
  };

  // Handle text answer changes
  const handleTextChange = (value) => {
    if (isReadOnly) return;
    setLocalAnswer(value);
  };

  if (!question) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
        <div className="text-gray-400 mb-4">
          <FiEdit3 className="h-12 w-12 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Question Selected</h3>
        <p className="text-gray-600">Please select a question from the navigation panel.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      {/* Question Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              {question.Type === 'MCQS' ? (
                <FiCheckSquare className="h-5 w-5 text-blue-600" />
              ) : (
                <FiType className="h-5 w-5 text-green-600" />
              )}
              <span className="text-sm font-medium text-gray-700">
                {question.Type === 'MCQS'
                  ? 'Multiple Choice'
                  : question.Type === 'SHORT'
                  ? 'Short Answer'
                  : 'Long Answer'}
              </span>
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                {question.marks || 1} {question.marks === 1 ? 'mark' : 'marks'}
              </span>
              <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full">
                {question.Level || 'MEDIUM'}
              </span>
            </div>
          </div>
          
          {/* Answer Status */}
          <div className="flex items-center space-x-2">
            {answer ? (
              <div className="flex items-center space-x-1 text-green-600">
                <FiCheckSquare className="h-4 w-4" />
                <span className="text-sm font-medium">Answered</span>
              </div>
            ) : (
              <div className="flex items-center space-x-1 text-gray-400">
                <FiEdit3 className="h-4 w-4" />
                <span className="text-sm">Not answered</span>
              </div>
            )}
          </div>
        </div>

        {/* Question Text */}
        <div className="prose max-w-none">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {question.text}
          </h2>
        </div>

        {/* Question Image */}
        {question.imageUrl && (
          <div className="mt-4">
            <div className="relative inline-block">
              <img
                src={question.imageUrl}
                alt="Question illustration"
                className="max-w-full h-auto rounded-lg border border-gray-200"
                style={{ maxHeight: '400px' }}
              />
              <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs flex items-center space-x-1">
                <FiImage className="h-3 w-3" />
                <span>Image</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Answer Section */}
      <div className="p-6">
        {question.Type === 'MCQS' ? (
          // Multiple Choice Questions
          <div className="space-y-3">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Select your answer:</h3>
            {question.options?.map((option, index) => (
              <div
                key={index}
                className={`relative border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                  selectedOption === index
                    ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                } ${isReadOnly ? 'cursor-not-allowed opacity-60' : ''}`}
                onClick={() => handleOptionSelect(index)}
              >
                <div className="flex items-start space-x-3">
                  <div className={`flex-shrink-0 w-5 h-5 rounded-full border-2 flex items-center justify-center mt-0.5 ${
                    selectedOption === index
                      ? 'border-blue-500 bg-blue-500'
                      : 'border-gray-300'
                  }`}>
                    {selectedOption === index && (
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-sm font-medium text-gray-700">
                        Option {String.fromCharCode(65 + index)}
                      </span>
                    </div>
                    <p className="text-gray-900">{option.option_text}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          // Descriptive Questions
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Your answer:</h3>
            <div className="relative">
              <textarea
                value={localAnswer}
                onChange={(e) => handleTextChange(e.target.value)}
                placeholder={isReadOnly ? "No answer provided" : "Type your answer here..."}
                disabled={isReadOnly}
                className={`w-full h-64 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  isReadOnly ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'
                }`}
                style={{ minHeight: '200px' }}
              />
              
              {/* Character count */}
              <div className="absolute bottom-3 right-3 text-xs text-gray-500 bg-white px-2 py-1 rounded">
                {localAnswer.length} characters
              </div>
            </div>
            
            {/* Answer guidelines */}
            {!isReadOnly && (
              <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-1">Answer Guidelines:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Be clear and concise in your explanation</li>
                  <li>• Include relevant examples if applicable</li>
                  <li>• Check your spelling and grammar</li>
                  <li>• Your answer is automatically saved as you type</li>
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Answer Status Footer */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-4">
              {answer ? (
                <span className="text-green-600 font-medium">✓ Answer saved</span>
              ) : (
                <span className="text-gray-500">No answer provided</span>
              )}
              
              {(question.Type === 'SHORT' || question.Type === 'LONG') && localAnswer && (
                <span className="text-gray-500">
                  Word count: {localAnswer.trim().split(/\s+/).filter(word => word.length > 0).length}
                </span>
              )}
            </div>
            
            {isReadOnly && (
              <span className="text-orange-600 font-medium">Read-only mode</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuestionDisplay;
