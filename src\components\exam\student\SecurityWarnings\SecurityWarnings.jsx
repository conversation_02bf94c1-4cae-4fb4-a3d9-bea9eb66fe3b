/**
 * Security Warnings Modal Component
 * Displays security violations and warnings to students
 */

import React, { useEffect } from 'react';
import { 
  FiAlertTriangle, 
  FiShield, 
  FiClock, 
  FiX, 
  FiAlertCircle 
} from 'react-icons/fi';

const SecurityWarnings = ({ isOpen, warning, onDismiss }) => {
  // Auto-dismiss certain warnings after a delay
  useEffect(() => {
    if (isOpen && warning && warning.type === 'time') {
      const timer = setTimeout(() => {
        onDismiss();
      }, 5000); // Auto-dismiss time warnings after 5 seconds

      return () => clearTimeout(timer);
    }
  }, [isOpen, warning, onDismiss]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onDismiss();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onDismiss]);

  if (!isOpen || !warning) return null;

  const getWarningConfig = () => {
    switch (warning.type) {
      case 'security':
        return {
          icon: FiShield,
          title: 'Security Warning',
          iconColor: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          buttonColor: 'bg-red-600 hover:bg-red-700',
          canDismiss: true
        };
      case 'time':
        return {
          icon: FiClock,
          title: 'Time Warning',
          iconColor: 'text-orange-500',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          buttonColor: 'bg-orange-600 hover:bg-orange-700',
          canDismiss: true
        };
      case 'disqualification':
        return {
          icon: FiAlertCircle,
          title: 'Exam Disqualified',
          iconColor: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-300',
          buttonColor: 'bg-red-600 hover:bg-red-700',
          canDismiss: false
        };
      default:
        return {
          icon: FiAlertTriangle,
          title: 'Warning',
          iconColor: 'text-yellow-500',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          buttonColor: 'bg-yellow-600 hover:bg-yellow-700',
          canDismiss: true
        };
    }
  };

  const config = getWarningConfig();
  const IconComponent = config.icon;
  const severity = warning.severity || 'warning';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div 
        className={`bg-white rounded-lg shadow-xl max-w-md w-full border-2 ${config.borderColor} animate-pulse-once`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className={`p-6 border-b ${config.bgColor}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-full ${config.bgColor}`}>
                <IconComponent className={`h-6 w-6 ${config.iconColor}`} />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {config.title}
                </h3>
                {severity && (
                  <span className={`text-sm font-medium ${
                    severity === 'critical' ? 'text-red-600' :
                    severity === 'danger' ? 'text-red-500' :
                    severity === 'warning' ? 'text-orange-500' :
                    'text-blue-500'
                  }`}>
                    {severity.charAt(0).toUpperCase() + severity.slice(1)} Level
                  </span>
                )}
              </div>
            </div>
            
            {config.canDismiss && (
              <button
                onClick={onDismiss}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <FiX className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-gray-800 text-base leading-relaxed mb-4">
            {warning.message}
          </p>

          {/* Additional info based on warning type */}
          {warning.type === 'security' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <h4 className="text-sm font-semibold text-red-900 mb-2">
                Security Policy Reminder:
              </h4>
              <ul className="text-sm text-red-800 space-y-1">
                <li>• Stay in fullscreen mode during the exam</li>
                <li>• Do not switch tabs or open other applications</li>
                <li>• Do not use developer tools or right-click</li>
                <li>• Keep your mouse within the exam window</li>
              </ul>
              <p className="text-sm text-red-700 mt-2 font-medium">
                3 strikes will result in automatic disqualification.
              </p>
            </div>
          )}

          {warning.type === 'time' && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
              <h4 className="text-sm font-semibold text-orange-900 mb-2">
                Time Management Tips:
              </h4>
              <ul className="text-sm text-orange-800 space-y-1">
                <li>• Review your answers before submitting</li>
                <li>• Focus on unanswered questions first</li>
                <li>• Don't spend too much time on difficult questions</li>
                <li>• Your progress is automatically saved</li>
              </ul>
            </div>
          )}

          {warning.type === 'disqualification' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <h4 className="text-sm font-semibold text-red-900 mb-2">
                What happens next:
              </h4>
              <ul className="text-sm text-red-800 space-y-1">
                <li>• Your exam session has been terminated</li>
                <li>• Your teacher will be notified</li>
                <li>• You will be redirected to the dashboard</li>
                <li>• Contact your teacher for further instructions</li>
              </ul>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-gray-50 border-t flex justify-end space-x-3">
          {config.canDismiss ? (
            <>
              {warning.type === 'time' && (
                <button
                  onClick={onDismiss}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Continue Exam
                </button>
              )}
              <button
                onClick={onDismiss}
                className={`px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors ${config.buttonColor}`}
              >
                {warning.type === 'security' ? 'I Understand' : 'OK'}
              </button>
            </>
          ) : (
            <div className="text-sm text-gray-600">
              Redirecting in a few seconds...
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SecurityWarnings;
