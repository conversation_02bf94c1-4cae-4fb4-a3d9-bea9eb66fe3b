import React from 'react';
import { useSelector } from 'react-redux';
import { 
  FiCheckCircle, 
  FiClock, 
  FiLoader, 
  FiAlertCircle,
  FiSend,
  FiStar
} from 'react-icons/fi';
import { selectAIChecking, selectCheckingStatus, selectProgress } from '../../../store/slices/exam/aiCheckingSlice';

/**
 * Submission Status Tracker Component
 * Shows the progress of exam submission and AI checking
 */
function SubmissionStatusTracker({ examId, attemptId, className = "" }) {
  const aiChecking = useSelector(selectAIChecking);
  const checkingStatus = useSelector(selectCheckingStatus);
  const progress = useSelector(selectProgress);

  // Define the submission flow steps
  const steps = [
    {
      id: 'submitted',
      title: 'Exam Submitted',
      description: 'Your exam has been successfully submitted',
      icon: FiSend,
      status: 'completed' // Always completed if we're showing this component
    },
    {
      id: 'ai_triggered',
      title: 'AI Evaluation Started',
      description: 'AI is preparing to evaluate your answers',
      icon: FiStar,
      status: checkingStatus === 'idle' ? 'pending' : 'completed'
    },
    {
      id: 'ai_processing',
      title: 'AI Processing',
      description: 'AI is analyzing your answers and generating feedback',
      icon: FiLoader,
      status: checkingStatus === 'checking' || checkingStatus === 'waiting' ? 'active' : 
             checkingStatus === 'completed' ? 'completed' : 'pending'
    },
    {
      id: 'results_ready',
      title: 'Results Ready',
      description: 'AI evaluation completed with detailed feedback',
      icon: FiCheckCircle,
      status: checkingStatus === 'completed' ? 'completed' : 
             checkingStatus === 'error' ? 'error' : 'pending'
    }
  ];

  // Get step status styling
  const getStepStyling = (step) => {
    switch (step.status) {
      case 'completed':
        return {
          container: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',
          icon: 'bg-green-100 dark:bg-green-900/40 text-green-600 dark:text-green-400',
          title: 'text-green-800 dark:text-green-200',
          description: 'text-green-600 dark:text-green-400'
        };
      case 'active':
        return {
          container: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800',
          icon: 'bg-blue-100 dark:bg-blue-900/40 text-blue-600 dark:text-blue-400',
          title: 'text-blue-800 dark:text-blue-200',
          description: 'text-blue-600 dark:text-blue-400'
        };
      case 'error':
        return {
          container: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',
          icon: 'bg-red-100 dark:bg-red-900/40 text-red-600 dark:text-red-400',
          title: 'text-red-800 dark:text-red-200',
          description: 'text-red-600 dark:text-red-400'
        };
      default: // pending
        return {
          container: 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700',
          icon: 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500',
          title: 'text-gray-600 dark:text-gray-400',
          description: 'text-gray-500 dark:text-gray-500'
        };
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Progress Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">Submission Progress</h3>
        {progress.percentage > 0 && (
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {progress.percentage}% Complete
          </div>
        )}
      </div>

      {/* Progress Bar */}
      {progress.percentage > 0 && (
        <div className="mb-6">
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${progress.percentage}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Steps */}
      <div className="space-y-3">
        {steps.map((step, index) => {
          const styling = getStepStyling(step);
          const IconComponent = step.icon;
          const isActive = step.status === 'active';
          const isError = step.status === 'error';

          return (
            <div
              key={step.id}
              className={`p-4 rounded-lg border transition-all duration-300 ${styling.container}`}
            >
              <div className="flex items-start gap-4">
                {/* Step Icon */}
                <div className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 ${styling.icon}`}>
                  {isActive ? (
                    <FiLoader className="w-5 h-5 animate-spin" />
                  ) : isError ? (
                    <FiAlertCircle className="w-5 h-5" />
                  ) : (
                    <IconComponent className="w-5 h-5" />
                  )}
                </div>

                {/* Step Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className={`font-medium ${styling.title}`}>
                      {step.title}
                    </h4>
                    {step.status === 'completed' && (
                      <FiCheckCircle className="w-4 h-4 text-green-500" />
                    )}
                  </div>
                  <p className={`text-sm ${styling.description}`}>
                    {isActive && progress.message ? progress.message : step.description}
                  </p>

                  {/* Error Message */}
                  {isError && aiChecking.error && (
                    <div className="mt-2 text-sm text-red-600 dark:text-red-400">
                      Error: {aiChecking.error}
                    </div>
                  )}
                </div>

                {/* Step Number */}
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                  step.status === 'completed' ? 'bg-green-500 text-white' :
                  step.status === 'active' ? 'bg-blue-500 text-white' :
                  step.status === 'error' ? 'bg-red-500 text-white' :
                  'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400'
                }`}>
                  {step.status === 'completed' ? '✓' : index + 1}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Current Status Message */}
      {progress.message && (
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
            <FiClock className="w-4 h-4" />
            <span className="text-sm font-medium">{progress.message}</span>
          </div>
        </div>
      )}

      {/* AI Results Summary */}
      {checkingStatus === 'completed' && examId && aiChecking.results[examId] && (
        <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center gap-3 mb-2">
            <FiStar className="w-5 h-5 text-green-600" />
            <h4 className="font-semibold text-green-800 dark:text-green-200">
              AI Evaluation Complete!
            </h4>
          </div>
          <p className="text-sm text-green-700 dark:text-green-300">
            Your exam has been evaluated with a score of {aiChecking.results[examId]?.result?.percentage || 0}%.
            Click "View Results" to see detailed feedback.
          </p>
        </div>
      )}
    </div>
  );
}

export default SubmissionStatusTracker;
