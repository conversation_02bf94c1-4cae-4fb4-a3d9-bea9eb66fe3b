/**
 * Teacher Monitoring Dashboard
 * Real-time monitoring interface for active exam sessions
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { fetchExamById } from '../../../../store/slices/ExamSlice';
import URL from '../../../../utils/api/API_URL';
import {
  FiUsers,
  FiClock,
  FiShield,
  FiAlertTriangle,
  FiCheckCircle,
  FiWifi,
  FiWifiOff,
  FiRefreshCw,
  FiEye,
  FiUserX,
  FiMessageSquare
} from 'react-icons/fi';

// Import child components (will be created)
// import StudentSessionCard from '../StudentSessionCard/StudentSessionCard';
// import SessionOverview from '../SessionOverview/SessionOverview';
// import ReconnectionManager from '../ReconnectionManager/ReconnectionManager';

const MonitoringDashboard = () => {
  const { examId } = useParams();
  const dispatch = useDispatch();

  // Redux state
  const { currentExam: examData, loading: examLoading, error: examError } = useSelector((state) => state.exams);

  // Local state management
  const [activeSessions, setActiveSessions] = useState([]);
  const [reconnectionRequests, setReconnectionRequests] = useState([]);
  const [cheatingIncidents, setCheatingIncidents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  
  // Filters and view options
  const [statusFilter, setStatusFilter] = useState('all'); // all, active, disconnected, completed
  const [sortBy, setSortBy] = useState('name'); // name, progress, time, strikes
  const [viewMode, setViewMode] = useState('grid'); // grid, list
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Auto-refresh interval
  const [refreshInterval, setRefreshInterval] = useState(null);

  /**
   * Fetch exam data and active sessions
   */
  const fetchExamData = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');

      // Fetch exam details using Redux
      if (examId) {
        dispatch(fetchExamById(examId));
      }

      // Fetch active sessions with correct API URL
      const sessionsResponse = await fetch(`${URL}/api/exams/${examId}/active-sessions`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (!sessionsResponse.ok) {
        throw new Error('Failed to fetch active sessions');
      }

      const sessions = await sessionsResponse.json();
      setActiveSessions(sessions);

    } catch (err) {

      setError(err.message);
    }
  }, [examId, dispatch]);

  /**
   * Fetch reconnection requests
   */
  const fetchReconnectionRequests = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${URL}/api/exams/${examId}/reconnection-requests`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const requests = await response.json();
        setReconnectionRequests(requests);
      }
    } catch (err) {

    }
  }, [examId]);

  /**
   * Fetch cheating incidents
   */
  const fetchCheatingIncidents = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${URL}/api/exams/${examId}/cheating-incidents`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const incidents = await response.json();
        setCheatingIncidents(incidents);
      }
    } catch (err) {

    }
  }, [examId]);

  /**
   * Refresh all data
   */
  const refreshData = useCallback(async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        fetchExamData(),
        fetchReconnectionRequests(),
        fetchCheatingIncidents()
      ]);
    } finally {
      setRefreshing(false);
    }
  }, [fetchExamData, fetchReconnectionRequests, fetchCheatingIncidents]);

  /**
   * Initial data load
   */
  useEffect(() => {
    const loadInitialData = async () => {
      setLoading(true);
      await refreshData();
      setLoading(false);
    };
    
    loadInitialData();
  }, [refreshData]);

  /**
   * Setup auto-refresh
   */
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(refreshData, 10000); // Refresh every 10 seconds
      setRefreshInterval(interval);
      
      return () => {
        if (interval) clearInterval(interval);
      };
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }
  }, [autoRefresh, refreshData, refreshInterval]);

  /**
   * Handle session termination
   */
  const handleTerminateSession = async (sessionId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${URL}/api/exams/session/admin/exam-session/${sessionId}/terminate`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        await refreshData();
      } else {
        throw new Error('Failed to terminate session');
      }
    } catch (err) {

      alert('Failed to terminate session: ' + err.message);
    }
  };

  /**
   * Filter and sort sessions
   */
  const filteredAndSortedSessions = activeSessions
    .filter(session => {
      if (statusFilter === 'all') return true;
      return session.status === statusFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.student_name.localeCompare(b.student_name);
        case 'progress':
          return (b.progress || 0) - (a.progress || 0);
        case 'time':
          return (a.remaining_time || 0) - (b.remaining_time || 0);
        case 'strikes':
          return (b.strikes || 0) - (a.strikes || 0);
        default:
          return 0;
      }
    });

  // Calculate statistics
  const stats = {
    total: activeSessions.length,
    active: activeSessions.filter(s => s.status === 'active').length,
    disconnected: activeSessions.filter(s => s.status === 'disconnected').length,
    completed: activeSessions.filter(s => s.status === 'completed').length,
    withStrikes: activeSessions.filter(s => (s.strikes || 0) > 0).length,
    pendingReconnections: reconnectionRequests.filter(r => r.status === 'pending').length
  };

  if (loading || examLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Exam Monitor</h2>
          <p className="text-gray-600">Fetching real-time exam data...</p>
        </div>
      </div>
    );
  }

  if (error || examError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <FiAlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Monitor</h2>
          <p className="text-gray-600 mb-4">{error || examError}</p>
          <button
            onClick={refreshData}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                Exam Monitor: {examData?.title}
              </h1>
              <p className="text-sm text-gray-600">
                Real-time monitoring of active exam sessions
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Auto-refresh toggle */}
              <label className="flex items-center space-x-2 text-sm">
                <input
                  type="checkbox"
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-gray-700">Auto-refresh</span>
              </label>
              
              {/* Manual refresh */}
              <button
                onClick={refreshData}
                disabled={refreshing}
                className="flex items-center space-x-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                <FiRefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Overview Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FiUsers className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <FiCheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Sessions</p>
                <p className="text-2xl font-bold text-gray-900">{stats.active}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <FiShield className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Security Issues</p>
                <p className="text-2xl font-bold text-gray-900">{stats.withStrikes}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <FiWifiOff className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Reconnection Requests</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pendingReconnections}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Reconnection Requests */}
        {reconnectionRequests.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Pending Reconnection Requests</h3>
            <div className="space-y-3">
              {reconnectionRequests.filter(r => r.status === 'pending').map((request) => (
                <div key={request.id} className="flex items-center justify-between p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{request.student_name}</p>
                    <p className="text-sm text-gray-600">Reason: {request.reason}</p>
                    <p className="text-xs text-gray-500">Requested: {new Date(request.created_at).toLocaleTimeString()}</p>
                  </div>
                  <div className="flex space-x-2">
                    <button className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                      Approve
                    </button>
                    <button className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                      Deny
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
          <div className="flex flex-wrap items-center justify-between gap-4">
            {/* Filters */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">Status:</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="text-sm border border-gray-300 rounded-lg px-3 py-1"
                >
                  <option value="all">All ({stats.total})</option>
                  <option value="active">Active ({stats.active})</option>
                  <option value="disconnected">Disconnected ({stats.disconnected})</option>
                  <option value="completed">Completed ({stats.completed})</option>
                </select>
              </div>
              
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">Sort by:</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="text-sm border border-gray-300 rounded-lg px-3 py-1"
                >
                  <option value="name">Name</option>
                  <option value="progress">Progress</option>
                  <option value="time">Time Remaining</option>
                  <option value="strikes">Strikes</option>
                </select>
              </div>
            </div>

            {/* View Mode */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
              >
                <div className="grid grid-cols-2 gap-1 w-4 h-4">
                  <div className="bg-current rounded-sm"></div>
                  <div className="bg-current rounded-sm"></div>
                  <div className="bg-current rounded-sm"></div>
                  <div className="bg-current rounded-sm"></div>
                </div>
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
              >
                <div className="space-y-1 w-4 h-4">
                  <div className="bg-current h-1 rounded-sm"></div>
                  <div className="bg-current h-1 rounded-sm"></div>
                  <div className="bg-current h-1 rounded-sm"></div>
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Student Sessions */}
        <div className={`${
          viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }`}>
          {filteredAndSortedSessions.map((session) => (
            <div key={session.session_id} className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    session.status === 'active' ? 'bg-green-500' :
                    session.status === 'disconnected' ? 'bg-red-500' :
                    'bg-gray-500'
                  }`}></div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{session.student_name}</h3>
                    <p className="text-sm text-gray-600">{session.student_email}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {session.strikes > 0 && (
                    <div className="flex items-center space-x-1 text-red-600">
                      <FiShield className="h-4 w-4" />
                      <span className="text-sm font-medium">{session.strikes}/3</span>
                    </div>
                  )}

                  <button
                    onClick={() => handleTerminateSession(session.session_id)}
                    className="p-1 text-red-600 hover:bg-red-50 rounded"
                    title="Terminate Session"
                  >
                    <FiUserX className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Progress:</span>
                  <span className="font-medium">{Math.round(session.progress || 0)}%</span>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${session.progress || 0}%` }}
                  ></div>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Time Remaining:</span>
                  <span className="font-medium">
                    {Math.floor((session.remaining_time || 0) / 60)}:{String((session.remaining_time || 0) % 60).padStart(2, '0')}
                  </span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Status:</span>
                  <span className={`font-medium capitalize ${
                    session.status === 'active' ? 'text-green-600' :
                    session.status === 'disconnected' ? 'text-red-600' :
                    'text-gray-600'
                  }`}>
                    {session.status}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredAndSortedSessions.length === 0 && (
          <div className="text-center py-12">
            <FiUsers className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No sessions found
            </h3>
            <p className="text-gray-600">
              {statusFilter === 'all' 
                ? 'No students have started this exam yet.'
                : `No ${statusFilter} sessions found.`
              }
            </p>
          </div>
        )}
      </main>
    </div>
  );
};

export default MonitoringDashboard;
