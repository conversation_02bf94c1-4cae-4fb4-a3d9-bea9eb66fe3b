import React, { useState, useEffect, useRef } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { ChevronDownIcon, ChevronLeftIcon } from "@heroicons/react/24/outline";
import SidebarLinkGroup from './SidebarLinkGroup';

function Sidebar({ sidebarOpen, setSidebarOpen, config = [], variant = 'default' }) {
  const location = useLocation();
  const { pathname } = location;

  const trigger = useRef(null);
  const sidebar = useRef(null);

  const storedSidebarExpanded = localStorage.getItem("sidebar-expanded");
  const [sidebarExpanded, setSidebarExpanded] = useState(
    storedSidebarExpanded === null ? false : storedSidebarExpanded === "true"
  );

  // Close on click outside
  useEffect(() => {
    const clickHandler = ({ target }) => {
      if (!sidebar.current || !trigger.current) return;
      if (!sidebarOpen || sidebar.current.contains(target) || trigger.current.contains(target)) return;
      setSidebarOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  // Close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!sidebarOpen || keyCode !== 27) return;
      setSidebarOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  // Handle sidebar expansion
  useEffect(() => {
    localStorage.setItem("sidebar-expanded", sidebarExpanded);
    const body = document.querySelector("body");
    if (sidebarExpanded) {
      body.classList.add("sidebar-expanded");
    } else {
      body.classList.remove("sidebar-expanded");
    }
  }, [sidebarExpanded]);

  // Check if a path is active
  const isPathActive = (path) => {
    if (path === "/") {
      return pathname === "/" || pathname.includes("dashboard");
    }
    return pathname.includes(path);
  };

  // Render navigation item with children
  const renderNavItemWithChildren = (item, isActive) => {
    return (
      <SidebarLinkGroup key={item.label} activecondition={isActive}>
        {(handleClick, open) => (
          <>
            <a
              href="#0"
              className={`block text-gray-800 dark:text-gray-100 truncate transition duration-150 ${
                isActive ? "" : "hover:text-gray-900 dark:hover:text-white"
              }`}
              onClick={(e) => {
                e.preventDefault();
                handleClick();
                setSidebarExpanded(true);
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  {item.icon && React.createElement(item.icon, {
                    className: `shrink-0 w-4 h-4 ${isActive ? 'text-violet-500' : 'text-gray-400 dark:text-gray-500'}`
                  })}
                  <span className="text-sm font-medium ml-4 lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                    {item.label}
                  </span>
                </div>
                <ChevronDownIcon className={`w-3 h-3 shrink-0 ml-1 text-gray-400 dark:text-gray-500 ${open && "rotate-180"}`} />
              </div>
            </a>
            <div className="lg:hidden lg:sidebar-expanded:block 2xl:block">
              <ul className={`pl-8 mt-1 ${!open && "hidden"}`}>
                {item.children.map((child) => (
                  <li key={child.label} className="mb-1 last:mb-0">
                    <NavLink
                      end
                      to={child.path}
                      className={({ isActive }) =>
                        "block transition duration-150 truncate " + 
                        (isActive ? "text-violet-500" : "text-gray-500/90 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200")
                      }
                    >
                      <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                        {child.label}
                      </span>
                    </NavLink>
                  </li>
                ))}
              </ul>
            </div>
          </>
        )}
      </SidebarLinkGroup>
    );
  };

  // Render simple navigation item
  const renderSimpleNavItem = (item, isActive) => {
    const itemContent = (
      <div className="flex items-center justify-between">
        <div className="grow flex items-center">
          {item.icon && React.createElement(item.icon, {
            className: `shrink-0 w-4 h-4 ${isActive ? 'text-violet-500' : 'text-gray-400 dark:text-gray-500'}`
          })}
          <span className="text-sm font-medium ml-4 lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
            {item.label}
          </span>
        </div>
        {item.badge && (
          <div className="flex shrink-0 ml-2">
            <span className="inline-flex items-center justify-center h-5 text-xs font-medium text-white bg-violet-400 px-2 rounded-sm">
              {item.badge}
            </span>
          </div>
        )}
      </div>
    );

    return (
      <li key={item.label} className={`pl-4 pr-3 py-2 rounded-lg mb-0.5 last:mb-0 bg-linear-to-r ${isActive && "from-violet-500/[0.12] dark:from-violet-500/[0.24] to-violet-500/[0.04]"}`}>
        {item.onClick ? (
          <div
            className={`block text-gray-800 dark:text-gray-100 truncate transition duration-150 cursor-pointer ${
              isActive ? "" : "hover:text-gray-900 dark:hover:text-white"
            }`}
            onClick={item.onClick}
          >
            {itemContent}
          </div>
        ) : (
          <NavLink
            end
            to={item.path}
            className={`block text-gray-800 dark:text-gray-100 truncate transition duration-150 ${
              isActive ? "" : "hover:text-gray-900 dark:hover:text-white"
            }`}
          >
            {itemContent}
          </NavLink>
        )}
      </li>
    );
  };

  // Render navigation item
  const renderNavItem = (item) => {
    const isActive = isPathActive(item.path);
    
    if (item.children) {
      return renderNavItemWithChildren(item, isActive);
    }
    
    return renderSimpleNavItem(item, isActive);
  };

  return (
    <div className="min-w-fit">
      {/* Sidebar backdrop (mobile only) */}
      <div
        className={`fixed inset-0 bg-gray-900/30 z-40 lg:hidden lg:z-auto transition-opacity duration-200 ${
          sidebarOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
        aria-hidden="true"
      />

      {/* Sidebar */}
      <div
        id="sidebar"
        ref={sidebar}
        className={`flex lg:flex! flex-col absolute z-40 left-0 top-0 lg:static lg:left-auto lg:top-auto lg:translate-x-0 h-[100dvh] overflow-y-scroll lg:overflow-y-auto no-scrollbar w-64 lg:w-20 lg:sidebar-expanded:!w-64 2xl:w-64! shrink-0 bg-white dark:bg-gray-800 p-4 transition-all duration-200 ease-in-out ${sidebarOpen ? "translate-x-0" : "-translate-x-64"} ${variant === 'v2' ? 'border-r border-gray-200 dark:border-gray-700/60' : 'rounded-r-2xl shadow-xs'}`}
      >
        {/* Sidebar header */}
        <div className="flex justify-between mb-10 pr-3 sm:px-2">
          {/* Close button */}
          <button
            ref={trigger}
            className="lg:hidden text-gray-500 hover:text-gray-400"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            aria-controls="sidebar"
            aria-expanded={sidebarOpen}
          >
            <span className="sr-only">Close sidebar</span>
            <svg className="w-6 h-6 fill-current" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M10.7 18.7l1.4-1.4L7.8 13H20v-2H7.8l4.3-4.3-1.4-1.4L4 12z" />
            </svg>
          </button>
          
          {/* Logo */}
          <NavLink end to="/" className="block">
            <svg className="fill-violet-500" xmlns="http://www.w3.org/2000/svg" width={32} height={32}>
              <path d="M31.956 14.8C31.372 6.92 25.08.628 17.2.044V5.76a9.04 9.04 0 0 0 9.04 9.04h5.716ZM14.8 26.24v5.716C6.92 31.372.63 25.08.044 17.2H5.76a9.04 9.04 0 0 1 9.04 9.04Zm11.44-9.04h5.716c-.584 7.88-6.876 14.172-14.756 14.756V26.24a9.04 9.04 0 0 1 9.04-9.04ZM.044 14.8C.63 6.92 6.92.628 14.8.044V5.76a9.04 9.04 0 0 1-9.04 9.04H.044Z" />
            </svg>
          </NavLink>
        </div>

        {/* Navigation Links */}
        <div className="space-y-8">
          <div>
            <h3 className="text-xs uppercase text-gray-400 dark:text-gray-500 font-semibold pl-3">
              <span className="hidden lg:block lg:sidebar-expanded:hidden 2xl:hidden text-center w-6" aria-hidden="true">
                •••
              </span>
              <span className="lg:hidden lg:sidebar-expanded:block 2xl:block">Navigation</span>
            </h3>
            <ul className="mt-3">
              {config.map(renderNavItem)}
            </ul>
          </div>
        </div>

        {/* Expand / collapse button */}
        <div className="pt-3 hidden lg:inline-flex 2xl:hidden justify-end mt-auto">
          <div className="w-12 pl-4 pr-3 py-2">
            <button 
              className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400" 
              onClick={() => setSidebarExpanded(!sidebarExpanded)}
            >
              <span className="sr-only">Expand / collapse sidebar</span>
              <ChevronLeftIcon className="shrink-0 w-4 h-4 text-gray-400 dark:text-gray-500 sidebar-expanded:rotate-180" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Sidebar;
