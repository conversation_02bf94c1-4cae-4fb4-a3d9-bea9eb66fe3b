/**
 * Connection Status Component
 * Shows real-time WebSocket connection status
 */

import React from 'react';
import { FiWifi, FiWifiOff, <PERSON><PERSON><PERSON><PERSON>, FiAlertCircle } from 'react-icons/fi';

const ConnectionStatus = ({ status }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          icon: FiWifi,
          text: 'Connected',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          pulse: false
        };
      case 'connecting':
        return {
          icon: FiLoader,
          text: 'Connecting',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          pulse: true
        };
      case 'reconnecting':
        return {
          icon: FiLoader,
          text: 'Reconnecting',
          color: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          pulse: true
        };
      case 'disconnected':
      default:
        return {
          icon: FiWifiOff,
          text: 'Disconnected',
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          pulse: false
        };
    }
  };

  const config = getStatusConfig();
  const IconComponent = config.icon;

  return (
    <div className={`flex items-center space-x-2 px-3 py-1 rounded-full border ${config.bgColor} ${config.borderColor}`}>
      <IconComponent 
        className={`h-4 w-4 ${config.color} ${config.pulse ? 'animate-spin' : ''}`} 
      />
      <span className={`text-sm font-medium ${config.color}`}>
        {config.text}
      </span>
    </div>
  );
};

export default ConnectionStatus;
