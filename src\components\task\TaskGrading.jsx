import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  gradeTaskSubmission,
  fetchTaskSubmissions,
  selectSubmissions,
  selectSubmissionLoading,
  selectGradingState,
  clearGradingState
} from '../../store/slices/TaskSlice';
import TaskAttachments from './TaskAttachments';
import {
  FiUser,
  FiCalendar,
  FiClock,
  FiStar,
  FiMessageSquare,
  FiSave,
  FiCheck,
  FiX,
  FiLoader,
  FiAlertCircle,
  FiCheckCircle,
  FiEdit3,
  FiEye
} from 'react-icons/fi';

/**
 * TaskGrading Component
 * Handles grading of task submissions by teachers
 * 
 * Props:
 * - task: Task object with details
 * - submission: Submission object to grade
 * - onGradingComplete: Callback when grading is completed
 * - className: Additional CSS classes
 */
const TaskGrading = ({
  task,
  submission,
  onGradingComplete,
  className = ''
}) => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();

  // Redux state
  const gradingState = useSelector(selectGradingState);

  // Local state
  const [grade, setGrade] = useState('');
  const [maxGrade, setMaxGrade] = useState(task?.max_grade || 100);
  const [feedback, setFeedback] = useState('');
  const [isGrading, setIsGrading] = useState(false);
  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300';

  // Initialize form with existing grade data
  useEffect(() => {
    if (submission) {
      setGrade(submission.grade || '');
      setFeedback(submission.feedback || '');
      if (submission.max_grade) {
        setMaxGrade(submission.max_grade);
      }
    }
  }, [submission]);

  // Clear grading state on unmount
  useEffect(() => {
    return () => {
      dispatch(clearGradingState());
    };
  }, [dispatch]);

  // Handle grade submission
  const handleSubmitGrade = async () => {
    if (!grade || isNaN(grade) || grade < 0 || grade > maxGrade) {
      alert(`Please enter a valid grade between 0 and ${maxGrade}`);
      return;
    }

    try {
      const gradingData = {
        grade: parseFloat(grade),
        max_grade: maxGrade,
        feedback: feedback.trim(),
        graded_at: new Date().toISOString()
      };

      await dispatch(gradeTaskSubmission({
        task_id: task.id,
        student_id: submission.student_id,
        grading_data: gradingData
      })).unwrap();

      setIsGrading(false);
      setShowSubmitConfirm(false);
      
      if (onGradingComplete) {
        onGradingComplete();
      }
    } catch (error) {
      console.error('Grading failed:', error);
    }
  };

  // Handle edit mode
  const handleStartGrading = () => {
    setIsGrading(true);
  };

  const handleCancelGrading = () => {
    setIsGrading(false);
    // Reset to original values
    setGrade(submission?.grade || '');
    setFeedback(submission?.feedback || '');
  };

  // Calculate grade percentage
  const getGradePercentage = () => {
    if (!grade || !maxGrade) return 0;
    return Math.round((parseFloat(grade) / maxGrade) * 100);
  };

  // Get grade color based on percentage
  const getGradeColor = (percentage) => {
    if (percentage >= 90) return 'text-green-600 dark:text-green-400';
    if (percentage >= 80) return 'text-blue-600 dark:text-blue-400';
    if (percentage >= 70) return 'text-yellow-600 dark:text-yellow-400';
    if (percentage >= 60) return 'text-orange-600 dark:text-orange-400';
    return 'text-red-600 dark:text-red-400';
  };

  // Status badge component
  const StatusBadge = ({ status }) => {
    const statusConfig = {
      submitted: {
        color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
        icon: FiClock,
        text: 'Pending Review'
      },
      graded: {
        color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
        icon: FiCheckCircle,
        text: 'Graded'
      }
    };

    const config = statusConfig[status] || statusConfig.submitted;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3" />
        {config.text}
      </span>
    );
  };

  if (!submission) {
    return (
      <div className={`${bgPrimary} rounded-lg border ${borderColor} p-6 ${className}`}>
        <div className="text-center py-8">
          <FiUser className={`w-8 h-8 mx-auto mb-2 ${textSecondary} opacity-50`} />
          <p className={`${textSecondary}`}>No submission selected</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`${bgPrimary} rounded-lg border ${borderColor} ${className}`}>
      {/* Header */}
      <div className={`px-6 py-4 border-b ${borderColor} ${bgSecondary}`}>
        <div className="flex items-center justify-between">
          <div>
            <h3 className={`text-lg font-semibold ${textPrimary} flex items-center gap-2`}>
              <FiUser className="w-5 h-5" />
              {submission.student_name || `Student ${submission.student_id}`}
            </h3>
            <div className="flex items-center gap-4 mt-2">
              <StatusBadge status={submission.status} />
              
              <div className="flex items-center gap-1 text-sm text-gray-500">
                <FiCalendar className="w-4 h-4" />
                <span>
                  Submitted: {new Date(submission.submitted_at).toLocaleDateString()} at{' '}
                  {new Date(submission.submitted_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </span>
              </div>
            </div>
          </div>

          <div className="text-right">
            {submission.grade !== null && submission.grade !== undefined ? (
              <div>
                <div className={`text-2xl font-bold ${getGradeColor(getGradePercentage())}`}>
                  {submission.grade}/{maxGrade}
                </div>
                <div className={`text-sm ${textSecondary}`}>
                  {getGradePercentage()}%
                </div>
              </div>
            ) : (
              <div className={`text-sm ${textSecondary}`}>
                Not graded
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {gradingState.error && (
        <div className="px-6 py-3 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
          <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <FiAlertCircle className="w-4 h-4" />
            <span className="text-sm">
              {typeof gradingState.error === 'string'
                ? gradingState.error
                : gradingState.error?.detail || gradingState.error?.message || 'An error occurred'
              }
            </span>
          </div>
        </div>
      )}

      {/* Success Display */}
      {gradingState.success && (
        <div className="px-6 py-3 bg-green-50 dark:bg-green-900/20 border-b border-green-200 dark:border-green-800">
          <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
            <FiCheckCircle className="w-4 h-4" />
            <span className="text-sm">Grade submitted successfully!</span>
          </div>
        </div>
      )}

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* Student Submission Text */}
        <div>
          <h4 className={`text-sm font-medium ${textPrimary} mb-3 flex items-center gap-2`}>
            <FiMessageSquare className="w-4 h-4" />
            Submission Text
          </h4>
          <div className={`p-4 border ${borderColor} rounded-lg ${bgSecondary}`}>
            {submission.submission_text ? (
              <div className={`whitespace-pre-wrap ${textPrimary}`}>
                {submission.submission_text}
              </div>
            ) : (
              <div className={`text-center py-4 ${textSecondary}`}>
                No text submission provided
              </div>
            )}
          </div>
        </div>

        {/* Student Attachments */}
        <div>
          <h4 className={`text-sm font-medium ${textPrimary} mb-3`}>
            Student Files
          </h4>
          <TaskAttachments
            taskId={task?.id}
            attachmentType="submission"
            canUpload={false}
            canDelete={false}
          />
        </div>

        {/* Grading Section */}
        <div className="border-t pt-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className={`text-sm font-medium ${textPrimary} flex items-center gap-2`}>
              <FiStar className="w-4 h-4" />
              Grading
            </h4>
            
            {!isGrading && (
              <button
                onClick={handleStartGrading}
                className="text-blue-600 hover:text-blue-700 text-sm flex items-center gap-1"
              >
                <FiEdit3 className="w-4 h-4" />
                {submission.grade !== null ? 'Edit Grade' : 'Grade Submission'}
              </button>
            )}
          </div>

          {isGrading ? (
            <div className="space-y-4">
              {/* Grade Input */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium ${textPrimary} mb-2`}>
                    Grade
                  </label>
                  <input
                    type="number"
                    value={grade}
                    onChange={(e) => setGrade(e.target.value)}
                    min="0"
                    max={maxGrade}
                    step="0.1"
                    placeholder="Enter grade"
                    className={`w-full px-3 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary}`}
                  />
                </div>
                
                <div>
                  <label className={`block text-sm font-medium ${textPrimary} mb-2`}>
                    Max Grade
                  </label>
                  <input
                    type="number"
                    value={maxGrade}
                    onChange={(e) => setMaxGrade(parseInt(e.target.value) || 100)}
                    min="1"
                    className={`w-full px-3 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary}`}
                  />
                </div>
              </div>

              {/* Grade Preview */}
              {grade && (
                <div className={`text-sm ${textSecondary}`}>
                  Percentage: {getGradePercentage()}%
                </div>
              )}

              {/* Feedback */}
              <div>
                <label className={`block text-sm font-medium ${textPrimary} mb-2`}>
                  Feedback (Optional)
                </label>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder="Provide feedback to the student..."
                  rows={4}
                  className={`w-full px-3 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary} placeholder-gray-400`}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setShowSubmitConfirm(true)}
                  disabled={gradingState.loading || !grade}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center gap-2"
                >
                  {gradingState.loading ? (
                    <FiLoader className="w-4 h-4 animate-spin" />
                  ) : (
                    <FiCheck className="w-4 h-4" />
                  )}
                  Submit Grade
                </button>
                
                <button
                  onClick={handleCancelGrading}
                  className={`px-4 py-2 border ${borderColor} rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2 ${textPrimary}`}
                >
                  <FiX className="w-4 h-4" />
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className={`p-4 border ${borderColor} rounded-lg ${bgSecondary}`}>
              {submission.grade !== null && submission.grade !== undefined ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className={`font-medium ${textPrimary}`}>Grade:</span>
                    <span className={`text-lg font-bold ${getGradeColor(getGradePercentage())}`}>
                      {submission.grade}/{maxGrade} ({getGradePercentage()}%)
                    </span>
                  </div>
                  
                  {submission.feedback && (
                    <div>
                      <span className={`font-medium ${textPrimary} block mb-2`}>Feedback:</span>
                      <div className={`whitespace-pre-wrap ${textPrimary}`}>
                        {submission.feedback}
                      </div>
                    </div>
                  )}
                  
                  {submission.graded_at && (
                    <div className={`text-xs ${textSecondary}`}>
                      Graded on: {new Date(submission.graded_at).toLocaleString()}
                    </div>
                  )}
                </div>
              ) : (
                <div className={`text-center py-4 ${textSecondary}`}>
                  <FiStar className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No grade assigned yet</p>
                  <button
                    onClick={handleStartGrading}
                    className="mt-2 text-blue-600 hover:text-blue-700 underline"
                  >
                    Grade this submission
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Submit Grade Confirmation Modal */}
      {showSubmitConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${bgPrimary} rounded-lg p-6 max-w-md w-full mx-4 border ${borderColor}`}>
            <h3 className={`text-lg font-semibold ${textPrimary} mb-4`}>
              Confirm Grade Submission
            </h3>
            <div className="space-y-3 mb-6">
              <p className={`${textSecondary}`}>
                Grade: <span className="font-medium">{grade}/{maxGrade} ({getGradePercentage()}%)</span>
              </p>
              {feedback && (
                <p className={`${textSecondary}`}>
                  Feedback: <span className="font-medium">Provided</span>
                </p>
              )}
              <p className={`text-sm ${textSecondary}`}>
                Are you sure you want to submit this grade? The student will be notified.
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleSubmitGrade}
                disabled={gradingState.loading}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
              >
                {gradingState.loading ? (
                  <FiLoader className="w-4 h-4 animate-spin" />
                ) : (
                  <FiCheck className="w-4 h-4" />
                )}
                Submit Grade
              </button>
              <button
                onClick={() => setShowSubmitConfirm(false)}
                className={`flex-1 px-4 py-2 border ${borderColor} rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${textPrimary}`}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskGrading;
