import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  submitTask,
  fetchStudentTaskSubmission,
  updateTaskStatus,
  selectCurrentSubmission,
  selectSubmissionLoading,
  selectSubmissionError,
  clearSubmissionState
} from '../../store/slices/TaskSlice';
import TaskAttachments from './TaskAttachments';
import {
  FiSend,
  FiClock,
  FiCheckCircle,
  FiAlertCircle,
  FiLoader,
  FiEdit3,
  FiSave,
  FiX,
  FiCalendar,
  FiUser,
  FiStar
} from 'react-icons/fi';

/**
 * TaskSubmission Component
 * Handles task submission for students with text responses and file attachments
 * 
 * Props:
 * - task: Task object with details
 * - studentId: ID of the current student
 * - onSubmissionUpdate: Callback when submission is updated
 * - className: Additional CSS classes
 */
const TaskSubmission = ({
  task,
  studentId,
  onSubmissionUpdate,
  className = ''
}) => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();

  // Redux state
  const currentSubmission = useSelector(selectCurrentSubmission);
  const submissionLoading = useSelector(selectSubmissionLoading);
  const submissionError = useSelector(selectSubmissionError);

  // Local state
  const [submissionText, setSubmissionText] = useState('');
  const [submissionNotes, setSubmissionNotes] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300';

  // Load existing submission on mount
  useEffect(() => {
    if (task?.id && studentId) {
      dispatch(fetchStudentTaskSubmission({
        task_id: task.id,
        student_id: studentId
      }));
    }

    return () => {
      dispatch(clearSubmissionState());
    };
  }, [dispatch, task?.id, studentId]);

  // Update local state when submission loads
  useEffect(() => {
    if (currentSubmission) {
      setSubmissionText(currentSubmission.submission_text || '');
      setSubmissionNotes(currentSubmission.submission_notes || '');
    }
  }, [currentSubmission]);

  // Check if task is past deadline
  const isPastDeadline = task?.deadline && new Date(task.deadline) < new Date();
  const canSubmit = !isPastDeadline || task?.accept_after_deadline;

  // Get submission status
  const getSubmissionStatus = () => {
    if (!currentSubmission) return 'not_submitted';
    return currentSubmission.status || 'submitted';
  };

  const submissionStatus = getSubmissionStatus();

  // Handle text submission
  const handleSubmit = async () => {
    if (!submissionText.trim()) {
      alert('Please enter your submission text');
      return;
    }

    try {
      const submissionData = {
        submission_text: submissionText.trim(),
        submission_notes: submissionNotes.trim() || undefined,
        submitted_at: new Date().toISOString()
      };

      await dispatch(submitTask({
        task_id: task.id,
        submission_data: submissionData
      })).unwrap();

      setIsEditing(false);
      setShowSubmitConfirm(false);
      
      if (onSubmissionUpdate) {
        onSubmissionUpdate();
      }
    } catch (error) {
      console.error('Submission failed:', error);
    }
  };

  // Handle edit mode
  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setSubmissionText(currentSubmission?.submission_text || '');
    setSubmissionNotes(currentSubmission?.submission_notes || '');
  };

  const handleSaveDraft = async () => {
    try {
      await dispatch(updateTaskStatus({
        task_id: task.id,
        status: 'in_progress',
        student_id: studentId
      })).unwrap();

      // Save as draft (you might want to add a separate API for this)
      setIsEditing(false);
    } catch (error) {
      console.error('Save draft failed:', error);
    }
  };

  // Status badge component
  const StatusBadge = ({ status }) => {
    const statusConfig = {
      not_submitted: {
        color: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400 border border-orange-200 dark:border-orange-800',
        icon: FiClock,
        text: 'Missing'
      },
      in_progress: {
        color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border border-blue-200 dark:border-blue-800',
        icon: FiEdit3,
        text: 'Draft'
      },
      submitted: {
        color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border border-green-200 dark:border-green-800',
        icon: FiCheckCircle,
        text: 'Turned in'
      },
      graded: {
        color: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 border border-purple-200 dark:border-purple-800',
        icon: FiStar,
        text: 'Graded'
      }
    };

    const config = statusConfig[status] || statusConfig.not_submitted;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium ${config.color}`}>
        <Icon className="w-4 h-4" />
        {config.text}
      </span>
    );
  };

  return (
    <div className={`${bgPrimary} rounded-lg border ${borderColor} ${className}`}>
      {/* Header */}
      <div className={`px-6 py-4 border-b ${borderColor} ${bgSecondary}`}>
        <div className="flex items-center justify-between">
          <div>
            <h3 className={`text-lg font-semibold ${textPrimary}`}>
              {submissionStatus === 'not_submitted' ? 'Submit Your Work' :
               submissionStatus === 'in_progress' ? 'Your Work in Progress' :
               submissionStatus === 'submitted' ? 'Your Submission' :
               'Your Graded Work'}
            </h3>
            <div className="flex items-center gap-4 mt-2">
              <StatusBadge status={submissionStatus} />

              {task?.deadline && (
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <FiCalendar className="w-4 h-4" />
                  <span>
                    Due: {new Date(task.deadline).toLocaleDateString()} at{' '}
                    {new Date(task.deadline).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                  {isPastDeadline && (
                    <span className="text-red-500 font-medium">(Overdue)</span>
                  )}
                </div>
              )}
            </div>
          </div>

          {currentSubmission?.grade && (
            <div className="text-right">
              <div className={`text-2xl font-bold ${textPrimary}`}>
                {currentSubmission.grade}
                {currentSubmission.max_grade && `/${currentSubmission.max_grade}`}
              </div>
              <div className={`text-sm ${textSecondary}`}>Grade</div>
            </div>
          )}
        </div>
      </div>

      {/* Error Display */}
      {submissionError && (
        <div className="px-6 py-3 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
          <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <FiAlertCircle className="w-4 h-4" />
            <span className="text-sm">
              {typeof submissionError === 'string'
                ? submissionError
                : submissionError?.detail || submissionError?.message || 'An error occurred'
              }
            </span>
          </div>
        </div>
      )}

      {/* Submission Content */}
      <div className="p-6 space-y-6">
        {/* Text Submission */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className={`block text-sm font-medium ${textPrimary}`}>
              Submission Text
            </label>
            
            {!isEditing && canSubmit && submissionStatus !== 'graded' && (
              <button
                onClick={handleEdit}
                className="text-blue-600 hover:text-blue-700 text-sm flex items-center gap-1"
              >
                <FiEdit3 className="w-4 h-4" />
                {submissionStatus === 'not_submitted' ? 'Start' : 'Edit'}
              </button>
            )}
          </div>

          {isEditing ? (
            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium ${textPrimary} mb-2`}>
                  Submission Content *
                </label>
                <textarea
                  value={submissionText}
                  onChange={(e) => setSubmissionText(e.target.value)}
                  placeholder="Enter your submission here..."
                  rows={8}
                  className={`w-full px-4 py-3 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary} placeholder-gray-400`}
                />
              </div>

              <div>
                <label className={`block text-sm font-medium ${textPrimary} mb-2`}>
                  Additional Notes (Optional)
                </label>
                <textarea
                  value={submissionNotes}
                  onChange={(e) => setSubmissionNotes(e.target.value)}
                  placeholder="Add any additional notes or comments..."
                  rows={3}
                  className={`w-full px-4 py-3 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary} placeholder-gray-400`}
                />
              </div>
              
              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-3">
                  <button
                    onClick={handleSaveDraft}
                    disabled={submissionLoading}
                    className={`px-4 py-2 border ${borderColor} rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 flex items-center gap-2 ${textPrimary}`}
                  >
                    <FiSave className="w-4 h-4" />
                    Save draft
                  </button>

                  <button
                    onClick={handleCancelEdit}
                    className={`px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors flex items-center gap-2`}
                  >
                    <FiX className="w-4 h-4" />
                    Cancel
                  </button>
                </div>

                <button
                  onClick={() => setShowSubmitConfirm(true)}
                  disabled={submissionLoading || !submissionText.trim()}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:bg-gray-400 flex items-center gap-2 font-medium"
                >
                  {submissionLoading ? (
                    <FiLoader className="w-4 h-4 animate-spin" />
                  ) : (
                    <FiSend className="w-4 h-4" />
                  )}
                  Turn in
                </button>
              </div>
            </div>
          ) : (
            <div className={`border ${borderColor} rounded-lg overflow-hidden`}>
              {submissionText ? (
                <div className={`p-6 ${bgPrimary}`}>
                  <div className={`whitespace-pre-wrap ${textPrimary} leading-relaxed`}>
                    {submissionText}
                  </div>
                  {submissionNotes && (
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <h5 className={`text-sm font-medium ${textSecondary} mb-2`}>Additional Notes:</h5>
                      <div className={`text-sm ${textSecondary} italic`}>
                        {submissionNotes}
                      </div>
                    </div>
                  )}
                  {canSubmit && submissionStatus !== 'graded' && (
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <button
                        onClick={handleEdit}
                        className="px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors flex items-center gap-2 text-sm"
                      >
                        <FiEdit3 className="w-4 h-4" />
                        Edit submission
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className={`text-center py-12 ${bgSecondary}`}>
                  <div className="max-w-sm mx-auto">
                    <FiEdit3 className={`w-12 h-12 mx-auto mb-4 ${textSecondary} opacity-50`} />
                    <h4 className={`text-lg font-medium ${textPrimary} mb-2`}>
                      {submissionStatus === 'not_submitted' ? 'Add your work' : 'No submission yet'}
                    </h4>
                    <p className={`text-sm ${textSecondary} mb-4`}>
                      {submissionStatus === 'not_submitted'
                        ? 'Create or upload your assignment to turn it in'
                        : 'You haven\'t submitted anything for this assignment yet'
                      }
                    </p>
                    {canSubmit && (
                      <button
                        onClick={handleEdit}
                        className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 mx-auto"
                      >
                        <FiEdit3 className="w-4 h-4" />
                        {submissionStatus === 'not_submitted' ? 'Add work' : 'Start submission'}
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* File Attachments */}
        <TaskAttachments
          taskId={task?.id}
          attachmentType="submission"
          canUpload={canSubmit && submissionStatus !== 'graded'}
          canDelete={canSubmit && submissionStatus !== 'graded'}
          maxFileSize={25}
          allowedTypes={['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'zip']}
        />

        {/* Teacher Feedback */}
        {currentSubmission?.feedback && (
          <div className="border-t pt-6">
            <h4 className={`text-sm font-medium ${textPrimary} mb-3 flex items-center gap-2`}>
              <FiUser className="w-4 h-4" />
              Teacher Feedback
            </h4>
            <div className={`p-4 border ${borderColor} rounded-lg ${bgSecondary}`}>
              <div className={`whitespace-pre-wrap ${textPrimary}`}>
                {currentSubmission.feedback}
              </div>
              {currentSubmission.feedback_date && (
                <div className={`text-xs ${textSecondary} mt-2`}>
                  {new Date(currentSubmission.feedback_date).toLocaleString()}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Submit Confirmation Modal */}
      {showSubmitConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${bgPrimary} rounded-lg p-6 max-w-md w-full mx-4 border ${borderColor}`}>
            <h3 className={`text-lg font-semibold ${textPrimary} mb-4`}>
              Turn in assignment?
            </h3>
            <p className={`${textSecondary} mb-6`}>
              Are you sure you want to turn in this assignment? You may not be able to edit it after turning it in.
            </p>
            <div className="flex items-center gap-3">
              <button
                onClick={handleSubmit}
                disabled={submissionLoading}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center justify-center gap-2 font-medium"
              >
                {submissionLoading ? (
                  <FiLoader className="w-4 h-4 animate-spin" />
                ) : (
                  <FiCheckCircle className="w-4 h-4" />
                )}
                Turn in
              </button>
              <button
                onClick={() => setShowSubmitConfirm(false)}
                className={`flex-1 px-4 py-2 border ${borderColor} rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${textPrimary}`}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskSubmission;
