import React, { useState, useCallback } from 'react';

const ExamFormTest = () => {
  const [exam, setExam] = useState({
    title: '',
    description: '',
    total_duration: 0
  });

  const handleExamChange = useCallback((e) => {
    const { name, value } = e.target;
    setExam((prev) => ({ ...prev, [name]: value }));
  }, []);

  return (
    <div className="min-h-screen py-8 px-8 bg-white">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-violet-700 text-center">Exam Form Test</h1>
        
        <div className="space-y-6">
          <div>
            <label className="block mb-2 font-medium text-gray-700">
              Exam Title
            </label>
            <input
              key="exam-title-test"
              type="text"
              name="title"
              value={exam.title}
              onChange={handleExamChange}
              className="w-full rounded-lg px-4 py-3 border-2 border-gray-300 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200"
              placeholder="Enter exam title"
            />
          </div>

          <div>
            <label className="block mb-2 font-medium text-gray-700">
              Description
            </label>
            <textarea
              key="exam-description-test"
              name="description"
              value={exam.description}
              onChange={handleExamChange}
              rows={3}
              className="w-full rounded-lg px-4 py-3 border-2 border-gray-300 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200"
              placeholder="Enter description"
            />
          </div>

          <div>
            <label className="block mb-2 font-medium text-gray-700">
              Duration (minutes)
            </label>
            <input
              key="exam-duration-test"
              type="number"
              name="total_duration"
              value={exam.total_duration}
              onChange={handleExamChange}
              min={1}
              max={480}
              className="w-full rounded-lg px-4 py-3 border-2 border-gray-300 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all duration-200"
              placeholder="e.g., 60"
            />
          </div>
        </div>

        <div className="mt-6 p-4 bg-gray-100 rounded-lg">
          <h3 className="font-medium mb-2">Current Values:</h3>
          <pre className="text-sm">{JSON.stringify(exam, null, 2)}</pre>
        </div>
      </div>
    </div>
  );
};

export default ExamFormTest;
