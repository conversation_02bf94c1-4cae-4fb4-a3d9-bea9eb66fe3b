import React, { useState } from 'react';
import MentorSearchDropdown from '../ui/MentorSearchDropdown';

const MentorDropdownTest = () => {
  const [selectedMentorId, setSelectedMentorId] = useState('');

  return (
    <div className="p-8 max-w-md mx-auto">
      <h2 className="text-xl font-bold mb-4">Mentor Search Dropdown Test</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select a Mentor:
          </label>
          <MentorSearchDropdown
            value={selectedMentorId}
            onChange={setSelectedMentorId}
            placeholder="Search and select a mentor..."
          />
        </div>
        
        {selectedMentorId && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-800">
              Selected Mentor ID: <strong>{selectedMentorId}</strong>
            </p>
          </div>
        )}
        
        <div className="text-xs text-gray-500">
          <p>This component:</p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Fetches mentors from /api/mentors/public</li>
            <li>Provides real-time search functionality</li>
            <li>Shows mentor profiles with ratings and experience</li>
            <li>Displays verification status</li>
            <li>Returns the selected mentor ID</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default MentorDropdownTest;
