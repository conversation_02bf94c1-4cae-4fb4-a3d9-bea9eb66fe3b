import React, { useState, useMemo } from 'react';
import { 
  FiChevronUp, 
  FiChevronDown, 
  FiMoreHorizontal, 
  FiDownload,
  FiFilter,
  FiSearch,
  FiCheck,
  FiX
} from 'react-icons/fi';
import SkeletonLoader from './SkeletonLoader';
import ErrorStates from './ErrorStates';
import { useUX } from '../../providers/UXProvider';

// Table header component
const TableHeader = ({ 
  column, 
  sortBy, 
  sortDirection, 
  onSort, 
  className = '' 
}) => {
  const canSort = column.sortable !== false;
  const isSorted = sortBy === column.key;
  
  return (
    <th 
      className={`
        px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider
        ${canSort ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 select-none' : ''}
        ${className}
      `}
      onClick={canSort ? () => onSort(column.key) : undefined}
    >
      <div className="flex items-center space-x-1">
        <span>{column.label}</span>
        {canSort && (
          <div className="flex flex-col">
            <FiChevronUp 
              className={`w-3 h-3 ${
                isSorted && sortDirection === 'asc' 
                  ? 'text-violet-600 dark:text-violet-400' 
                  : 'text-gray-300'
              }`} 
            />
            <FiChevronDown 
              className={`w-3 h-3 -mt-1 ${
                isSorted && sortDirection === 'desc' 
                  ? 'text-violet-600 dark:text-violet-400' 
                  : 'text-gray-300'
              }`} 
            />
          </div>
        )}
      </div>
    </th>
  );
};

// Mobile card view for responsive tables
const MobileCard = ({ item, columns, actions, onRowClick }) => {
  return (
    <div 
      className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 space-y-3 cursor-pointer hover:shadow-md transition-shadow"
      onClick={() => onRowClick?.(item)}
    >
      {columns.map((column) => {
        if (column.hideOnMobile) return null;
        
        const value = column.render 
          ? column.render(item[column.key], item)
          : item[column.key];
          
        return (
          <div key={column.key} className="flex justify-between items-start">
            <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
              {column.label}:
            </span>
            <span className="text-sm text-gray-900 dark:text-gray-100 text-right flex-1 ml-2">
              {value}
            </span>
          </div>
        );
      })}
      
      {actions && actions.length > 0 && (
        <div className="flex justify-end space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700">
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={(e) => {
                e.stopPropagation();
                action.onClick(item);
              }}
              className={`btn btn-sm ${action.className || 'bg-gray-100 hover:bg-gray-200 text-gray-700'}`}
              disabled={action.disabled?.(item)}
            >
              {action.icon && <action.icon className="w-4 h-4 mr-1" />}
              {action.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

// Main DataTable component
const DataTable = ({
  data = [],
  columns = [],
  loading = false,
  error = null,
  onRetry,
  
  // Sorting
  sortable = true,
  defaultSort = null,
  onSort,
  
  // Pagination
  pagination = true,
  pageSize = 10,
  currentPage = 1,
  totalItems = 0,
  onPageChange,
  
  // Selection
  selectable = false,
  selectedItems = [],
  onSelectionChange,
  
  // Actions
  actions = [],
  bulkActions = [],
  
  // Filtering
  filterable = false,
  filters = {},
  onFilterChange,
  
  // Export
  exportable = false,
  onExport,
  
  // Row interactions
  onRowClick,
  
  // Styling
  className = '',
  emptyState,
  
  // Mobile
  mobileBreakpoint = 768
}) => {
  const { utils } = useUX();
  const [sortBy, setSortBy] = useState(defaultSort?.key || null);
  const [sortDirection, setSortDirection] = useState(defaultSort?.direction || 'asc');
  const [showFilters, setShowFilters] = useState(false);
  const [isMobileView, setIsMobileView] = useState(false);
  
  // Check if mobile view should be used
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobileView(window.innerWidth < mobileBreakpoint);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [mobileBreakpoint]);
  
  // Handle sorting
  const handleSort = (columnKey) => {
    if (!sortable) return;
    
    let newDirection = 'asc';
    if (sortBy === columnKey && sortDirection === 'asc') {
      newDirection = 'desc';
    }
    
    setSortBy(columnKey);
    setSortDirection(newDirection);
    
    if (onSort) {
      onSort(columnKey, newDirection);
    }
  };
  
  // Sort data locally if no external sorting
  const sortedData = useMemo(() => {
    if (!sortBy || onSort) return data;
    
    return [...data].sort((a, b) => {
      const aVal = a[sortBy];
      const bVal = b[sortBy];
      
      if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [data, sortBy, sortDirection, onSort]);
  
  // Handle selection
  const handleSelectAll = (checked) => {
    if (!selectable || !onSelectionChange) return;
    
    if (checked) {
      onSelectionChange(sortedData.map(item => item.id));
    } else {
      onSelectionChange([]);
    }
  };
  
  const handleSelectItem = (itemId, checked) => {
    if (!selectable || !onSelectionChange) return;
    
    if (checked) {
      onSelectionChange([...selectedItems, itemId]);
    } else {
      onSelectionChange(selectedItems.filter(id => id !== itemId));
    }
  };
  
  const isAllSelected = selectedItems.length === sortedData.length && sortedData.length > 0;
  const isIndeterminate = selectedItems.length > 0 && selectedItems.length < sortedData.length;
  
  // Loading state
  if (loading) {
    return (
      <div className={className}>
        <SkeletonLoader type="table" rows={pageSize} columns={columns.length} />
      </div>
    );
  }
  
  // Error state
  if (error) {
    return (
      <div className={className}>
        <ErrorStates.Retryable
          title="Failed to Load Data"
          message={typeof error === 'string' ? error : 'We encountered an error while loading the data.'}
          onRetry={onRetry}
          retryText="Reload Data"
        />
      </div>
    );
  }
  
  // Empty state
  if (sortedData.length === 0) {
    return (
      <div className={className}>
        {emptyState || (
          <ErrorStates.Empty
            title="No data found"
            description="There are no items to display."
          />
        )}
      </div>
    );
  }
  
  // Mobile view
  if (isMobileView) {
    return (
      <div className={`space-y-4 ${className}`}>
        {/* Mobile header with actions */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {sortedData.length} items
          </span>
          
          <div className="flex items-center space-x-2">
            {filterable && (
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="btn btn-sm bg-gray-100 hover:bg-gray-200 text-gray-700"
              >
                <FiFilter className="w-4 h-4" />
              </button>
            )}
            
            {exportable && (
              <button
                onClick={onExport}
                className="btn btn-sm bg-gray-100 hover:bg-gray-200 text-gray-700"
              >
                <FiDownload className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
        
        {/* Mobile cards */}
        <div className="space-y-3">
          {sortedData.map((item, index) => (
            <MobileCard
              key={item.id || index}
              item={item}
              columns={columns}
              actions={actions}
              onRowClick={onRowClick}
            />
          ))}
        </div>
      </div>
    );
  }
  
  // Desktop table view
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden ${className}`}>
      {/* Table header with actions */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {selectable && selectedItems.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {selectedItems.length} selected
                </span>
                
                {bulkActions.map((action, index) => (
                  <button
                    key={index}
                    onClick={() => action.onClick(selectedItems)}
                    className={`btn btn-sm ${action.className || 'bg-violet-600 hover:bg-violet-700 text-white'}`}
                  >
                    {action.icon && <action.icon className="w-4 h-4 mr-1" />}
                    {action.label}
                  </button>
                ))}
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {filterable && (
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="btn btn-sm bg-gray-100 hover:bg-gray-200 text-gray-700"
              >
                <FiFilter className="w-4 h-4 mr-1" />
                Filters
              </button>
            )}
            
            {exportable && (
              <button
                onClick={onExport}
                className="btn btn-sm bg-gray-100 hover:bg-gray-200 text-gray-700"
              >
                <FiDownload className="w-4 h-4 mr-1" />
                Export
              </button>
            )}
          </div>
        </div>
      </div>
      
      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              {selectable && (
                <th className="px-6 py-3 w-12">
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    ref={(el) => {
                      if (el) el.indeterminate = isIndeterminate;
                    }}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="form-checkbox h-4 w-4 text-violet-600"
                  />
                </th>
              )}
              
              {columns.map((column) => (
                <TableHeader
                  key={column.key}
                  column={column}
                  sortBy={sortBy}
                  sortDirection={sortDirection}
                  onSort={handleSort}
                />
              ))}
              
              {actions.length > 0 && (
                <th className="px-6 py-3 w-20">
                  <span className="sr-only">Actions</span>
                </th>
              )}
            </tr>
          </thead>
          
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {sortedData.map((item, index) => (
              <tr 
                key={item.id || index}
                className={`
                  hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors
                  ${onRowClick ? 'cursor-pointer' : ''}
                  ${selectedItems.includes(item.id) ? 'bg-violet-50 dark:bg-violet-900/20' : ''}
                `}
                onClick={() => onRowClick?.(item)}
              >
                {selectable && (
                  <td className="px-6 py-4 w-12">
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(item.id)}
                      onChange={(e) => {
                        e.stopPropagation();
                        handleSelectItem(item.id, e.target.checked);
                      }}
                      className="form-checkbox h-4 w-4 text-violet-600"
                    />
                  </td>
                )}
                
                {columns.map((column) => (
                  <td key={column.key} className="px-6 py-4 whitespace-nowrap">
                    {column.render 
                      ? column.render(item[column.key], item)
                      : item[column.key]
                    }
                  </td>
                ))}
                
                {actions.length > 0 && (
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      {actions.map((action, actionIndex) => (
                        <button
                          key={actionIndex}
                          onClick={(e) => {
                            e.stopPropagation();
                            action.onClick(item);
                          }}
                          className={`btn btn-sm ${action.className || 'bg-gray-100 hover:bg-gray-200 text-gray-700'}`}
                          disabled={action.disabled?.(item)}
                          title={action.label}
                        >
                          {action.icon && <action.icon className="w-4 h-4" />}
                          {action.showLabel && action.label}
                        </button>
                      ))}
                    </div>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Pagination */}
      {pagination && (
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalItems)} of {totalItems} results
            </span>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => onPageChange?.(currentPage - 1)}
                disabled={currentPage === 1}
                className="btn btn-sm bg-gray-100 hover:bg-gray-200 text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Page {currentPage} of {Math.ceil(totalItems / pageSize)}
              </span>
              
              <button
                onClick={() => onPageChange?.(currentPage + 1)}
                disabled={currentPage >= Math.ceil(totalItems / pageSize)}
                className="btn btn-sm bg-gray-100 hover:bg-gray-200 text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataTable;
