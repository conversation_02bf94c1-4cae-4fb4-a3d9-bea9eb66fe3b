import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { logout } from '../../store/slices/LoginSlice';
import { fetchCurrentUser } from '../../store/slices/userSlice';
import Transition from './Transition';
import { FiUser } from 'react-icons/fi';

import UserAvatar from '../../assets/images/user-avatar-32.png';

function DropdownProfile({
  align
}) {

  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentUser } = useSelector((state) => state.users);

  const trigger = useRef(null);
  const dropdown = useRef(null);

  // Get user data from localStorage
  const userdataString = localStorage.getItem("userdata");
  const userdata = userdataString ? JSON.parse(userdataString) : null;

  // Get user role from localStorage (fallback)
  const userRole = localStorage.getItem("role") || userdata?.user_type || 'User';

  // Get display name from userdata, but only if it matches current role
  const userName = (userdata && userdata.user_type === userRole)
    ? (userdata.username || userdata.email || userRole)
    : userRole;

  // Capitalize first letter of role for display
  const displayRole = userRole.charAt(0).toUpperCase() + userRole.slice(1);

  // Fetch current user data if not available
  useEffect(() => {
    if (!currentUser) {
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, currentUser]);

  // Clear userdata if role doesn't match (indicating user switch)
  useEffect(() => {
    if (userdata && userdata.user_type !== userRole) {
      localStorage.removeItem("userdata");
    }
  }, [userdata, userRole]);

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    setDropdownOpen(false);
    navigate('/Login');
  };

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }) => {
      if (!dropdown.current) return;
      if (!dropdownOpen || dropdown.current.contains(target) || trigger.current.contains(target)) return;
      setDropdownOpen(false);
    };
    document.addEventListener('click', clickHandler);
    return () => document.removeEventListener('click', clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener('keydown', keyHandler);
    return () => document.removeEventListener('keydown', keyHandler);
  });

  return (
    <div className="relative inline-flex">
      <button
        ref={trigger}
        className="inline-flex justify-center items-center group"
        aria-haspopup="true"
        onClick={() => setDropdownOpen(!dropdownOpen)}
        aria-expanded={dropdownOpen}
      >
        {(() => {
          // Priority: 1. Image data objects, 2. URL fallback, 3. Default avatar
          const imageData = currentUser?.profile_picture_data;
          const thumbnailData = imageData?.thumbnail;
          const fullImageData = imageData?.full_image;
          const urlFallback = currentUser?.profile_picture_thumbnail_url || currentUser?.profile_picture_url;

          const imageSrc = thumbnailData?.data_url || fullImageData?.data_url || urlFallback;

          return imageSrc ? (
            <img
              className="w-8 h-8 rounded-full object-cover"
              src={imageSrc}
              width="32"
              height="32"
              alt="User Profile"
              onError={(e) => {
                e.target.src = UserAvatar;
              }}
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-violet-100 dark:bg-violet-900/30 flex items-center justify-center">
              <FiUser className="w-4 h-4 text-violet-600 dark:text-violet-400" />
            </div>
          );
        })()}
        <div className="flex items-center truncate">
          <span className="truncate ml-2 text-sm font-medium text-gray-600 dark:text-gray-100 group-hover:text-gray-800 dark:group-hover:text-white">{userName}</span>
          <svg className="w-3 h-3 shrink-0 ml-1 fill-current text-gray-400 dark:text-gray-500" viewBox="0 0 12 12">
            <path d="M5.9 11.4L.5 6l1.4-1.4 4 4 4-4L11.3 6z" />
          </svg>
        </div>
      </button>

      <Transition
        className={`origin-top-right z-10 absolute top-full min-w-44 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700/60 py-1.5 rounded-lg shadow-lg overflow-hidden mt-1 ${align === 'right' ? 'right-0' : 'left-0'}`}
        show={dropdownOpen}
        enter="transition ease-out duration-200 transform"
        enterStart="opacity-0 -translate-y-2"
        enterEnd="opacity-100 translate-y-0"
        leave="transition ease-out duration-200"
        leaveStart="opacity-100"
        leaveEnd="opacity-0"
      >
        <div
          ref={dropdown}
          onFocus={() => setDropdownOpen(true)}
          onBlur={() => setDropdownOpen(false)}
        >
          <div className="pt-0.5 pb-2 px-3 mb-1 border-b border-gray-200 dark:border-gray-700/60">
            <div className="font-medium text-gray-800 dark:text-gray-100">{userName}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400 italic">{displayRole}</div>
          </div>
          <ul>
            <li>
              <Link
                className="font-medium text-sm text-violet-500 hover:text-violet-600 dark:hover:text-violet-400 flex items-center py-1 px-3"
                to={`/${userRole}/settings`}
                onClick={() => setDropdownOpen(!dropdownOpen)}
              >
                Settings
              </Link>
            </li>
            <li>
              <button
                className="font-medium text-sm text-violet-500 hover:text-violet-600 dark:hover:text-violet-400 flex items-center py-1 px-3 w-full text-left"
                onClick={handleLogout}
              >
                Sign Out
              </button>
            </li>
          </ul>
        </div>
      </Transition>
    </div>
  )
}

export default DropdownProfile;