import React from 'react';
import { FiAlertCircle, FiRefreshCw } from 'react-icons/fi';

const ErrorMessage = ({ 
  error, 
  title = 'Something went wrong',
  description,
  onRetry,
  retryText = 'Try again',
  fullScreen = false,
  currentTheme = 'light'
}) => {
  // Theme classes
  const bgPrimary = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  // Format error message
  const errorMessage = typeof error === 'object' 
    ? error?.detail || error?.message || JSON.stringify(error) 
    : error;

  const errorContent = (
    <div className={`${bgSecondary} rounded-lg p-8 border ${borderColor} text-center max-w-md mx-auto`}>
      <div className="flex justify-center mb-4">
        <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-full">
          <FiAlertCircle className="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>
      </div>
      
      <h2 className={`text-xl font-semibold ${textPrimary} mb-2`}>
        {title}
      </h2>
      
      {description && (
        <p className={`${textSecondary} mb-4`}>
          {description}
        </p>
      )}
      
      {errorMessage && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4">
          <p className="text-red-700 dark:text-red-400 text-sm">
            {errorMessage}
          </p>
        </div>
      )}
      
      {onRetry && (
        <button
          onClick={onRetry}
          className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <FiRefreshCw className="w-4 h-4" />
          <span>{retryText}</span>
        </button>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className={`min-h-screen ${bgPrimary} flex items-center justify-center p-4`}>
        {errorContent}
      </div>
    );
  }

  return errorContent;
};

export default ErrorMessage;
