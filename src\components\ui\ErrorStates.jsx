import React from 'react';
import {
  FiAlertTriangle,
  FiWifi,
  FiRefreshCw,
  FiHome,
  FiArrowLeft,
  FiMail,
  FiHelpCircle,
  FiX,
  FiAlertCircle,
  FiCheckCircle,
  FiInfo
} from 'react-icons/fi';

// Base error component
const ErrorBase = ({ 
  children, 
  className = '',
  variant = 'default' 
}) => {
  const variants = {
    default: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',
    warning: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800',
    info: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800',
    success: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
  };

  return (
    <div className={`rounded-lg border p-4 ${variants[variant]} ${className}`}>
      {children}
    </div>
  );
};

// Inline error message
export const InlineError = ({ 
  message, 
  className = '',
  variant = 'default',
  icon = true,
  dismissible = false,
  onDismiss
}) => {
  const icons = {
    default: FiAlertCircle,
    warning: FiAlertTriangle,
    info: FiInfo,
    success: FiCheckCircle
  };

  const textColors = {
    default: 'text-red-700 dark:text-red-400',
    warning: 'text-yellow-700 dark:text-yellow-400',
    info: 'text-blue-700 dark:text-blue-400',
    success: 'text-green-700 dark:text-green-400'
  };

  const IconComponent = icons[variant];

  return (
    <ErrorBase variant={variant} className={className}>
      <div className="flex items-start">
        {icon && (
          <IconComponent className={`mr-3 mt-0.5 flex-shrink-0 ${textColors[variant]}`} size={16} />
        )}
        <p className={`text-sm ${textColors[variant]} flex-1`}>
          {message}
        </p>
        {dismissible && (
          <button
            onClick={onDismiss}
            className={`ml-3 flex-shrink-0 ${textColors[variant]} hover:opacity-70`}
          >
            <FiX size={16} />
          </button>
        )}
      </div>
    </ErrorBase>
  );
};

// Retryable error component
export const RetryableError = ({
  title = "Something went wrong",
  message = "We encountered an error while loading this content.",
  onRetry,
  retryText = "Try Again",
  className = '',
  showSupport = true
}) => (
  <ErrorBase className={`text-center py-8 ${className}`}>
    <div className="flex flex-col items-center">
      <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-4">
        <FiAlertTriangle className="text-red-600 dark:text-red-400" size={32} />
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
        {title}
      </h3>
      
      <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
        {message}
      </p>
      
      <div className="flex flex-col sm:flex-row gap-3">
        {onRetry && (
          <button
            onClick={onRetry}
            className="btn bg-red-600 hover:bg-red-700 text-white min-h-[44px] px-6"
          >
            <FiRefreshCw className="w-4 h-4 mr-2" />
            {retryText}
          </button>
        )}
        
        {showSupport && (
          <button className="btn bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 min-h-[44px] px-6">
            <FiMail className="w-4 h-4 mr-2" />
            Contact Support
          </button>
        )}
      </div>
    </div>
  </ErrorBase>
);

// Network error component
export const NetworkError = ({
  onRetry,
  className = ''
}) => (
  <ErrorBase className={`text-center py-8 ${className}`}>
    <div className="flex flex-col items-center">
      <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-4">
        <FiWifi className="text-red-600 dark:text-red-400" size={32} />
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
        Connection Problem
      </h3>
      
      <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
        Please check your internet connection and try again.
      </p>
      
      <button
        onClick={onRetry}
        className="btn bg-red-600 hover:bg-red-700 text-white min-h-[44px] px-6"
      >
        <FiRefreshCw className="w-4 h-4 mr-2" />
        Retry Connection
      </button>
    </div>
  </ErrorBase>
);

// 404 Not Found component
export const NotFoundError = ({
  title = "Page Not Found",
  message = "The page you're looking for doesn't exist or has been moved.",
  onGoHome,
  onGoBack,
  className = ''
}) => (
  <div className={`text-center py-12 ${className}`}>
    <div className="flex flex-col items-center">
      <div className="text-6xl font-bold text-gray-300 dark:text-gray-600 mb-4">
        404
      </div>
      
      <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
        {title}
      </h1>
      
      <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-md">
        {message}
      </p>
      
      <div className="flex flex-col sm:flex-row gap-3">
        {onGoHome && (
          <button
            onClick={onGoHome}
            className="btn bg-violet-600 hover:bg-violet-700 text-white min-h-[44px] px-6"
          >
            <FiHome className="w-4 h-4 mr-2" />
            Go Home
          </button>
        )}
        
        {onGoBack && (
          <button
            onClick={onGoBack}
            className="btn bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 min-h-[44px] px-6"
          >
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </button>
        )}
      </div>
    </div>
  </div>
);

// Permission denied component
export const PermissionError = ({
  title = "Access Denied",
  message = "You don't have permission to access this resource.",
  onGoBack,
  onContactAdmin,
  className = ''
}) => (
  <ErrorBase variant="warning" className={`text-center py-8 ${className}`}>
    <div className="flex flex-col items-center">
      <div className="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mb-4">
        <FiAlertTriangle className="text-yellow-600 dark:text-yellow-400" size={32} />
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
        {title}
      </h3>
      
      <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
        {message}
      </p>
      
      <div className="flex flex-col sm:flex-row gap-3">
        {onGoBack && (
          <button
            onClick={onGoBack}
            className="btn bg-yellow-600 hover:bg-yellow-700 text-white min-h-[44px] px-6"
          >
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </button>
        )}
        
        {onContactAdmin && (
          <button
            onClick={onContactAdmin}
            className="btn bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 min-h-[44px] px-6"
          >
            <FiHelpCircle className="w-4 h-4 mr-2" />
            Contact Admin
          </button>
        )}
      </div>
    </div>
  </ErrorBase>
);

// Form validation error
export const FormError = ({
  field,
  message,
  suggestion,
  className = ''
}) => (
  <div className={`mt-1 ${className}`}>
    <p className="text-sm text-red-600 dark:text-red-400">
      {message}
    </p>
    {suggestion && (
      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
        {suggestion}
      </p>
    )}
  </div>
);

// Empty state component
export const EmptyState = ({
  icon: IconComponent = FiHelpCircle,
  title = "No data found",
  description = "There's nothing to show here yet.",
  action,
  className = ''
}) => (
  <div className={`text-center py-12 ${className}`}>
    <div className="flex flex-col items-center">
      <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
        <IconComponent className="text-gray-400 dark:text-gray-500" size={32} />
      </div>
      
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        {title}
      </h3>
      
      <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md">
        {description}
      </p>
      
      {action}
    </div>
  </div>
);

// Error boundary fallback
export const ErrorBoundaryFallback = ({
  error,
  resetError,
  className = ''
}) => (
  <div className={`min-h-screen flex items-center justify-center p-4 ${className}`}>
    <ErrorBase className="max-w-md w-full text-center py-8">
      <div className="flex flex-col items-center">
        <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-4">
          <FiAlertTriangle className="text-red-600 dark:text-red-400" size={32} />
        </div>
        
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
          Something went wrong
        </h2>
        
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          An unexpected error occurred. Please try refreshing the page.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={resetError}
            className="btn bg-red-600 hover:bg-red-700 text-white min-h-[44px] px-6"
          >
            <FiRefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="btn bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 min-h-[44px] px-6"
          >
            Refresh Page
          </button>
        </div>
        
        {import.meta.env.MODE === 'development' && error && (
          <details className="mt-6 text-left w-full">
            <summary className="text-sm text-gray-500 cursor-pointer">
              Error Details (Development)
            </summary>
            <pre className="mt-2 text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-auto">
              {error.toString()}
            </pre>
          </details>
        )}
      </div>
    </ErrorBase>
  </div>
);

export default {
  Inline: InlineError,
  Retryable: RetryableError,
  Network: NetworkError,
  NotFound: NotFoundError,
  Permission: PermissionError,
  Form: FormError,
  Empty: EmptyState,
  Boundary: ErrorBoundaryFallback
};
