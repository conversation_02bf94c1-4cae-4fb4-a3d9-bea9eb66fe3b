import React from 'react';

const LoadingSpinner = ({ 
  size = 'md', 
  color = 'blue', 
  text = 'Loading...', 
  fullScreen = false,
  currentTheme = 'light'
}) => {
  // Size classes
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-12 w-12',
    lg: 'h-16 w-16',
    xl: 'h-24 w-24'
  };

  // Color classes
  const colorClasses = {
    blue: 'border-blue-600',
    violet: 'border-violet-600',
    green: 'border-green-600',
    red: 'border-red-600',
    gray: 'border-gray-600'
  };

  // Theme classes
  const bgPrimary = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";

  const spinnerContent = (
    <div className="text-center">
      <div className={`animate-spin rounded-full ${sizeClasses[size]} border-b-2 ${colorClasses[color]} mx-auto mb-4`}></div>
      {text && (
        <p className={`${textSecondary} text-sm`}>{text}</p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className={`min-h-screen ${bgPrimary} flex items-center justify-center`}>
        {spinnerContent}
      </div>
    );
  }

  return spinnerContent;
};

export default LoadingSpinner;
