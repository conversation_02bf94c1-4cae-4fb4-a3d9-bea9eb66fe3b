import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiSearch, FiChevronDown, FiUser, FiStar, FiMapPin, FiCheckCircle } from 'react-icons/fi';
import { fetchPublicMentors } from '../../store/slices/MentorsSlice';

const MentorSearchDropdown = ({ 
  value, 
  onChange, 
  placeholder = "Search and select a mentor...",
  className = "",
  error = null,
  disabled = false 
}) => {
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMentor, setSelectedMentor] = useState(null);
  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);

  // Get mentors data from Redux store
  const { 
    publicMentors, 
    publicMentorsLoading, 
    publicMentorsError 
  } = useSelector(state => state.mentors);

  // Fetch mentors when component mounts or search term changes
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      dispatch(fetchPublicMentors({
        search: searchTerm,
        verified_only: true,
        page: 1,
        size: 20
      }));
    }, 300); // Debounce search

    return () => clearTimeout(delayedSearch);
  }, [dispatch, searchTerm]);

  // Handle clicking outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Find selected mentor from the list
  useEffect(() => {
    if (value && publicMentors?.mentors) {
      const mentor = publicMentors.mentors.find(m => m.id === value);
      setSelectedMentor(mentor);
    } else {
      setSelectedMentor(null);
    }
  }, [value, publicMentors]);

  const handleMentorSelect = (mentor) => {
    setSelectedMentor(mentor);
    onChange(mentor.id);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleDropdownToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setTimeout(() => searchInputRef.current?.focus(), 100);
      }
    }
  };

  const filteredMentors = publicMentors?.mentors || [];

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Selected Mentor Display / Trigger */}
      <div
        onClick={handleDropdownToggle}
        className={`
          w-full px-3 py-2 border rounded-md cursor-pointer transition-colors duration-200
          ${error 
            ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
            : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
          }
          ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white hover:border-gray-400'}
          ${isOpen ? 'ring-2 ring-blue-500 ring-opacity-20' : ''}
        `}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center flex-1 min-w-0">
            {selectedMentor ? (
              <>
                <div className="flex-shrink-0 w-8 h-8 mr-3">
                  {selectedMentor.profile_image_url ? (
                    <img
                      src={selectedMentor.profile_image_url}
                      alt={selectedMentor.full_name}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <FiUser className="w-4 h-4 text-gray-500" />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 truncate">
                      {selectedMentor.full_name}
                    </span>
                    {selectedMentor.is_verified && (
                      <FiCheckCircle className="w-4 h-4 text-green-500 ml-1 flex-shrink-0" />
                    )}
                  </div>
                  <div className="flex items-center text-xs text-gray-500">
                    <FiStar className="w-3 h-3 mr-1" />
                    <span>{selectedMentor.rating || 'N/A'}</span>
                    <span className="mx-1">•</span>
                    <span>{selectedMentor.experience_years} years</span>
                  </div>
                </div>
              </>
            ) : (
              <span className="text-gray-500">{placeholder}</span>
            )}
          </div>
          <FiChevronDown 
            className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
              isOpen ? 'transform rotate-180' : ''
            }`} 
          />
        </div>
      </div>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden">
          {/* Search Input */}
          <div className="p-3 border-b border-gray-200">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search mentors..."
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
          </div>

          {/* Mentors List */}
          <div className="max-h-60 overflow-y-auto">
            {publicMentorsLoading ? (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
                <span className="mt-2 block text-sm">Loading mentors...</span>
              </div>
            ) : publicMentorsError ? (
              <div className="p-4 text-center text-red-500 text-sm">
                Error loading mentors: {publicMentorsError}
              </div>
            ) : filteredMentors.length === 0 ? (
              <div className="p-4 text-center text-gray-500 text-sm">
                {searchTerm ? 'No mentors found matching your search.' : 'No mentors available.'}
              </div>
            ) : (
              filteredMentors.map((mentor) => (
                <div
                  key={mentor.id}
                  onClick={() => handleMentorSelect(mentor)}
                  className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-10 h-10 mr-3">
                      {mentor.profile_image_url ? (
                        <img
                          src={mentor.profile_image_url}
                          alt={mentor.full_name}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                          <FiUser className="w-5 h-5 text-gray-500" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-gray-900 truncate">
                          {mentor.full_name}
                        </span>
                        {mentor.is_verified && (
                          <FiCheckCircle className="w-4 h-4 text-green-500 ml-1 flex-shrink-0" />
                        )}
                      </div>
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <FiStar className="w-3 h-3 mr-1" />
                        <span>{mentor.rating || 'N/A'}</span>
                        <span className="mx-1">•</span>
                        <span>{mentor.experience_years} years</span>
                        {mentor.country && (
                          <>
                            <span className="mx-1">•</span>
                            <FiMapPin className="w-3 h-3 mr-1" />
                            <span>{mentor.country}</span>
                          </>
                        )}
                      </div>
                      {mentor.expertise_areas && mentor.expertise_areas.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {mentor.expertise_areas.slice(0, 3).map((area, index) => (
                            <span
                              key={index}
                              className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                            >
                              {area}
                            </span>
                          ))}
                          {mentor.expertise_areas.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{mentor.expertise_areas.length - 3} more
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

export default MentorSearchDropdown;
