import React from 'react';
import { FiChevronLeft, FiChevronRight, FiMoreHorizontal } from 'react-icons/fi';

/**
 * Reusable Pagination Component
 * Supports both traditional page-based pagination and load-more style pagination
 */
const Pagination = ({
  // For traditional pagination
  currentPage = 1,
  totalPages = 1,
  onPageChange,
  
  // For load-more pagination
  hasMore = false,
  loading = false,
  onLoadMore,
  loadMoreText = "Load More",
  
  // Common props
  mode = "pages", // "pages" or "loadmore"
  className = "",
  showPageNumbers = true,
  maxVisiblePages = 5,
  
  // Styling
  theme = "light", // "light" or "dark"
  size = "medium" // "small", "medium", "large"
}) => {
  // Size classes
  const sizeClasses = {
    small: {
      button: "px-2 py-1 text-sm",
      spacing: "space-x-1"
    },
    medium: {
      button: "px-3 py-2 text-sm",
      spacing: "space-x-2"
    },
    large: {
      button: "px-4 py-3 text-base",
      spacing: "space-x-3"
    }
  };

  // Theme classes
  const themeClasses = {
    light: {
      button: "bg-white text-gray-700 border border-gray-300 hover:bg-gray-50",
      activeButton: "bg-violet-600 text-white border-violet-600",
      disabledButton: "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed",
      loadMoreButton: "bg-violet-600 hover:bg-violet-700 text-white"
    },
    dark: {
      button: "bg-gray-800 text-gray-300 border border-gray-600 hover:bg-gray-700",
      activeButton: "bg-violet-600 text-white border-violet-600",
      disabledButton: "bg-gray-900 text-gray-600 border-gray-700 cursor-not-allowed",
      loadMoreButton: "bg-violet-600 hover:bg-violet-700 text-white"
    }
  };

  const currentSizeClasses = sizeClasses[size];
  const currentThemeClasses = themeClasses[theme];

  // Generate page numbers to display
  const generatePageNumbers = () => {
    const pages = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, currentPage + halfVisible);
    
    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxVisiblePages) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      } else {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
    }
    
    // Add first page and ellipsis if needed
    if (startPage > 1) {
      pages.push(1);
      if (startPage > 2) {
        pages.push('ellipsis-start');
      }
    }
    
    // Add visible pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // Add ellipsis and last page if needed
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push('ellipsis-end');
      }
      pages.push(totalPages);
    }
    
    return pages;
  };

  // Load More Mode
  if (mode === "loadmore") {
    if (!hasMore) return null;
    
    return (
      <div className={`flex justify-center ${className}`}>
        <button
          onClick={onLoadMore}
          disabled={loading}
          className={`
            ${currentSizeClasses.button}
            ${currentThemeClasses.loadMoreButton}
            rounded-lg font-medium transition-colors duration-200
            disabled:opacity-50 disabled:cursor-not-allowed
            flex items-center gap-2
          `}
        >
          {loading && (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          )}
          {loading ? "Loading..." : loadMoreText}
        </button>
      </div>
    );
  }

  // Traditional Pagination Mode
  if (totalPages <= 1) return null;

  const pageNumbers = generatePageNumbers();

  return (
    <div className={`flex items-center justify-center ${currentSizeClasses.spacing} ${className}`}>
      {/* Previous Button */}
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`
          ${currentSizeClasses.button}
          ${currentPage === 1 ? currentThemeClasses.disabledButton : currentThemeClasses.button}
          rounded-lg transition-colors duration-200 flex items-center gap-1
        `}
      >
        <FiChevronLeft className="w-4 h-4" />
        <span className="hidden sm:inline">Previous</span>
      </button>

      {/* Page Numbers */}
      {showPageNumbers && (
        <div className={`flex items-center ${currentSizeClasses.spacing}`}>
          {pageNumbers.map((page, index) => {
            if (typeof page === 'string' && page.startsWith('ellipsis')) {
              return (
                <span
                  key={page}
                  className={`${currentSizeClasses.button} ${currentThemeClasses.button} rounded-lg flex items-center justify-center`}
                >
                  <FiMoreHorizontal className="w-4 h-4" />
                </span>
              );
            }

            const isActive = page === currentPage;
            return (
              <button
                key={page}
                onClick={() => onPageChange(page)}
                className={`
                  ${currentSizeClasses.button}
                  ${isActive ? currentThemeClasses.activeButton : currentThemeClasses.button}
                  rounded-lg transition-colors duration-200 font-medium
                `}
              >
                {page}
              </button>
            );
          })}
        </div>
      )}

      {/* Next Button */}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`
          ${currentSizeClasses.button}
          ${currentPage === totalPages ? currentThemeClasses.disabledButton : currentThemeClasses.button}
          rounded-lg transition-colors duration-200 flex items-center gap-1
        `}
      >
        <span className="hidden sm:inline">Next</span>
        <FiChevronRight className="w-4 h-4" />
      </button>
    </div>
  );
};

/**
 * Simple Load More Button Component
 * For cases where you just need a load more button without the full pagination component
 */
export const LoadMoreButton = ({
  hasMore = false,
  loading = false,
  onLoadMore,
  text = "Load More",
  className = "",
  theme = "light"
}) => {
  return (
    <Pagination
      mode="loadmore"
      hasMore={hasMore}
      loading={loading}
      onLoadMore={onLoadMore}
      loadMoreText={text}
      className={className}
      theme={theme}
    />
  );
};

/**
 * Page Info Component
 * Shows current page information like "Showing 1-10 of 100 results"
 */
export const PageInfo = ({
  currentPage = 1,
  itemsPerPage = 10,
  totalItems = 0,
  className = "",
  theme = "light"
}) => {
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);
  
  const textColor = theme === "dark" ? "text-gray-400" : "text-gray-600";
  
  if (totalItems === 0) {
    return (
      <div className={`text-sm ${textColor} ${className}`}>
        No items found
      </div>
    );
  }
  
  return (
    <div className={`text-sm ${textColor} ${className}`}>
      Showing {startItem}-{endItem} of {totalItems} results
    </div>
  );
};

export default Pagination;
