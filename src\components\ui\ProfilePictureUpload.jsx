import React, { useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { uploadProfilePicture, deleteProfilePicture } from '../../store/slices/userSlice';
import { useThemeProvider } from '../../providers/ThemeContext';
import { validateProfilePicture } from '../../utils/fileValidation';
import {
  FiCamera,
  FiTrash2,
  FiUpload,
  FiUser,
  FiX,
  <PERSON>Check,
  FiLoader
} from 'react-icons/fi';

/**
 * ProfilePictureUpload Component
 * Handles profile picture upload, preview, and deletion
 *
 * Uses the enhanced API with image data objects:
 * - POST /api/files/profile-picture for uploads
 * - DELETE /api/files/profile-picture for deletion
 * - GET /api/users/me returns profile_picture_data with base64 image objects
 * - Supports both image data objects and URL fallbacks
 */
const ProfilePictureUpload = ({ 
  currentUser, 
  size = 'lg',
  showUploadButton = true,
  showDeleteButton = true,
  className = ''
}) => {
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  const { loading, error, success } = useSelector((state) => state.users);
  
  const [previewUrl, setPreviewUrl] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef(null);

  // Theme classes
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const borderColor = currentTheme === "dark" ? "border-gray-600" : "border-gray-300";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";

  // Size configurations
  const sizeConfig = {
    sm: { container: 'w-16 h-16', icon: 'w-6 h-6', text: 'text-xs' },
    md: { container: 'w-24 h-24', icon: 'w-8 h-8', text: 'text-sm' },
    lg: { container: 'w-32 h-32', icon: 'w-12 h-12', text: 'text-base' },
    xl: { container: 'w-40 h-40', icon: 'w-16 h-16', text: 'text-lg' }
  };

  const config = sizeConfig[size] || sizeConfig.lg;

  // File validation using utility function
  const validateFile = (file) => {
    const validation = validateProfilePicture(file);
    return validation.isValid ? null : validation.error;
  };

  // Handle file selection
  const handleFileSelect = (file) => {
    const validationError = validateFile(file);
    if (validationError) {
      alert(validationError);
      return;
    }

    setSelectedFile(file);
    
    // Create preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  // Handle file input change
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // Handle drag and drop
  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  // Upload profile picture
  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      await dispatch(uploadProfilePicture(selectedFile)).unwrap();
      setSelectedFile(null);
      setPreviewUrl(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Failed to upload profile picture:', error);
    }
  };

  // Delete profile picture
  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete your profile picture?')) {
      try {
        await dispatch(deleteProfilePicture()).unwrap();
      } catch (error) {
        console.error('Failed to delete profile picture:', error);
      }
    }
  };

  // Cancel selection
  const handleCancel = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Get current profile picture URL with priority for image data objects
  const getCurrentProfilePicture = () => {
    if (previewUrl) return previewUrl;

    // Priority: 1. Image data objects, 2. URL fallback
    const imageData = currentUser?.profile_picture_data;
    const fullImageData = imageData?.full_image;
    const thumbnailData = imageData?.thumbnail;
    const urlFallback = currentUser?.profile_picture_url || currentUser?.profile_picture_thumbnail_url;

    return fullImageData?.data_url || thumbnailData?.data_url || urlFallback;
  };

  return (
    <div className={`${className}`}>
      {/* Profile Picture Display */}
      <div className="flex flex-col items-center space-y-4">
        {/* Avatar Container */}
        <div
          className={`
            ${config.container} rounded-full border-2 ${borderColor} 
            ${dragOver ? 'border-violet-500 bg-violet-50 dark:bg-violet-900/20' : cardBg}
            relative overflow-hidden cursor-pointer transition-all duration-200
            hover:border-violet-500 group
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          {getCurrentProfilePicture() ? (
            <img
              src={getCurrentProfilePicture()}
              alt="Profile"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className={`w-full h-full flex items-center justify-center ${textSecondary}`}>
              <FiUser className={config.icon} />
            </div>
          )}
          
          {/* Upload Overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <FiCamera className="w-6 h-6 text-white" />
          </div>
          
          {/* Loading Overlay */}
          {loading && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <FiLoader className="w-6 h-6 text-white animate-spin" />
            </div>
          )}
        </div>

        {/* File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="hidden"
        />

        {/* Action Buttons */}
        {selectedFile ? (
          <div className="flex space-x-2">
            <button
              onClick={handleUpload}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors disabled:opacity-50"
            >
              <FiCheck className="w-4 h-4" />
              Upload
            </button>
            <button
              onClick={handleCancel}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors"
            >
              <FiX className="w-4 h-4" />
              Cancel
            </button>
          </div>
        ) : (
          <div className="flex space-x-2">
            {showUploadButton && (
              <button
                onClick={() => fileInputRef.current?.click()}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors disabled:opacity-50"
              >
                <FiUpload className="w-4 h-4" />
                Upload Photo
              </button>
            )}
            {showDeleteButton && getCurrentProfilePicture() && (
              <button
                onClick={handleDelete}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                <FiTrash2 className="w-4 h-4" />
                Delete
              </button>
            )}
          </div>
        )}

        {/* Status Messages */}
        {error && (
          <div className="text-red-600 dark:text-red-400 text-sm text-center">
            {typeof error === 'string' ? error : error.detail || 'An error occurred'}
          </div>
        )}
        {success && (
          <div className="text-green-600 dark:text-green-400 text-sm text-center">
            {success}
          </div>
        )}

        {/* Instructions */}
        <div className={`text-center ${textSecondary} text-sm`}>
          <p>Click to upload or drag and drop</p>
          <p>JPG, PNG, GIF, WEBP (max 10MB)</p>
        </div>
      </div>
    </div>
  );
};

export default ProfilePictureUpload;
