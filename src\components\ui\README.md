# EduFair UI Component Library

A comprehensive collection of reusable UI components for the EduFair application. This library provides consistent design, behavior, and accessibility across the entire application.

## 📁 Component Categories

### 🃏 Cards (`/cards`)
Reusable card components for displaying structured information:
- **UserCard**: User profiles with avatar, contact info, and verification status
- **ClassroomCard**: Classroom information with stats and actions
- **DashboardCard**: Flexible dashboard widgets with charts and metrics
- **InfoCard**: Information displays, alerts, and notifications

### 📝 Forms (`/forms`)
Comprehensive form components with validation and consistent styling:
- **FormModal**: Modal wrapper for forms with submission handling
- **FormButton**: Specialized buttons for form actions (Submit, Cancel, Save, Delete)
- **FormValidation**: Validation rules, hooks, and error display components
- **ButtonGroup**: Organized button layouts

### 🔘 Buttons (`/buttons`)
Complete button system with semantic variants:
- **Button**: Base button with multiple variants and states
- **IconButton**: Icon-only buttons with tooltips
- **ActionButtons**: Semantic action buttons (View, Edit, Delete, etc.)
- **QuickActionBar**: Pre-configured action button groups

### 🪟 Modals (`/modals`)
Modal system for overlays and dialogs:
- **BaseModal**: Foundation modal with accessibility features
- **InfoModal**: Information and alert modals with different types
- **ContentModal**: Rich content display (images, videos, documents)
- **FormModal**: Form-specific modals with submission handling

### 📊 Tables (`/tables`)
Specialized table components built on DataTable:
- **UserTable**: User management with role-based actions
- **ClassroomTable**: Classroom listings with stats and status
- **ExamTable**: Exam management with smart status detection
- **CompactTable**: Variants for smaller spaces

### 🧭 Navigation (`/navigation`)
Navigation components for user guidance:
- **Breadcrumbs**: Hierarchical navigation with predefined patterns
- **Tabs**: Content organization with multiple variants
- **Pagination**: Data navigation with items-per-page controls
- **ActionMenu**: Dropdown menus with predefined action patterns

### 📐 Layout (`/layout`)
Layout components for consistent page structure:
- **PageHeader**: Consistent page headers with breadcrumbs and actions
- **PageContainer**: Responsive containers with max-width controls
- **ContentWrapper**: Flexible content wrappers (Card, Panel, Section)
- **SectionDivider**: Visual separation between content sections

## 🚀 Quick Start

### Installation
```bash
# Components are already part of the project
# Import from the ui directory
```

### Basic Usage
```jsx
import { 
  UserCard, 
  FormModal, 
  Button, 
  PageHeader,
  DataTable 
} from '../components/ui';

// Or import by category
import { Cards, Forms, Buttons } from '../components/ui';
```

### Example: User Management Page
```jsx
import { 
  PageContainer,
  PageHeader,
  UserTable,
  FormModal,
  useFormValidation,
  validationRules
} from '../components/ui';

function UserManagementPage() {
  const [users, setUsers] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  const { values, errors, handleChange, validateForm } = useFormValidation(
    { name: '', email: '' },
    {
      name: [validationRules.required],
      email: [validationRules.required, validationRules.email]
    }
  );

  return (
    <PageContainer>
      <PageHeader
        title="User Management"
        breadcrumbs={breadcrumbPatterns.adminUsers()}
        actions={[
          {
            label: 'Add User',
            variant: 'primary',
            onClick: () => setIsModalOpen(true)
          }
        ]}
      />
      
      <UserTable
        users={users}
        onView={handleViewUser}
        onEdit={handleEditUser}
        onDelete={handleDeleteUser}
        selectable={true}
      />
      
      <FormModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleCreateUser}
        title="Create User"
      >
        <FormField label="Name" error={errors.name}>
          <TextInput
            value={values.name}
            onChange={(e) => handleChange('name', e.target.value)}
          />
        </FormField>
      </FormModal>
    </PageContainer>
  );
}
```

## 🎨 Design System

### Color Palette
- **Primary**: Violet (`violet-600`) - Main actions and branding
- **Secondary**: Gray (`gray-600`) - Secondary actions
- **Success**: Green (`green-600`) - Positive actions and states
- **Warning**: Yellow (`yellow-600`) - Caution and warnings
- **Danger**: Red (`red-600`) - Destructive actions and errors

### Typography
- **Headings**: Font weights 600-700, responsive sizing
- **Body**: Font weight 400, consistent line heights
- **Labels**: Font weight 500, uppercase tracking for form labels

### Spacing
- **Consistent Scale**: 4px base unit (1, 2, 3, 4, 6, 8, 12, 16, 24, 32...)
- **Component Padding**: Small (4px), Default (6px), Large (8px)
- **Layout Margins**: Responsive spacing with mobile-first approach

### Shadows
- **Subtle**: `shadow-sm` for cards and buttons
- **Default**: `shadow` for modals and elevated content
- **Prominent**: `shadow-lg` for important overlays

## ♿ Accessibility

All components follow WCAG 2.1 AA guidelines:
- **Keyboard Navigation**: Full keyboard support with visible focus indicators
- **Screen Readers**: Proper ARIA labels, roles, and semantic HTML
- **Color Contrast**: Sufficient contrast ratios for all text and interactive elements
- **Focus Management**: Logical tab order and focus trapping in modals

## 📱 Responsive Design

Components are mobile-first and responsive:
- **Breakpoints**: sm (640px), md (768px), lg (1024px), xl (1280px)
- **Adaptive Layouts**: Tables become cards on mobile, navigation collapses
- **Touch Targets**: Minimum 44px touch targets for mobile devices

## 🔧 Customization

### Theming
Components support dark mode automatically through Tailwind's dark mode classes.

### Extending Components
```jsx
// Extend existing components
const CustomUserCard = ({ user, ...props }) => {
  return (
    <UserCard
      user={user}
      className="border-2 border-blue-500"
      {...props}
    />
  );
};

// Create variants
const PrimaryButton = (props) => (
  <Button variant="primary" {...props} />
);
```

### Custom Styling
```jsx
// Override styles with className
<Button 
  variant="primary" 
  className="bg-custom-color hover:bg-custom-color-dark"
>
  Custom Button
</Button>
```

## 📚 Documentation

Each component category has detailed documentation:
- **Props**: Complete prop definitions with types and defaults
- **Examples**: Usage examples for common scenarios
- **Variants**: Specialized component variants
- **Patterns**: Common usage patterns and best practices

## 🧪 Testing

Components are designed for testability:
- **Semantic HTML**: Easy to target with testing selectors
- **Consistent Props**: Predictable prop interfaces
- **Event Handlers**: Clear callback patterns for user interactions

## 🔄 Migration Guide

### From Custom Components
1. **Identify Patterns**: Find repeated UI patterns in your code
2. **Replace Gradually**: Start with high-impact components (buttons, cards)
3. **Update Imports**: Change imports to use the UI library
4. **Remove Custom CSS**: Replace custom styles with component props

### Example Migration
```jsx
// Before
<div className="bg-white rounded-lg shadow p-6">
  <div className="flex items-center justify-between mb-4">
    <h3 className="text-lg font-semibold">{user.name}</h3>
    <button onClick={() => handleEdit(user)}>Edit</button>
  </div>
  <p className="text-gray-600">{user.email}</p>
</div>

// After
<UserCard
  user={user}
  onEdit={handleEdit}
  showActions={true}
/>
```

## 🤝 Contributing

When adding new components:
1. **Follow Patterns**: Use existing component patterns and conventions
2. **Document Thoroughly**: Include props, examples, and usage guidelines
3. **Test Accessibility**: Ensure keyboard navigation and screen reader support
4. **Consider Responsive**: Design for mobile-first responsive behavior
5. **Add to Index**: Export from appropriate category index files

## 📈 Performance

Components are optimized for performance:
- **Tree Shaking**: Import only what you need
- **Lazy Loading**: Modal content loaded on demand
- **Memoization**: Expensive calculations memoized where appropriate
- **Bundle Size**: Minimal dependencies and efficient code

## 🔮 Future Enhancements

Planned improvements:
- **Animation System**: Consistent animations and transitions
- **Theme Customization**: Runtime theme switching
- **Component Variants**: Additional specialized variants
- **Advanced Patterns**: Complex component compositions
- **Performance Monitoring**: Bundle size and runtime performance tracking

---

This UI component library provides a solid foundation for building consistent, accessible, and maintainable user interfaces in the EduFair application. Each component is designed to work independently while maintaining visual and behavioral consistency across the entire system.
