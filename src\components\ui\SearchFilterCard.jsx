import { useEffect, memo } from 'react';
import { FiGrid, FiList } from 'react-icons/fi';
import { useStableCallback } from '../../utils/helpers/performance';

/**
 * Reusable SearchFilterCard component for admin pages
 * Provides search, filters, and view mode toggle in a consistent card layout
 */
const SearchFilterCard = memo(function SearchFilterCard({
  // Search props
  searchValue = '',
  onSearchChange,
  onSearchSubmit,
  onSearchClear,
  searchPlaceholder = 'Search...',
  
  // Filter props
  filters = [],
  
  // Results props
  resultsCount = 0,
  resultsType = 'items',
  
  // View mode props
  viewMode = 'table',
  onViewModeChange,
  showViewToggle = true,
  
  // Additional props
  className = '',
  title = 'Search'
}) {
  // Keyboard shortcut for search
  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        document.querySelector('[data-search-input]')?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleSearchInputChange = useStableCallback((e) => {
    const value = e.target.value;
    onSearchChange?.(value);
  }, [onSearchChange]);

  const handleSearchKeyDown = useStableCallback((e) => {
    if (e.key === 'Enter') {
      onSearchSubmit?.(searchValue);
    }
  }, [onSearchSubmit, searchValue]);

  const handleClearSearch = useStableCallback(() => {
    onSearchClear?.();
  }, [onSearchClear]);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      <div className="flex flex-col space-y-4">
        {/* Search Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">{title}</h3>
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">Ctrl</kbd>
            <span>+</span>
            <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">K</kbd>
          </div>
        </div>

        {/* Search Input */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            data-search-input
            value={searchValue}
            onChange={handleSearchInputChange}
            onKeyDown={handleSearchKeyDown}
            placeholder={`${searchPlaceholder} (Ctrl+K)`}
            className="form-input w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-violet-500"
          />
          {searchValue && (
            <button
              onClick={handleClearSearch}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* Filters and Controls */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex flex-col sm:flex-row gap-3">
            {/* Dynamic Filters */}
            {filters.map((filter, index) => (
              <div key={index} className="flex flex-col">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {filter.label}
                </label>
                <select
                  value={filter.value}
                  onChange={(e) => filter.onChange(e.target.value)}
                  className="form-select text-sm min-w-[120px]"
                >
                  {filter.options.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            ))}
          </div>

          <div className="flex items-center justify-between sm:justify-end gap-4">
            {/* Results Count */}
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {resultsCount} {resultsType} found
            </span>

            {/* View Mode Toggle */}
            {showViewToggle && (
              <div className="flex items-center bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
                <button
                  onClick={() => onViewModeChange?.('table')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === 'table' 
                      ? 'bg-white dark:bg-gray-700 shadow text-violet-600 dark:text-violet-400' 
                      : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                  title="Table View"
                >
                  <FiList className="w-4 h-4" />
                </button>
                <button
                  onClick={() => onViewModeChange?.('grid')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === 'grid' 
                      ? 'bg-white dark:bg-gray-700 shadow text-violet-600 dark:text-violet-400' 
                      : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                  title="Grid View"
                >
                  <FiGrid className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});

export default SearchFilterCard;
