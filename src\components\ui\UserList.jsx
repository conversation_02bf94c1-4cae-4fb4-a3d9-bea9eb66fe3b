import React from 'react';
import User<PERSON>istItem from './UserListItem';
import { FiUsers, FiSearch } from 'react-icons/fi';

/**
 * Reusable UserList component for displaying collections of users
 * 
 * @param {Array} users - Array of user objects
 * @param {Function} onFollow - Callback when follow is clicked
 * @param {Function} onVisitProfile - Callback when visit profile is clicked
 * @param {Array} customActions - Additional custom actions for each user
 * @param {boolean} showEmail - Whether to show email for each user
 * @param {boolean} showPhone - Whether to show phone for each user
 * @param {boolean} showLocation - Whether to show location for each user
 * @param {string} size - Size variant for user items
 * @param {string} variant - Style variant for user items
 * @param {boolean} showActions - Whether to show actions dropdown
 * @param {string} emptyTitle - Title for empty state
 * @param {string} emptyDescription - Description for empty state
 * @param {React.Component} emptyIcon - Icon for empty state
 * @param {boolean} loading - Whether the list is loading
 * @param {string} className - Additional CSS classes
 * @param {boolean} dividers - Whether to show dividers between items
 * @param {boolean} searchable - Whether to show search functionality
 * @param {string} searchPlaceholder - Placeholder for search input
 */
const UserList = ({
  users = [],
  onFollow,
  onVisitProfile,
  customActions = [],
  showEmail = true,
  showPhone = false,
  showLocation = false,
  size = 'md',
  variant = 'default',
  showActions = true,
  emptyTitle = 'No users found',
  emptyDescription = 'There are no users to display at the moment.',
  emptyIcon: EmptyIcon = FiUsers,
  loading = false,
  className = '',
  dividers = true,
  searchable = false,
  searchPlaceholder = 'Search users...',
  onSearch
}) => {
  const [searchTerm, setSearchTerm] = React.useState('');

  // Filter users based on search term
  const filteredUsers = React.useMemo(() => {
    if (!searchTerm) return users;
    
    const term = searchTerm.toLowerCase();
    return users.filter(user => 
      (user.username || user.name || '').toLowerCase().includes(term) ||
      (user.email || '').toLowerCase().includes(term)
    );
  }, [users, searchTerm]);

  // Handle search
  const handleSearch = (value) => {
    setSearchTerm(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  // Loading skeleton
  const LoadingSkeleton = () => (
    <div className="space-y-1">
      {[...Array(3)].map((_, index) => (
        <div key={index} className="flex items-center space-x-3 p-4 animate-pulse">
          <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
      ))}
    </div>
  );

  // Empty state
  const EmptyState = () => (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
      <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
        <EmptyIcon className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {emptyTitle}
      </h3>
      <p className="text-gray-600 dark:text-gray-400 max-w-sm">
        {emptyDescription}
      </p>
    </div>
  );

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Search Header */}
      {searchable && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      )}

      {/* User List Content */}
      <div className="min-h-0">
        {loading ? (
          <LoadingSkeleton />
        ) : filteredUsers.length > 0 ? (
          <div className={dividers ? 'divide-y divide-gray-200 dark:divide-gray-700' : ''}>
            {filteredUsers.map((user, index) => (
              <UserListItem
                key={user.id || index}
                user={user}
                onFollow={onFollow}
                onVisitProfile={onVisitProfile}
                customActions={customActions}
                showEmail={showEmail}
                showPhone={showPhone}
                showLocation={showLocation}
                size={size}
                variant={variant}
                showActions={showActions}
              />
            ))}
          </div>
        ) : (
          <EmptyState />
        )}
      </div>

      {/* Footer with count */}
      {!loading && filteredUsers.length > 0 && (
        <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 rounded-b-lg">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {searchTerm ? (
              <>
                Showing {filteredUsers.length} of {users.length} users
                {searchTerm && (
                  <span className="ml-1">
                    matching "<span className="font-medium">{searchTerm}</span>"
                  </span>
                )}
              </>
            ) : (
              <>
                {users.length} user{users.length !== 1 ? 's' : ''} total
              </>
            )}
          </p>
        </div>
      )}
    </div>
  );
};

export default UserList;
