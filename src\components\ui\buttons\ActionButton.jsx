import React from 'react';
import { 
  FiEye, 
  FiEdit, 
  FiTrash2, 
  FiDownload, 
  FiShare2, 
  FiCopy, 
  FiExternalLink,
  FiPlus,
  FiRefreshCw,
  FiSettings,
  FiMoreVertical
} from 'react-icons/fi';
import Button, { IconButton } from './Button';

/**
 * Specialized action buttons for common operations
 * Provides semantic meaning and consistent styling
 */

/**
 * View/Preview action button
 */
export const ViewButton = ({
  onClick,
  tooltip = 'View',
  size = 'sm',
  variant = 'ghost',
  ...props
}) => {
  return (
    <IconButton
      icon={FiEye}
      onClick={onClick}
      tooltip={tooltip}
      size={size}
      variant={variant}
      className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
      {...props}
    />
  );
};

/**
 * Edit action button
 */
export const EditButton = ({
  onClick,
  tooltip = 'Edit',
  size = 'sm',
  variant = 'ghost',
  ...props
}) => {
  return (
    <IconButton
      icon={FiEdit}
      onClick={onClick}
      tooltip={tooltip}
      size={size}
      variant={variant}
      className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
      {...props}
    />
  );
};

/**
 * Delete action button
 */
export const DeleteButton = ({
  onClick,
  tooltip = 'Delete',
  size = 'sm',
  variant = 'ghost',
  confirmDelete = false,
  confirmMessage = 'Are you sure you want to delete this item?',
  ...props
}) => {
  const handleClick = () => {
    if (confirmDelete) {
      if (window.confirm(confirmMessage)) {
        onClick?.();
      }
    } else {
      onClick?.();
    }
  };

  return (
    <IconButton
      icon={FiTrash2}
      onClick={handleClick}
      tooltip={tooltip}
      size={size}
      variant={variant}
      className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
      {...props}
    />
  );
};

/**
 * Download action button
 */
export const DownloadButton = ({
  onClick,
  tooltip = 'Download',
  size = 'sm',
  variant = 'ghost',
  ...props
}) => {
  return (
    <IconButton
      icon={FiDownload}
      onClick={onClick}
      tooltip={tooltip}
      size={size}
      variant={variant}
      className="text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
      {...props}
    />
  );
};

/**
 * Share action button
 */
export const ShareButton = ({
  onClick,
  tooltip = 'Share',
  size = 'sm',
  variant = 'ghost',
  ...props
}) => {
  return (
    <IconButton
      icon={FiShare2}
      onClick={onClick}
      tooltip={tooltip}
      size={size}
      variant={variant}
      className="text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300"
      {...props}
    />
  );
};

/**
 * Copy action button
 */
export const CopyButton = ({
  onClick,
  tooltip = 'Copy',
  size = 'sm',
  variant = 'ghost',
  ...props
}) => {
  return (
    <IconButton
      icon={FiCopy}
      onClick={onClick}
      tooltip={tooltip}
      size={size}
      variant={variant}
      className="text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
      {...props}
    />
  );
};

/**
 * External link action button
 */
export const ExternalLinkButton = ({
  href,
  onClick,
  tooltip = 'Open in new tab',
  size = 'sm',
  variant = 'ghost',
  ...props
}) => {
  const handleClick = () => {
    if (href) {
      window.open(href, '_blank', 'noopener,noreferrer');
    }
    onClick?.();
  };

  return (
    <IconButton
      icon={FiExternalLink}
      onClick={handleClick}
      tooltip={tooltip}
      size={size}
      variant={variant}
      className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
      {...props}
    />
  );
};

/**
 * Add/Create action button
 */
export const AddButton = ({
  onClick,
  children = 'Add',
  size = 'default',
  variant = 'primary',
  ...props
}) => {
  return (
    <Button
      icon={FiPlus}
      onClick={onClick}
      size={size}
      variant={variant}
      {...props}
    >
      {children}
    </Button>
  );
};

/**
 * Refresh action button
 */
export const RefreshButton = ({
  onClick,
  tooltip = 'Refresh',
  size = 'sm',
  variant = 'ghost',
  isLoading = false,
  ...props
}) => {
  return (
    <IconButton
      icon={FiRefreshCw}
      onClick={onClick}
      tooltip={tooltip}
      size={size}
      variant={variant}
      isLoading={isLoading}
      className="text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
      {...props}
    />
  );
};

/**
 * Settings action button
 */
export const SettingsButton = ({
  onClick,
  tooltip = 'Settings',
  size = 'sm',
  variant = 'ghost',
  ...props
}) => {
  return (
    <IconButton
      icon={FiSettings}
      onClick={onClick}
      tooltip={tooltip}
      size={size}
      variant={variant}
      className="text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
      {...props}
    />
  );
};

/**
 * More actions button (typically for dropdown menus)
 */
export const MoreActionsButton = ({
  onClick,
  tooltip = 'More actions',
  size = 'sm',
  variant = 'ghost',
  ...props
}) => {
  return (
    <IconButton
      icon={FiMoreVertical}
      onClick={onClick}
      tooltip={tooltip}
      size={size}
      variant={variant}
      className="text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
      {...props}
    />
  );
};

/**
 * Action button group for organizing multiple actions
 */
export const ActionButtonGroup = ({
  children,
  orientation = 'horizontal', // 'horizontal', 'vertical'
  spacing = 'default', // 'tight', 'default', 'loose'
  alignment = 'left', // 'left', 'center', 'right'
  className = ''
}) => {
  const orientationClasses = {
    horizontal: 'flex-row',
    vertical: 'flex-col'
  };

  const spacingClasses = {
    tight: orientation === 'horizontal' ? 'space-x-1' : 'space-y-1',
    default: orientation === 'horizontal' ? 'space-x-2' : 'space-y-2',
    loose: orientation === 'horizontal' ? 'space-x-3' : 'space-y-3'
  };

  const alignmentClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end'
  };

  return (
    <div className={`
      flex ${orientationClasses[orientation]} ${spacingClasses[spacing]} ${alignmentClasses[alignment]}
      ${className}
    `}>
      {children}
    </div>
  );
};

/**
 * Quick action bar with common actions
 */
export const QuickActionBar = ({
  onView,
  onEdit,
  onDelete,
  onShare,
  onDownload,
  onMore,
  showView = true,
  showEdit = true,
  showDelete = true,
  showShare = false,
  showDownload = false,
  showMore = false,
  className = ''
}) => {
  return (
    <ActionButtonGroup className={className}>
      {showView && onView && <ViewButton onClick={onView} />}
      {showEdit && onEdit && <EditButton onClick={onEdit} />}
      {showShare && onShare && <ShareButton onClick={onShare} />}
      {showDownload && onDownload && <DownloadButton onClick={onDownload} />}
      {showDelete && onDelete && <DeleteButton onClick={onDelete} />}
      {showMore && onMore && <MoreActionsButton onClick={onMore} />}
    </ActionButtonGroup>
  );
};

export default {
  ViewButton,
  EditButton,
  DeleteButton,
  DownloadButton,
  ShareButton,
  CopyButton,
  ExternalLinkButton,
  AddButton,
  RefreshButton,
  SettingsButton,
  MoreActionsButton,
  ActionButtonGroup,
  QuickActionBar
};
