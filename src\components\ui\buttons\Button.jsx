import React from 'react';
import { FiLoader } from 'react-icons/fi';

/**
 * Reusable Button component with consistent styling and behavior
 * Base component for all button variants in the application
 */
const Button = ({
  children,
  variant = 'primary', // 'primary', 'secondary', 'outline', 'ghost', 'danger', 'success', 'warning'
  size = 'default', // 'xs', 'sm', 'default', 'lg', 'xl'
  isLoading = false,
  disabled = false,
  icon: IconComponent,
  iconPosition = 'left', // 'left', 'right', 'only'
  fullWidth = false,
  rounded = 'default', // 'none', 'sm', 'default', 'lg', 'full'
  shadow = true,
  onClick,
  className = '',
  type = 'button',
  ...props
}) => {
  // Base classes
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed select-none';

  // Size configurations
  const sizeClasses = {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-3 py-1.5 text-sm',
    default: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg'
  };

  // Variant configurations
  const variantClasses = {
    primary: 'bg-violet-600 text-white hover:bg-violet-700 focus:ring-violet-500 border border-transparent',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 border border-transparent',
    outline: 'border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-violet-500',
    ghost: 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:ring-violet-500 border border-transparent',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 border border-transparent',
    success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 border border-transparent',
    warning: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 border border-transparent'
  };

  // Rounded configurations
  const roundedClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    default: 'rounded-lg',
    lg: 'rounded-xl',
    full: 'rounded-full'
  };

  // Shadow configurations
  const shadowClasses = shadow ? 'shadow-sm hover:shadow-md' : '';

  // Icon spacing based on size and position
  const getIconSpacing = () => {
    if (iconPosition === 'only') return '';
    
    const spacing = {
      xs: 'space-x-1',
      sm: 'space-x-1.5',
      default: 'space-x-2',
      lg: 'space-x-2.5',
      xl: 'space-x-3'
    };
    return spacing[size] || spacing.default;
  };

  // Icon size based on button size
  const getIconSize = () => {
    const sizes = {
      xs: 'w-3 h-3',
      sm: 'w-3.5 h-3.5',
      default: 'w-4 h-4',
      lg: 'w-5 h-5',
      xl: 'w-6 h-6'
    };
    return sizes[size] || sizes.default;
  };

  const renderIcon = (position) => {
    if (!IconComponent || iconPosition !== position) return null;
    return <IconComponent className={getIconSize()} />;
  };

  const renderLoadingIcon = () => {
    return <FiLoader className={`${getIconSize()} animate-spin`} />;
  };

  const renderContent = () => {
    if (iconPosition === 'only') {
      return isLoading ? renderLoadingIcon() : renderIcon('only');
    }

    return (
      <>
        {isLoading ? renderLoadingIcon() : renderIcon('left')}
        {children && <span>{children}</span>}
        {!isLoading && renderIcon('right')}
      </>
    );
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`
        ${baseClasses}
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${roundedClasses[rounded]}
        ${shadowClasses}
        ${fullWidth ? 'w-full' : ''}
        ${getIconSpacing()}
        ${className}
      `}
      {...props}
    >
      {renderContent()}
    </button>
  );
};

/**
 * Icon-only button variant
 */
export const IconButton = ({
  icon: IconComponent,
  tooltip,
  size = 'default',
  variant = 'ghost',
  rounded = 'default',
  className = '',
  ...props
}) => {
  const button = (
    <Button
      icon={IconComponent}
      iconPosition="only"
      size={size}
      variant={variant}
      rounded={rounded}
      shadow={false}
      className={className}
      {...props}
    />
  );

  // If tooltip is provided, wrap with tooltip component
  if (tooltip) {
    return (
      <div className="relative group">
        {button}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
          {tooltip}
        </div>
      </div>
    );
  }

  return button;
};

/**
 * Loading button that shows spinner when loading
 */
export const LoadingButton = ({
  isLoading = false,
  loadingText,
  children,
  ...props
}) => {
  return (
    <Button
      isLoading={isLoading}
      {...props}
    >
      {isLoading && loadingText ? loadingText : children}
    </Button>
  );
};

/**
 * Button with badge/notification indicator
 */
export const BadgeButton = ({
  badge,
  badgeColor = 'red',
  badgePosition = 'top-right', // 'top-right', 'top-left', 'bottom-right', 'bottom-left'
  children,
  className = '',
  ...props
}) => {
  const badgePositionClasses = {
    'top-right': '-top-1 -right-1',
    'top-left': '-top-1 -left-1',
    'bottom-right': '-bottom-1 -right-1',
    'bottom-left': '-bottom-1 -left-1'
  };

  const badgeColorClasses = {
    red: 'bg-red-500 text-white',
    blue: 'bg-blue-500 text-white',
    green: 'bg-green-500 text-white',
    yellow: 'bg-yellow-500 text-white',
    purple: 'bg-purple-500 text-white',
    gray: 'bg-gray-500 text-white'
  };

  return (
    <div className={`relative inline-flex ${className}`}>
      <Button {...props}>
        {children}
      </Button>
      {badge && (
        <span className={`
          absolute ${badgePositionClasses[badgePosition]} 
          ${badgeColorClasses[badgeColor]}
          text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center
          transform scale-75 origin-center
        `}>
          {badge}
        </span>
      )}
    </div>
  );
};

/**
 * Toggle button for on/off states
 */
export const ToggleButton = ({
  isToggled = false,
  onToggle,
  toggledVariant = 'primary',
  untoggledVariant = 'outline',
  children,
  toggledChildren,
  ...props
}) => {
  return (
    <Button
      variant={isToggled ? toggledVariant : untoggledVariant}
      onClick={() => onToggle?.(!isToggled)}
      {...props}
    >
      {isToggled && toggledChildren ? toggledChildren : children}
    </Button>
  );
};

export default Button;
