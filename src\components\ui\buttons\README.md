# Button Components

This directory contains reusable button components for the EduFair application. These components provide consistent styling, behavior, and semantic meaning across the application.

## Components

### Button
The base button component with comprehensive styling options and variants.

**Props:**
- `variant` (string): Button style - 'primary', 'secondary', 'outline', 'ghost', 'danger', 'success', 'warning'
- `size` (string): Button size - 'xs', 'sm', 'default', 'lg', 'xl'
- `isLoading` (boolean): Shows loading spinner
- `disabled` (boolean): Disabled state
- `icon` (component): Icon component to display
- `iconPosition` (string): Icon position - 'left', 'right', 'only'
- `fullWidth` (boolean): Full width button
- `rounded` (string): Border radius - 'none', 'sm', 'default', 'lg', 'full'
- `shadow` (boolean): Drop shadow (default: true)

**Usage:**
```jsx
import { Button } from '../components/ui/buttons';

<Button variant="primary" size="lg" icon={FiPlus}>
  Create New
</Button>

<Button variant="danger" isLoading={isDeleting}>
  Delete Item
</Button>
```

### IconButton
Button variant for icon-only buttons with optional tooltips.

**Props:**
- `icon` (component): Icon component (required)
- `tooltip` (string): Tooltip text
- All Button props except children

**Usage:**
```jsx
import { IconButton } from '../components/ui/buttons';

<IconButton 
  icon={FiEdit} 
  tooltip="Edit item"
  onClick={handleEdit}
/>
```

### LoadingButton
Button with built-in loading state management.

**Props:**
- `isLoading` (boolean): Loading state
- `loadingText` (string): Text to show when loading
- All Button props

**Usage:**
```jsx
import { LoadingButton } from '../components/ui/buttons';

<LoadingButton 
  isLoading={isSubmitting}
  loadingText="Saving..."
  onClick={handleSave}
>
  Save Changes
</LoadingButton>
```

### Action Buttons
Specialized buttons for common actions with semantic meaning and consistent styling.

**Available Action Buttons:**
- `ViewButton`: View/preview actions (blue)
- `EditButton`: Edit actions (green)
- `DeleteButton`: Delete actions (red) with optional confirmation
- `DownloadButton`: Download actions (purple)
- `ShareButton`: Share actions (indigo)
- `CopyButton`: Copy actions (gray)
- `ExternalLinkButton`: External link actions (blue)
- `AddButton`: Add/create actions (primary)
- `RefreshButton`: Refresh actions (gray)
- `SettingsButton`: Settings actions (gray)
- `MoreActionsButton`: More actions menu trigger (gray)

**Usage:**
```jsx
import { 
  ViewButton, 
  EditButton, 
  DeleteButton,
  QuickActionBar 
} from '../components/ui/buttons';

// Individual buttons
<ViewButton onClick={handleView} />
<EditButton onClick={handleEdit} />
<DeleteButton 
  onClick={handleDelete}
  confirmDelete={true}
  confirmMessage="Are you sure you want to delete this classroom?"
/>

// Quick action bar
<QuickActionBar
  onView={handleView}
  onEdit={handleEdit}
  onDelete={handleDelete}
  showShare={true}
  onShare={handleShare}
/>
```

### ActionButtonGroup
Container for organizing multiple action buttons.

**Props:**
- `orientation` (string): Layout direction - 'horizontal', 'vertical'
- `spacing` (string): Button spacing - 'tight', 'default', 'loose'
- `alignment` (string): Button alignment - 'left', 'center', 'right'

**Usage:**
```jsx
import { ActionButtonGroup, ViewButton, EditButton } from '../components/ui/buttons';

<ActionButtonGroup spacing="tight" alignment="right">
  <ViewButton onClick={handleView} />
  <EditButton onClick={handleEdit} />
  <DeleteButton onClick={handleDelete} />
</ActionButtonGroup>
```

### Specialized Button Variants

#### BadgeButton
Button with notification badge indicator.

```jsx
import { BadgeButton } from '../components/ui/buttons';

<BadgeButton 
  badge={5}
  badgeColor="red"
  badgePosition="top-right"
  icon={FiBell}
>
  Notifications
</BadgeButton>
```

#### ToggleButton
Button for toggle states (on/off).

```jsx
import { ToggleButton } from '../components/ui/buttons';

<ToggleButton
  isToggled={isActive}
  onToggle={setIsActive}
  toggledVariant="success"
  untoggledVariant="outline"
>
  {isActive ? 'Active' : 'Inactive'}
</ToggleButton>
```

## Design System

### Color Variants
- **Primary**: Violet theme color for main actions
- **Secondary**: Gray for secondary actions
- **Outline**: Bordered buttons for subtle actions
- **Ghost**: Transparent buttons for minimal UI
- **Danger**: Red for destructive actions
- **Success**: Green for positive actions
- **Warning**: Yellow for caution actions

### Sizes
- **xs**: Extra small (px-2 py-1, text-xs)
- **sm**: Small (px-3 py-1.5, text-sm)
- **default**: Default (px-4 py-2, text-sm)
- **lg**: Large (px-6 py-3, text-base)
- **xl**: Extra large (px-8 py-4, text-lg)

### States
- **Default**: Normal interactive state
- **Hover**: Enhanced styling on hover
- **Focus**: Focus ring for accessibility
- **Loading**: Spinner with disabled interaction
- **Disabled**: Reduced opacity with no interaction

## Accessibility

- Proper focus management with visible focus rings
- Keyboard navigation support
- Screen reader friendly with semantic HTML
- ARIA labels for icon-only buttons
- Disabled state handling
- Loading state announcements

## Best Practices

1. **Use semantic action buttons** for common operations (ViewButton, EditButton, etc.)
2. **Provide tooltips** for icon-only buttons
3. **Handle loading states** properly with isLoading prop
4. **Group related actions** using ActionButtonGroup
5. **Use consistent sizing** within the same interface
6. **Provide confirmation** for destructive actions
7. **Use appropriate variants** based on action importance

## Migration Guide

**Before:**
```jsx
<button className="bg-violet-600 text-white px-4 py-2 rounded-lg hover:bg-violet-700">
  Submit
</button>

<button className="p-2 text-gray-400 hover:text-blue-600" onClick={handleView}>
  <FiEye className="w-4 h-4" />
</button>
```

**After:**
```jsx
<Button variant="primary">
  Submit
</Button>

<ViewButton onClick={handleView} />
```

## Integration with Forms

Button components work seamlessly with form components:

```jsx
import { Button } from '../components/ui/buttons';
import { FormModal, ButtonGroup } from '../components/ui/forms';

<FormModal
  title="Create Item"
  isOpen={isOpen}
  onClose={onClose}
  showFooter={false}
>
  <FormField label="Name">
    <TextInput value={name} onChange={setName} />
  </FormField>
  
  <ButtonGroup alignment="right" className="mt-6">
    <Button variant="outline" onClick={onClose}>
      Cancel
    </Button>
    <Button variant="primary" onClick={handleSubmit}>
      Create
    </Button>
  </ButtonGroup>
</FormModal>
```
