import React from 'react';
import { FiInfo, FiAlertCircle, FiCheckCircle, FiXCircle, FiX } from 'react-icons/fi';

/**
 * Reusable InfoCard component for displaying information, alerts, and notifications
 * Used for status messages, announcements, and general information display
 */
const InfoCard = ({
  title,
  message,
  type = 'info', // 'info', 'success', 'warning', 'error'
  icon: CustomIcon,
  onClose,
  actions = [],
  children,
  className = '',
  size = 'default', // 'compact', 'default', 'large'
  variant = 'default' // 'default', 'filled', 'outlined'
}) => {
  const sizeClasses = {
    compact: 'p-3',
    default: 'p-4',
    large: 'p-6'
  };

  const getTypeConfig = (type) => {
    const configs = {
      info: {
        icon: FiInfo,
        colors: {
          default: 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200',
          filled: 'bg-blue-500 text-white',
          outlined: 'border-blue-300 text-blue-700 dark:border-blue-600 dark:text-blue-300'
        },
        iconColor: 'text-blue-500'
      },
      success: {
        icon: FiCheckCircle,
        colors: {
          default: 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200',
          filled: 'bg-green-500 text-white',
          outlined: 'border-green-300 text-green-700 dark:border-green-600 dark:text-green-300'
        },
        iconColor: 'text-green-500'
      },
      warning: {
        icon: FiAlertCircle,
        colors: {
          default: 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200',
          filled: 'bg-yellow-500 text-white',
          outlined: 'border-yellow-300 text-yellow-700 dark:border-yellow-600 dark:text-yellow-300'
        },
        iconColor: 'text-yellow-500'
      },
      error: {
        icon: FiXCircle,
        colors: {
          default: 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200',
          filled: 'bg-red-500 text-white',
          outlined: 'border-red-300 text-red-700 dark:border-red-600 dark:text-red-300'
        },
        iconColor: 'text-red-500'
      }
    };
    return configs[type] || configs.info;
  };

  const typeConfig = getTypeConfig(type);
  const IconComponent = CustomIcon || typeConfig.icon;

  const getVariantClasses = () => {
    const base = 'rounded-lg border transition-all duration-200';
    
    switch (variant) {
      case 'filled':
        return `${base} ${typeConfig.colors.filled}`;
      case 'outlined':
        return `${base} bg-white dark:bg-gray-800 ${typeConfig.colors.outlined}`;
      default:
        return `${base} ${typeConfig.colors.default}`;
    }
  };

  return (
    <div className={`
      ${getVariantClasses()} ${sizeClasses[size]} ${className}
    `}>
      <div className="flex items-start space-x-3">
        {/* Icon */}
        <div className="flex-shrink-0">
          <IconComponent 
            className={`
              w-5 h-5 mt-0.5
              ${variant === 'filled' ? 'text-white' : typeConfig.iconColor}
            `} 
          />
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* Title */}
          {title && (
            <h3 className={`
              text-sm font-semibold mb-1
              ${variant === 'filled' ? 'text-white' : ''}
            `}>
              {title}
            </h3>
          )}

          {/* Message */}
          {message && (
            <p className={`
              text-sm
              ${variant === 'filled' ? 'text-white/90' : ''}
            `}>
              {message}
            </p>
          )}

          {/* Custom Content */}
          {children && (
            <div className="mt-2">
              {children}
            </div>
          )}

          {/* Actions */}
          {actions.length > 0 && (
            <div className="mt-3 flex flex-wrap gap-2">
              {actions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.onClick}
                  className={`
                    px-3 py-1 text-xs font-medium rounded-md transition-colors
                    ${variant === 'filled' 
                      ? 'bg-white/20 text-white hover:bg-white/30' 
                      : `bg-${type}-100 text-${type}-700 hover:bg-${type}-200 dark:bg-${type}-900/30 dark:text-${type}-300 dark:hover:bg-${type}-900/50`
                    }
                  `}
                >
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Close Button */}
        {onClose && (
          <button
            onClick={onClose}
            className={`
              flex-shrink-0 p-1 rounded-md transition-colors
              ${variant === 'filled' 
                ? 'text-white/80 hover:text-white hover:bg-white/20' 
                : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
              }
            `}
          >
            <FiX className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};

/**
 * Specialized AnnouncementCard for classroom announcements
 */
export const AnnouncementCard = ({
  announcement,
  onEdit,
  onDelete,
  showActions = false,
  className = ''
}) => {
  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
  };

  const actions = [];
  if (showActions) {
    if (onEdit) {
      actions.push({ label: 'Edit', onClick: () => onEdit(announcement) });
    }
    if (onDelete) {
      actions.push({ label: 'Delete', onClick: () => onDelete(announcement) });
    }
  }

  return (
    <InfoCard
      title={announcement.title}
      message={announcement.content || announcement.message}
      type="info"
      actions={actions}
      className={className}
    >
      {announcement.created_at && (
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Posted on {formatDate(announcement.created_at)}
        </div>
      )}
      {announcement.author && (
        <div className="text-xs text-gray-500 dark:text-gray-400">
          By {announcement.author.username || announcement.author.name}
        </div>
      )}
    </InfoCard>
  );
};

/**
 * Specialized NotificationCard for system notifications
 */
export const NotificationCard = ({
  notification,
  onMarkAsRead,
  onDismiss,
  className = ''
}) => {
  const getNotificationType = (priority) => {
    const types = {
      high: 'error',
      medium: 'warning',
      low: 'info'
    };
    return types[priority] || 'info';
  };

  const actions = [];
  if (onMarkAsRead && !notification.read) {
    actions.push({ label: 'Mark as Read', onClick: () => onMarkAsRead(notification) });
  }

  return (
    <InfoCard
      title={notification.title}
      message={notification.message}
      type={getNotificationType(notification.priority)}
      actions={actions}
      onClose={onDismiss ? () => onDismiss(notification) : undefined}
      className={`${!notification.read ? 'ring-2 ring-blue-200 dark:ring-blue-800' : ''} ${className}`}
    />
  );
};

export default InfoCard;
