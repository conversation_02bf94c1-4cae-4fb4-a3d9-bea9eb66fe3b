import React from 'react';
import { 
  FiMail, 
  FiPhone, 
  FiShield, 
  FiEye, 
  FiEdit, 
  FiMoreVertical,
  FiUser,
  FiCalendar
} from 'react-icons/fi';

/**
 * Reusable UserCard component for displaying user information
 * Used across Admin, Teacher, and other user management interfaces
 */
const UserCard = ({
  user,
  onView,
  onEdit,
  onMoreActions,
  showActions = true,
  showVerificationStatus = true,
  showJoinDate = true,
  className = '',
  size = 'default' // 'compact', 'default', 'large'
}) => {
  if (!user) return null;

  const sizeClasses = {
    compact: 'p-4',
    default: 'p-6',
    large: 'p-8'
  };

  const avatarSizes = {
    compact: 'w-8 h-8 text-sm',
    default: 'w-12 h-12 text-lg',
    large: 'w-16 h-16 text-xl'
  };

  const getRoleColor = (userType) => {
    const colors = {
      admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      teacher: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      student: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      institute: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      sponsor: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
    };
    return colors[userType] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  };

  const getVerificationColor = (isVerified) => {
    return isVerified
      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className={`
      bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md 
      transition-all duration-200 border border-transparent 
      hover:border-violet-200 dark:hover:border-violet-700
      ${sizeClasses[size]} ${className}
    `}>
      {/* Header Section */}
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          {/* Avatar */}
          <div className={`
            bg-violet-100 dark:bg-violet-900/30 rounded-full 
            flex items-center justify-center flex-shrink-0
            ${avatarSizes[size]}
          `}>
            {user.avatar ? (
              <img 
                src={user.avatar} 
                alt={user.username} 
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <span className="font-semibold text-violet-600 dark:text-violet-400">
                {user.username?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() || 'U'}
              </span>
            )}
          </div>

          {/* User Info */}
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
              {user.username || user.name || 'N/A'}
            </h3>
            
            {/* Email */}
            <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
              <FiMail className="w-4 h-4 mr-1 flex-shrink-0" />
              <span className="truncate">{user.email || 'N/A'}</span>
              {showVerificationStatus && user.is_email_verified && (
                <FiShield className="w-4 h-4 ml-2 text-green-500 flex-shrink-0" title="Email verified" />
              )}
            </p>

            {/* Phone (if available) */}
            {user.mobile && (
              <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
                <FiPhone className="w-4 h-4 mr-1 flex-shrink-0" />
                <span className="truncate">{user.mobile}</span>
                {showVerificationStatus && user.is_mobile_verified && (
                  <FiShield className="w-4 h-4 ml-2 text-green-500 flex-shrink-0" title="Mobile verified" />
                )}
              </p>
            )}
          </div>
        </div>

        {/* Role Badge and Actions */}
        <div className="flex flex-col items-end space-y-2 flex-shrink-0">
          {/* Role Badge */}
          <span className={`
            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
            ${getRoleColor(user.user_type)}
          `}>
            {user.user_type?.charAt(0).toUpperCase() + user.user_type?.slice(1) || 'Unknown'}
          </span>

          {/* Action Buttons */}
          {showActions && (
            <div className="flex items-center space-x-1">
              {onView && (
                <button
                  onClick={() => onView(user)}
                  className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  title="View User"
                >
                  <FiEye className="w-4 h-4" />
                </button>
              )}
              {onEdit && (
                <button
                  onClick={() => onEdit(user)}
                  className="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors"
                  title="Edit User"
                >
                  <FiEdit className="w-4 h-4" />
                </button>
              )}
              {onMoreActions && (
                <button
                  onClick={() => onMoreActions(user)}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                  title="More Actions"
                >
                  <FiMoreVertical className="w-4 h-4" />
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Footer Section */}
      {(showJoinDate || showVerificationStatus) && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-sm">
            {/* Join Date */}
            {showJoinDate && (
              <span className="text-gray-500 dark:text-gray-400 flex items-center">
                <FiCalendar className="w-4 h-4 mr-1" />
                Joined: {formatDate(user.created_at)}
              </span>
            )}

            {/* Verification Status */}
            {showVerificationStatus && (
              <div className="flex space-x-2">
                <span className={`
                  inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                  ${getVerificationColor(user.is_email_verified)}
                `}>
                  Email {user.is_email_verified ? 'Verified' : 'Unverified'}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default UserCard;
