import React, { useEffect, useRef } from 'react';
import { FiX, FiLoader } from 'react-icons/fi';

/**
 * Reusable FormModal component for modal forms
 * Provides consistent modal behavior and styling for forms
 */
const FormModal = ({
  isOpen,
  onClose,
  title,
  subtitle,
  children,
  onSubmit,
  submitLabel = 'Submit',
  cancelLabel = 'Cancel',
  isSubmitting = false,
  submitDisabled = false,
  showFooter = true,
  size = 'default', // 'small', 'default', 'large', 'full'
  className = '',
  overlayClassName = '',
  contentClassName = '',
  headerClassName = '',
  footerClassName = '',
  closeOnOverlayClick = true,
  closeOnEscape = true,
  preventClose = false
}) => {
  const modalRef = useRef(null);
  const firstInputRef = useRef(null);

  // Size configurations
  const sizeClasses = {
    small: 'max-w-md',
    default: 'max-w-lg',
    large: 'max-w-2xl',
    full: 'max-w-4xl'
  };

  // Handle escape key
  useEffect(() => {
    if (!closeOnEscape || !isOpen) return;

    const handleEscape = (e) => {
      if (e.key === 'Escape' && !preventClose) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, preventClose, onClose]);

  // Handle overlay click
  const handleOverlayClick = (e) => {
    if (closeOnOverlayClick && !preventClose && e.target === e.currentTarget) {
      onClose();
    }
  };

  // Focus management
  useEffect(() => {
    if (isOpen && firstInputRef.current) {
      const timer = setTimeout(() => {
        firstInputRef.current?.focus();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = 'unset';
      };
    }
  }, [isOpen]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (onSubmit && !isSubmitting && !submitDisabled) {
      await onSubmit(e);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Overlay */}
      <div 
        className={`
          fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity
          ${overlayClassName}
        `}
        onClick={handleOverlayClick}
      />

      {/* Modal Container */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div
          ref={modalRef}
          className={`
            relative w-full ${sizeClasses[size]} transform transition-all
            bg-white dark:bg-gray-800 rounded-xl shadow-xl
            ${className}
          `}
        >
          {/* Header */}
          <div className={`
            flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700
            ${headerClassName}
          `}>
            <div className="flex-1 min-w-0">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 truncate">
                {title}
              </h2>
              {subtitle && (
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {subtitle}
                </p>
              )}
            </div>
            
            {!preventClose && (
              <button
                type="button"
                onClick={onClose}
                className="ml-4 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                disabled={isSubmitting}
              >
                <FiX className="w-5 h-5" />
              </button>
            )}
          </div>

          {/* Content */}
          <form onSubmit={handleSubmit} className="flex flex-col max-h-[calc(100vh-200px)]">
            <div className={`
              flex-1 overflow-y-auto p-6 space-y-6
              ${contentClassName}
            `}>
              {/* Clone children to add ref to first input */}
              {React.Children.map(children, (child, index) => {
                if (index === 0 && React.isValidElement(child) && 
                    (child.type === 'input' || child.props?.type)) {
                  return React.cloneElement(child, { ref: firstInputRef });
                }
                return child;
              })}
            </div>

            {/* Footer */}
            {showFooter && (
              <div className={`
                flex items-center justify-end space-x-3 p-6 
                border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50
                ${footerClassName}
              `}>
                <button
                  type="button"
                  onClick={onClose}
                  disabled={isSubmitting}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-violet-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {cancelLabel}
                </button>
                
                <button
                  type="submit"
                  disabled={isSubmitting || submitDisabled}
                  className="px-4 py-2 text-sm font-medium text-white bg-violet-600 border border-transparent rounded-lg hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-violet-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                >
                  {isSubmitting && <FiLoader className="w-4 h-4 animate-spin" />}
                  <span>{isSubmitting ? 'Submitting...' : submitLabel}</span>
                </button>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
};

/**
 * Specialized ConfirmModal for confirmation dialogs
 */
export const ConfirmModal = ({
  isOpen,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  confirmLabel = 'Confirm',
  cancelLabel = 'Cancel',
  type = 'default', // 'default', 'danger', 'warning'
  isLoading = false
}) => {
  const getButtonClasses = (type) => {
    const classes = {
      default: 'bg-violet-600 hover:bg-violet-700 focus:ring-violet-500',
      danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
      warning: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
    };
    return classes[type] || classes.default;
  };

  const handleConfirm = async () => {
    if (onConfirm && !isLoading) {
      await onConfirm();
    }
  };

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="small"
      showFooter={false}
      preventClose={isLoading}
    >
      <div className="text-center">
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          {message}
        </p>
        
        <div className="flex items-center justify-center space-x-3">
          <button
            type="button"
            onClick={onClose}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {cancelLabel}
          </button>
          
          <button
            type="button"
            onClick={handleConfirm}
            disabled={isLoading}
            className={`
              px-4 py-2 text-sm font-medium text-white border border-transparent rounded-lg 
              focus:outline-none focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed 
              transition-colors flex items-center space-x-2
              ${getButtonClasses(type)}
            `}
          >
            {isLoading && <FiLoader className="w-4 h-4 animate-spin" />}
            <span>{isLoading ? 'Processing...' : confirmLabel}</span>
          </button>
        </div>
      </div>
    </FormModal>
  );
};

export default FormModal;
