import React, { useState, useEffect } from 'react';
import { FiAlertCircle, FiCheck, FiX } from 'react-icons/fi';

/**
 * Form validation utilities and components
 */

// Validation rules
export const validationRules = {
  required: (value) => {
    if (value === null || value === undefined || value === '') {
      return 'This field is required';
    }
    return null;
  },

  email: (value) => {
    if (!value) return null;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value) ? null : 'Please enter a valid email address';
  },

  minLength: (min) => (value) => {
    if (!value) return null;
    return value.length >= min ? null : `Must be at least ${min} characters long`;
  },

  maxLength: (max) => (value) => {
    if (!value) return null;
    return value.length <= max ? null : `Must be no more than ${max} characters long`;
  },

  pattern: (regex, message) => (value) => {
    if (!value) return null;
    return regex.test(value) ? null : message;
  },

  phone: (value) => {
    if (!value) return null;
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(value.replace(/\s/g, '')) ? null : 'Please enter a valid phone number';
  },

  password: (value) => {
    if (!value) return null;
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const hasNumbers = /\d/.test(value);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);

    if (value.length < minLength) {
      return `Password must be at least ${minLength} characters long`;
    }
    if (!hasUpperCase) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!hasLowerCase) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!hasNumbers) {
      return 'Password must contain at least one number';
    }
    if (!hasSpecialChar) {
      return 'Password must contain at least one special character';
    }
    return null;
  },

  confirmPassword: (originalPassword) => (value) => {
    if (!value) return null;
    return value === originalPassword ? null : 'Passwords do not match';
  },

  url: (value) => {
    if (!value) return null;
    try {
      new URL(value);
      return null;
    } catch {
      return 'Please enter a valid URL';
    }
  },

  number: (value) => {
    if (!value) return null;
    return !isNaN(value) && !isNaN(parseFloat(value)) ? null : 'Please enter a valid number';
  },

  min: (min) => (value) => {
    if (!value) return null;
    const num = parseFloat(value);
    return num >= min ? null : `Value must be at least ${min}`;
  },

  max: (max) => (value) => {
    if (!value) return null;
    const num = parseFloat(value);
    return num <= max ? null : `Value must be no more than ${max}`;
  }
};

/**
 * Custom hook for form validation
 */
export const useFormValidation = (initialValues = {}, validationSchema = {}) => {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isValid, setIsValid] = useState(false);

  // Validate a single field
  const validateField = (name, value) => {
    const rules = validationSchema[name];
    if (!rules) return null;

    for (const rule of rules) {
      const error = rule(value);
      if (error) return error;
    }
    return null;
  };

  // Validate all fields
  const validateForm = () => {
    const newErrors = {};
    let formIsValid = true;

    Object.keys(validationSchema).forEach(name => {
      const error = validateField(name, values[name]);
      if (error) {
        newErrors[name] = error;
        formIsValid = false;
      }
    });

    setErrors(newErrors);
    setIsValid(formIsValid);
    return formIsValid;
  };

  // Handle field change
  const handleChange = (name, value) => {
    setValues(prev => ({ ...prev, [name]: value }));
    
    // Validate field if it has been touched
    if (touched[name]) {
      const error = validateField(name, value);
      setErrors(prev => ({ ...prev, [name]: error }));
    }
  };

  // Handle field blur
  const handleBlur = (name) => {
    setTouched(prev => ({ ...prev, [name]: true }));
    const error = validateField(name, values[name]);
    setErrors(prev => ({ ...prev, [name]: error }));
  };

  // Reset form
  const reset = (newValues = initialValues) => {
    setValues(newValues);
    setErrors({});
    setTouched({});
    setIsValid(false);
  };

  // Update validation when values change
  useEffect(() => {
    const hasErrors = Object.values(errors).some(error => error !== null);
    const hasRequiredFields = Object.keys(validationSchema).every(name => {
      const rules = validationSchema[name];
      const hasRequired = rules.some(rule => rule === validationRules.required);
      return !hasRequired || values[name];
    });
    
    setIsValid(!hasErrors && hasRequiredFields);
  }, [values, errors, validationSchema]);

  return {
    values,
    errors,
    touched,
    isValid,
    handleChange,
    handleBlur,
    validateForm,
    reset,
    setValues,
    setErrors
  };
};

/**
 * ValidationMessage component for displaying field validation status
 */
export const ValidationMessage = ({ 
  error, 
  success, 
  className = '',
  showIcon = true 
}) => {
  if (!error && !success) return null;

  const isError = !!error;
  const message = error || success;

  return (
    <div className={`
      flex items-center text-xs mt-1
      ${isError ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}
      ${className}
    `}>
      {showIcon && (
        <div className="mr-1 flex-shrink-0">
          {isError ? (
            <FiAlertCircle className="w-3 h-3" />
          ) : (
            <FiCheck className="w-3 h-3" />
          )}
        </div>
      )}
      <span>{message}</span>
    </div>
  );
};

/**
 * FieldValidationIndicator component for showing validation status inline
 */
export const FieldValidationIndicator = ({ 
  error, 
  success, 
  isValidating = false,
  className = '' 
}) => {
  if (isValidating) {
    return (
      <div className={`absolute inset-y-0 right-0 flex items-center pr-3 ${className}`}>
        <div className="w-4 h-4 border-2 border-gray-300 border-t-violet-600 rounded-full animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`absolute inset-y-0 right-0 flex items-center pr-3 ${className}`}>
        <FiX className="w-4 h-4 text-red-500" />
      </div>
    );
  }

  if (success) {
    return (
      <div className={`absolute inset-y-0 right-0 flex items-center pr-3 ${className}`}>
        <FiCheck className="w-4 h-4 text-green-500" />
      </div>
    );
  }

  return null;
};

/**
 * FormSummary component for displaying form-level validation summary
 */
export const FormSummary = ({ 
  errors = {}, 
  className = '',
  title = 'Please fix the following errors:' 
}) => {
  const errorList = Object.entries(errors)
    .filter(([_, error]) => error)
    .map(([field, error]) => ({ field, error }));

  if (errorList.length === 0) return null;

  return (
    <div className={`
      bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 
      rounded-lg p-4 ${className}
    `}>
      <div className="flex items-start">
        <FiAlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
        <div>
          <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
            {title}
          </h3>
          <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
            {errorList.map(({ field, error }) => (
              <li key={field}>
                <strong className="capitalize">{field.replace(/([A-Z])/g, ' $1').toLowerCase()}:</strong> {error}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default {
  validationRules,
  useFormValidation,
  ValidationMessage,
  FieldValidationIndicator,
  FormSummary
};
