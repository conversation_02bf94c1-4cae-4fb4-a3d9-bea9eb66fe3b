// EduFair UI Component Library
// Comprehensive export of all reusable UI components

// Card Components
export * from './cards';

// Form Components  
export * from './forms';

// Button Components
export * from './buttons';

// Modal Components
export * from './modals';

// Table Components
export * from './tables';

// Navigation Components
export * from './navigation';

// Layout Components
export * from './layout';

// Legacy Components (for backward compatibility)
export { default as DataTable } from './DataTable';
export * from './FormComponents';

// Newly organized components
export { default as Toast } from './Toast';
export { default as LoadingSpinner } from './LoadingSpinner';
export { default as ErrorMessage } from './ErrorMessage';
export { default as EmptyState } from './EmptyState';
export { default as SkeletonLoader } from './SkeletonLoader';
export { default as ErrorStates } from './ErrorStates';
export { default as ErrorBoundary } from './ErrorBoundary';
export { default as AutoSaveIndicator } from './AutoSaveIndicator';
export { default as SearchFilterCard } from './SearchFilterCard';
export { default as TabNavigation } from './TabNavigation';
export { default as Transition } from './Transition';
export { default as DropdownEditMenu } from './DropdownEditMenu';
export { default as DropdownFilter } from './DropdownFilter';
export { default as DropdownHelp } from './DropdownHelp';
export { default as DropdownNotifications } from './DropdownNotifications';
export { default as DropdownProfile } from './DropdownProfile';
export { default as ModalSearch } from './ModalSearch';

// Component Categories for organized imports
// Note: Import individual components directly from their respective directories
// Example: import { UserCard } from './cards';

// Forms components are available through direct imports
// Example: import { FormModal, useFormValidation } from './forms';

// Button components are available through direct imports
// Example: import { Button, IconButton } from './buttons';

// Modal components are available through direct imports
// Example: import { BaseModal, InfoModal } from './modals';

// Table components are available through direct imports
// Example: import { DataTable, UserTable } from './tables';

// Navigation components are available through direct imports
// Example: import { Breadcrumbs, Tabs, Pagination } from './navigation';

// Layout components are available through direct imports
// Example: import { PageHeader, PageContainer, Card } from './layout';

// Usage Examples:
// import { UserCard, DashboardCard } from './components/ui/cards';
// import { FormModal, useFormValidation } from './components/ui/forms';
// import { Button, IconButton } from './components/ui/buttons';
// import { BaseModal, InfoModal } from './components/ui/modals';
// import { DataTable, UserTable } from './components/ui/tables';
// import { Breadcrumbs, Tabs, Pagination } from './components/ui/navigation';
// import { PageHeader, PageContainer, Card } from './components/ui/layout';
