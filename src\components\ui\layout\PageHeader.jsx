import React from 'react';
import { FiArrowLeft } from 'react-icons/fi';
import { Breadcrumbs } from '../navigation';
import { Button } from '../buttons';

/**
 * Reusable PageHeader component for consistent page headers
 * Provides title, description, breadcrumbs, and action buttons
 */
const PageHeader = ({
  title,
  subtitle,
  description,
  breadcrumbs = [],
  actions = [],
  backButton = false,
  onBack,
  className = '',
  titleClassName = '',
  subtitleClassName = '',
  actionsClassName = '',
  children,
  size = 'default', // 'compact', 'default', 'large'
  variant = 'default', // 'default', 'centered', 'minimal'
  ...props
}) => {
  // Size configurations
  const sizeClasses = {
    compact: {
      container: 'py-4',
      title: 'text-xl',
      subtitle: 'text-sm',
      spacing: 'space-y-2'
    },
    default: {
      container: 'py-6',
      title: 'text-2xl',
      subtitle: 'text-base',
      spacing: 'space-y-4'
    },
    large: {
      container: 'py-8',
      title: 'text-3xl',
      subtitle: 'text-lg',
      spacing: 'space-y-6'
    }
  };

  const sizeConfig = sizeClasses[size] || sizeClasses.default;

  // Variant configurations
  const variantClasses = {
    default: 'text-left',
    centered: 'text-center',
    minimal: 'text-left'
  };

  const renderBackButton = () => {
    if (!backButton) return null;

    return (
      <Button
        variant="ghost"
        size="sm"
        icon={FiArrowLeft}
        onClick={onBack}
        className="mb-4"
      >
        Back
      </Button>
    );
  };

  const renderBreadcrumbs = () => {
    if (!breadcrumbs || breadcrumbs.length === 0) return null;

    return (
      <div className="mb-4">
        <Breadcrumbs items={breadcrumbs} />
      </div>
    );
  };

  const renderTitle = () => {
    if (!title) return null;

    return (
      <h1 className={`
        font-bold text-gray-900 dark:text-gray-100 
        ${sizeConfig.title} ${titleClassName}
      `}>
        {title}
      </h1>
    );
  };

  const renderSubtitle = () => {
    if (!subtitle) return null;

    return (
      <h2 className={`
        font-medium text-gray-600 dark:text-gray-400 
        ${sizeConfig.subtitle} ${subtitleClassName}
      `}>
        {subtitle}
      </h2>
    );
  };

  const renderDescription = () => {
    if (!description) return null;

    return (
      <p className="text-gray-600 dark:text-gray-400 max-w-3xl">
        {description}
      </p>
    );
  };

  const renderActions = () => {
    if (!actions || actions.length === 0) return null;

    return (
      <div className={`
        flex flex-wrap items-center gap-3
        ${variant === 'centered' ? 'justify-center' : 'justify-start'}
        ${actionsClassName}
      `}>
        {actions.map((action, index) => {
          if (React.isValidElement(action)) {
            return React.cloneElement(action, { key: index });
          }

          return (
            <Button
              key={index}
              variant={action.variant || 'primary'}
              size={action.size || 'default'}
              icon={action.icon}
              onClick={action.onClick}
              disabled={action.disabled}
              isLoading={action.isLoading}
            >
              {action.label}
            </Button>
          );
        })}
      </div>
    );
  };

  if (variant === 'minimal') {
    return (
      <div className={`${sizeConfig.container} ${className}`} {...props}>
        {renderBackButton()}
        {renderBreadcrumbs()}
        
        <div className="flex items-center justify-between">
          <div className="min-w-0 flex-1">
            {renderTitle()}
            {subtitle && <div className="mt-1">{renderSubtitle()}</div>}
          </div>
          
          {actions.length > 0 && (
            <div className="ml-4 flex-shrink-0">
              {renderActions()}
            </div>
          )}
        </div>
        
        {description && <div className="mt-2">{renderDescription()}</div>}
        {children && <div className="mt-4">{children}</div>}
      </div>
    );
  }

  if (variant === 'centered') {
    return (
      <div className={`${sizeConfig.container} ${variantClasses[variant]} ${className}`} {...props}>
        {renderBackButton()}
        {renderBreadcrumbs()}
        
        <div className={sizeConfig.spacing}>
          {renderTitle()}
          {renderSubtitle()}
          {renderDescription()}
          {renderActions()}
          {children}
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={`${sizeConfig.container} ${className}`} {...props}>
      {renderBackButton()}
      {renderBreadcrumbs()}
      
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
        <div className="min-w-0 flex-1">
          <div className={sizeConfig.spacing}>
            {renderTitle()}
            {renderSubtitle()}
            {renderDescription()}
          </div>
        </div>
        
        {actions.length > 0 && (
          <div className="mt-4 lg:mt-0 lg:ml-6 flex-shrink-0">
            {renderActions()}
          </div>
        )}
      </div>
      
      {children && (
        <div className="mt-6">
          {children}
        </div>
      )}
    </div>
  );
};

/**
 * Specialized PageHeader variants
 */

/**
 * Simple page header with just title and actions
 */
export const SimplePageHeader = ({ title, actions = [], ...props }) => {
  return (
    <PageHeader
      title={title}
      actions={actions}
      variant="minimal"
      size="compact"
      {...props}
    />
  );
};

/**
 * Dashboard page header with stats or metrics
 */
export const DashboardPageHeader = ({ 
  title, 
  subtitle, 
  stats = [], 
  actions = [], 
  ...props 
}) => {
  return (
    <PageHeader
      title={title}
      subtitle={subtitle}
      actions={actions}
      {...props}
    >
      {stats.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center">
                {stat.icon && (
                  <div className="p-2 bg-violet-100 dark:bg-violet-900 rounded-lg mr-3">
                    <stat.icon className="w-5 h-5 text-violet-600 dark:text-violet-400" />
                  </div>
                )}
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {stat.label}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {stat.value}
                  </p>
                  {stat.change && (
                    <p className={`text-xs ${
                      stat.change > 0 
                        ? 'text-green-600 dark:text-green-400' 
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {stat.change > 0 ? '+' : ''}{stat.change}%
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </PageHeader>
  );
};

/**
 * Form page header with save/cancel actions
 */
export const FormPageHeader = ({ 
  title, 
  subtitle,
  onSave, 
  onCancel, 
  isSaving = false,
  saveDisabled = false,
  ...props 
}) => {
  const actions = [
    {
      label: 'Cancel',
      variant: 'outline',
      onClick: onCancel
    },
    {
      label: isSaving ? 'Saving...' : 'Save',
      variant: 'primary',
      onClick: onSave,
      isLoading: isSaving,
      disabled: saveDisabled
    }
  ];

  return (
    <PageHeader
      title={title}
      subtitle={subtitle}
      actions={actions}
      variant="minimal"
      {...props}
    />
  );
};

export default PageHeader;
