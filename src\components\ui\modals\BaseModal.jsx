import React, { useEffect, useRef } from 'react';
import { FiX } from 'react-icons/fi';

/**
 * Base Modal component that provides the foundation for all modal variants
 * Handles common modal behavior like backdrop clicks, escape key, focus management
 */
const BaseModal = ({
  isOpen,
  onClose,
  children,
  size = 'default', // 'xs', 'sm', 'default', 'lg', 'xl', 'full'
  position = 'center', // 'center', 'top', 'bottom'
  closeOnOverlayClick = true,
  closeOnEscape = true,
  showCloseButton = true,
  preventClose = false,
  className = '',
  overlayClassName = '',
  contentClassName = '',
  zIndex = 50,
  animation = 'scale', // 'scale', 'slide', 'fade'
  ...props
}) => {
  const modalRef = useRef(null);
  const previousFocusRef = useRef(null);

  // Size configurations
  const sizeClasses = {
    xs: 'max-w-xs',
    sm: 'max-w-sm',
    default: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-full mx-4'
  };

  // Position configurations
  const positionClasses = {
    center: 'items-center justify-center',
    top: 'items-start justify-center pt-16',
    bottom: 'items-end justify-center pb-16'
  };

  // Animation configurations
  const animationClasses = {
    scale: {
      enter: 'transition ease-out duration-300',
      enterFrom: 'opacity-0 scale-95',
      enterTo: 'opacity-100 scale-100',
      leave: 'transition ease-in duration-200',
      leaveFrom: 'opacity-100 scale-100',
      leaveTo: 'opacity-0 scale-95'
    },
    slide: {
      enter: 'transition ease-out duration-300',
      enterFrom: 'opacity-0 translate-y-4',
      enterTo: 'opacity-100 translate-y-0',
      leave: 'transition ease-in duration-200',
      leaveFrom: 'opacity-100 translate-y-0',
      leaveTo: 'opacity-0 translate-y-4'
    },
    fade: {
      enter: 'transition ease-out duration-300',
      enterFrom: 'opacity-0',
      enterTo: 'opacity-100',
      leave: 'transition ease-in duration-200',
      leaveFrom: 'opacity-100',
      leaveTo: 'opacity-0'
    }
  };

  // Handle escape key
  useEffect(() => {
    if (!closeOnEscape || !isOpen) return;

    const handleEscape = (e) => {
      if (e.key === 'Escape' && !preventClose) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, preventClose, onClose]);

  // Handle overlay click
  const handleOverlayClick = (e) => {
    if (closeOnOverlayClick && !preventClose && e.target === e.currentTarget) {
      onClose();
    }
  };

  // Focus management
  useEffect(() => {
    if (isOpen) {
      // Store the currently focused element
      previousFocusRef.current = document.activeElement;
      
      // Focus the modal
      const timer = setTimeout(() => {
        modalRef.current?.focus();
      }, 100);

      return () => clearTimeout(timer);
    } else {
      // Restore focus to the previously focused element
      if (previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
    }
  }, [isOpen]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      const originalStyle = window.getComputedStyle(document.body).overflow;
      document.body.style.overflow = 'hidden';
      
      return () => {
        document.body.style.overflow = originalStyle;
      };
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div 
      className={`fixed inset-0 overflow-y-auto z-${zIndex}`}
      {...props}
    >
      {/* Backdrop */}
      <div 
        className={`
          fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300
          ${overlayClassName}
        `}
        onClick={handleOverlayClick}
        aria-hidden="true"
      />

      {/* Modal Container */}
      <div className={`
        flex min-h-full ${positionClasses[position]} p-4
      `}>
        <div
          ref={modalRef}
          tabIndex={-1}
          className={`
            relative w-full ${sizeClasses[size]} transform
            bg-white dark:bg-gray-800 rounded-xl shadow-xl
            ${animationClasses[animation].enter}
            ${animationClasses[animation].enterTo}
            ${contentClassName}
            ${className}
          `}
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-title"
        >
          {/* Close Button */}
          {showCloseButton && !preventClose && (
            <button
              type="button"
              onClick={onClose}
              className="absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              aria-label="Close modal"
            >
              <FiX className="w-5 h-5" />
            </button>
          )}

          {/* Content */}
          {children}
        </div>
      </div>
    </div>
  );
};

/**
 * Modal Header component for consistent header styling
 */
export const ModalHeader = ({
  title,
  subtitle,
  children,
  className = '',
  showBorder = true
}) => {
  return (
    <div className={`
      px-6 py-4 ${showBorder ? 'border-b border-gray-200 dark:border-gray-700' : ''}
      ${className}
    `}>
      {title && (
        <h2 
          id="modal-title"
          className="text-xl font-semibold text-gray-900 dark:text-gray-100"
        >
          {title}
        </h2>
      )}
      {subtitle && (
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {subtitle}
        </p>
      )}
      {children}
    </div>
  );
};

/**
 * Modal Body component for consistent content styling
 */
export const ModalBody = ({
  children,
  className = '',
  padding = true,
  scrollable = true
}) => {
  return (
    <div className={`
      ${padding ? 'px-6 py-4' : ''}
      ${scrollable ? 'overflow-y-auto max-h-96' : ''}
      ${className}
    `}>
      {children}
    </div>
  );
};

/**
 * Modal Footer component for consistent footer styling
 */
export const ModalFooter = ({
  children,
  className = '',
  showBorder = true,
  alignment = 'right' // 'left', 'center', 'right', 'between'
}) => {
  const alignmentClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between'
  };

  return (
    <div className={`
      px-6 py-4 ${showBorder ? 'border-t border-gray-200 dark:border-gray-700' : ''}
      bg-gray-50 dark:bg-gray-900/50 rounded-b-xl
      flex items-center ${alignmentClasses[alignment]} space-x-3
      ${className}
    `}>
      {children}
    </div>
  );
};

export default BaseModal;
