import React from 'react';
import { FiMaximize2, FiMinimize2 } from 'react-icons/fi';
import BaseModal, { <PERSON>dalHeader, ModalBody, ModalFooter } from './BaseModal';
import { Button, IconButton } from '../buttons';

/**
 * ContentModal for displaying rich content like images, videos, documents
 * Supports fullscreen mode and various content types
 */
const ContentModal = ({
  isOpen,
  onClose,
  title,
  subtitle,
  children,
  content,
  contentType = 'custom', // 'image', 'video', 'iframe', 'custom'
  src,
  alt,
  size = 'lg',
  fullscreenEnabled = true,
  actions = [],
  className = '',
  ...props
}) => {
  const [isFullscreen, setIsFullscreen] = React.useState(false);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const renderContent = () => {
    switch (contentType) {
      case 'image':
        return (
          <div className="flex items-center justify-center">
            <img
              src={src}
              alt={alt || title}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
          </div>
        );
      
      case 'video':
        return (
          <div className="aspect-video">
            <video
              src={src}
              controls
              className="w-full h-full rounded-lg"
            >
              Your browser does not support the video tag.
            </video>
          </div>
        );
      
      case 'iframe':
        return (
          <div className="aspect-video">
            <iframe
              src={src}
              title={title}
              className="w-full h-full rounded-lg border-0"
              allowFullScreen
            />
          </div>
        );
      
      default:
        return content || children;
    }
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      size={isFullscreen ? 'full' : size}
      className={`${isFullscreen ? 'h-screen' : ''} ${className}`}
      {...props}
    >
      <ModalHeader 
        title={title} 
        subtitle={subtitle}
        className="flex items-center justify-between"
      >
        {fullscreenEnabled && (
          <IconButton
            icon={isFullscreen ? FiMinimize2 : FiMaximize2}
            onClick={toggleFullscreen}
            tooltip={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
            variant="ghost"
            size="sm"
          />
        )}
      </ModalHeader>

      <ModalBody 
        className={`${isFullscreen ? 'flex-1 flex items-center justify-center' : ''}`}
        padding={contentType === 'custom'}
        scrollable={!isFullscreen}
      >
        {renderContent()}
      </ModalBody>

      {actions.length > 0 && (
        <ModalFooter>
          {actions.map((action, index) => (
            <Button
              key={index}
              variant={action.variant || 'outline'}
              onClick={action.onClick}
              disabled={action.disabled}
              isLoading={action.isLoading}
            >
              {action.label}
            </Button>
          ))}
        </ModalFooter>
      )}
    </BaseModal>
  );
};

/**
 * ImageModal for displaying images with zoom and navigation
 */
export const ImageModal = ({
  isOpen,
  onClose,
  images = [],
  currentIndex = 0,
  onNavigate,
  title,
  ...props
}) => {
  const [currentImageIndex, setCurrentImageIndex] = React.useState(currentIndex);

  React.useEffect(() => {
    setCurrentImageIndex(currentIndex);
  }, [currentIndex]);

  const currentImage = images[currentImageIndex];
  const hasMultiple = images.length > 1;

  const goToPrevious = () => {
    const newIndex = currentImageIndex > 0 ? currentImageIndex - 1 : images.length - 1;
    setCurrentImageIndex(newIndex);
    onNavigate?.(newIndex);
  };

  const goToNext = () => {
    const newIndex = currentImageIndex < images.length - 1 ? currentImageIndex + 1 : 0;
    setCurrentImageIndex(newIndex);
    onNavigate?.(newIndex);
  };

  const actions = [];
  
  if (hasMultiple) {
    actions.push(
      {
        label: 'Previous',
        variant: 'outline',
        onClick: goToPrevious
      },
      {
        label: 'Next',
        variant: 'outline',
        onClick: goToNext
      }
    );
  }

  return (
    <ContentModal
      isOpen={isOpen}
      onClose={onClose}
      title={title || currentImage?.title || `Image ${currentImageIndex + 1} of ${images.length}`}
      contentType="image"
      src={currentImage?.src || currentImage}
      alt={currentImage?.alt}
      actions={actions}
      {...props}
    />
  );
};

/**
 * VideoModal for displaying videos
 */
export const VideoModal = ({
  isOpen,
  onClose,
  src,
  title,
  poster,
  ...props
}) => {
  return (
    <ContentModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      contentType="video"
      src={src}
      {...props}
    >
      <video
        src={src}
        poster={poster}
        controls
        className="w-full h-full rounded-lg"
      >
        Your browser does not support the video tag.
      </video>
    </ContentModal>
  );
};

/**
 * DocumentModal for displaying documents via iframe
 */
export const DocumentModal = ({
  isOpen,
  onClose,
  src,
  title,
  downloadUrl,
  ...props
}) => {
  const actions = [];
  
  if (downloadUrl) {
    actions.push({
      label: 'Download',
      variant: 'primary',
      onClick: () => window.open(downloadUrl, '_blank')
    });
  }

  return (
    <ContentModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      contentType="iframe"
      src={src}
      actions={actions}
      size="xl"
      {...props}
    />
  );
};

/**
 * PreviewModal for previewing various content types
 */
export const PreviewModal = ({
  isOpen,
  onClose,
  item,
  onEdit,
  onDelete,
  onDownload,
  ...props
}) => {
  const getContentType = (item) => {
    if (!item?.type && !item?.mimeType) return 'custom';
    
    const type = item.type || item.mimeType;
    
    if (type.startsWith('image/')) return 'image';
    if (type.startsWith('video/')) return 'video';
    if (type.includes('pdf') || type.includes('document')) return 'iframe';
    
    return 'custom';
  };

  const actions = [];
  
  if (onEdit) {
    actions.push({
      label: 'Edit',
      variant: 'outline',
      onClick: () => onEdit(item)
    });
  }
  
  if (onDownload) {
    actions.push({
      label: 'Download',
      variant: 'outline',
      onClick: () => onDownload(item)
    });
  }
  
  if (onDelete) {
    actions.push({
      label: 'Delete',
      variant: 'danger',
      onClick: () => onDelete(item)
    });
  }

  return (
    <ContentModal
      isOpen={isOpen}
      onClose={onClose}
      title={item?.name || item?.title}
      subtitle={item?.description}
      contentType={getContentType(item)}
      src={item?.url || item?.src}
      alt={item?.alt}
      actions={actions}
      {...props}
    />
  );
};

export default ContentModal;
