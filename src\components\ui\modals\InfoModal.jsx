import React from 'react';
import { 
  FiInfo, 
  FiAlertCircle, 
  FiCheckCircle, 
  FiXCircle,
  FiAlertTriangle,
  FiHelpCircle
} from 'react-icons/fi';
import BaseModal, { ModalHeader, ModalBody, ModalFooter } from './BaseModal';
import { Button } from '../buttons';

/**
 * InfoModal component for displaying information, alerts, and notifications
 * Provides different types and consistent styling
 */
const InfoModal = ({
  isOpen,
  onClose,
  title,
  message,
  type = 'info', // 'info', 'success', 'warning', 'error', 'question'
  icon: CustomIcon,
  actions = [],
  children,
  size = 'sm',
  showCloseButton = true,
  className = '',
  ...props
}) => {
  // Type configurations
  const typeConfig = {
    info: {
      icon: FiInfo,
      iconColor: 'text-blue-500',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      borderColor: 'border-blue-200 dark:border-blue-800'
    },
    success: {
      icon: FiCheckCircle,
      iconColor: 'text-green-500',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      borderColor: 'border-green-200 dark:border-green-800'
    },
    warning: {
      icon: FiAlertTriangle,
      iconColor: 'text-yellow-500',
      bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
      borderColor: 'border-yellow-200 dark:border-yellow-800'
    },
    error: {
      icon: FiXCircle,
      iconColor: 'text-red-500',
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      borderColor: 'border-red-200 dark:border-red-800'
    },
    question: {
      icon: FiHelpCircle,
      iconColor: 'text-purple-500',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20',
      borderColor: 'border-purple-200 dark:border-purple-800'
    }
  };

  const config = typeConfig[type] || typeConfig.info;
  const IconComponent = CustomIcon || config.icon;

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      size={size}
      showCloseButton={showCloseButton}
      className={className}
      {...props}
    >
      <ModalHeader title={title} showBorder={false} />
      
      <ModalBody>
        <div className={`
          rounded-lg border p-4 ${config.bgColor} ${config.borderColor}
        `}>
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <IconComponent className={`w-6 h-6 ${config.iconColor}`} />
            </div>
            
            <div className="flex-1 min-w-0">
              {message && (
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {message}
                </p>
              )}
              
              {children && (
                <div className="mt-2">
                  {children}
                </div>
              )}
            </div>
          </div>
        </div>
      </ModalBody>

      {(actions.length > 0 || !showCloseButton) && (
        <ModalFooter>
          {actions.length > 0 ? (
            actions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant || 'outline'}
                onClick={action.onClick}
                disabled={action.disabled}
                isLoading={action.isLoading}
              >
                {action.label}
              </Button>
            ))
          ) : (
            <Button variant="primary" onClick={onClose}>
              OK
            </Button>
          )}
        </ModalFooter>
      )}
    </BaseModal>
  );
};

/**
 * AlertModal for simple alert messages
 */
export const AlertModal = ({
  isOpen,
  onClose,
  title = 'Alert',
  message,
  type = 'info',
  buttonText = 'OK',
  ...props
}) => {
  return (
    <InfoModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      message={message}
      type={type}
      showCloseButton={false}
      actions={[
        {
          label: buttonText,
          variant: 'primary',
          onClick: onClose
        }
      ]}
      {...props}
    />
  );
};

/**
 * ConfirmationModal for yes/no confirmations
 */
export const ConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  type = 'question',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmVariant = 'primary',
  isLoading = false,
  ...props
}) => {
  const handleConfirm = async () => {
    if (onConfirm && !isLoading) {
      await onConfirm();
    }
  };

  return (
    <InfoModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      message={message}
      type={type}
      showCloseButton={false}
      actions={[
        {
          label: cancelText,
          variant: 'outline',
          onClick: onClose,
          disabled: isLoading
        },
        {
          label: confirmText,
          variant: confirmVariant,
          onClick: handleConfirm,
          isLoading: isLoading
        }
      ]}
      {...props}
    />
  );
};

/**
 * NotificationModal for displaying notifications
 */
export const NotificationModal = ({
  isOpen,
  onClose,
  notification,
  onMarkAsRead,
  onAction,
  ...props
}) => {
  const getNotificationType = (priority) => {
    const types = {
      high: 'error',
      medium: 'warning',
      low: 'info'
    };
    return types[priority] || 'info';
  };

  const actions = [];
  
  if (onMarkAsRead && !notification?.read) {
    actions.push({
      label: 'Mark as Read',
      variant: 'outline',
      onClick: () => onMarkAsRead(notification)
    });
  }

  if (onAction && notification?.actionLabel) {
    actions.push({
      label: notification.actionLabel,
      variant: 'primary',
      onClick: () => onAction(notification)
    });
  }

  if (actions.length === 0) {
    actions.push({
      label: 'OK',
      variant: 'primary',
      onClick: onClose
    });
  }

  return (
    <InfoModal
      isOpen={isOpen}
      onClose={onClose}
      title={notification?.title || 'Notification'}
      message={notification?.message}
      type={getNotificationType(notification?.priority)}
      actions={actions}
      {...props}
    >
      {notification?.details && (
        <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
          {notification.details}
        </div>
      )}
      
      {notification?.timestamp && (
        <div className="mt-2 text-xs text-gray-400 dark:text-gray-500">
          {new Date(notification.timestamp).toLocaleString()}
        </div>
      )}
    </InfoModal>
  );
};

/**
 * SuccessModal for success messages
 */
export const SuccessModal = ({
  isOpen,
  onClose,
  title = 'Success!',
  message,
  actionText = 'Continue',
  onAction,
  ...props
}) => {
  const actions = [
    {
      label: actionText,
      variant: 'primary',
      onClick: onAction || onClose
    }
  ];

  return (
    <InfoModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      message={message}
      type="success"
      actions={actions}
      showCloseButton={false}
      {...props}
    />
  );
};

/**
 * ErrorModal for error messages
 */
export const ErrorModal = ({
  isOpen,
  onClose,
  title = 'Error',
  message,
  error,
  showDetails = false,
  onRetry,
  ...props
}) => {
  const actions = [];
  
  if (onRetry) {
    actions.push({
      label: 'Retry',
      variant: 'primary',
      onClick: onRetry
    });
  }
  
  actions.push({
    label: 'Close',
    variant: onRetry ? 'outline' : 'primary',
    onClick: onClose
  });

  return (
    <InfoModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      message={message}
      type="error"
      actions={actions}
      showCloseButton={false}
      {...props}
    >
      {showDetails && error && (
        <div className="mt-3 p-3 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono text-gray-600 dark:text-gray-400">
          {error.toString()}
        </div>
      )}
    </InfoModal>
  );
};

export default InfoModal;
