# Modal Components

This directory contains reusable modal components for the EduFair application. These components provide consistent modal behavior, styling, and accessibility features.

## Components

### BaseModal
The foundation modal component that provides common modal functionality.

**Props:**
- `isOpen` (boolean): Whether the modal is open
- `onClose` (function): Close callback
- `size` (string): Modal size - 'xs', 'sm', 'default', 'lg', 'xl', 'full'
- `position` (string): Modal position - 'center', 'top', 'bottom'
- `closeOnOverlayClick` (boolean): Close on backdrop click (default: true)
- `closeOnEscape` (boolean): Close on escape key (default: true)
- `showCloseButton` (boolean): Show close button (default: true)
- `preventClose` (boolean): Prevent closing during operations
- `animation` (string): Animation type - 'scale', 'slide', 'fade'
- `zIndex` (number): Z-index value (default: 50)

**Usage:**
```jsx
import { BaseModal, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ooter } from '../components/ui/modals';

<BaseModal isOpen={isOpen} onClose={onClose} size="lg">
  <ModalHeader title="Custom Modal" />
  <ModalBody>
    <p>Modal content goes here</p>
  </ModalBody>
  <ModalFooter>
    <Button onClick={onClose}>Close</Button>
  </ModalFooter>
</BaseModal>
```

### InfoModal
Modal for displaying information, alerts, and notifications with different types.

**Props:**
- `type` (string): Modal type - 'info', 'success', 'warning', 'error', 'question'
- `title` (string): Modal title
- `message` (string): Main message
- `icon` (component): Custom icon component
- `actions` (array): Action buttons array
- `children` (node): Additional content

**Specialized Variants:**
- `AlertModal`: Simple alert messages
- `ConfirmationModal`: Yes/no confirmations
- `NotificationModal`: System notifications
- `SuccessModal`: Success messages
- `ErrorModal`: Error messages with retry option

**Usage:**
```jsx
import { 
  InfoModal, 
  AlertModal, 
  ConfirmationModal, 
  SuccessModal, 
  ErrorModal 
} from '../components/ui/modals';

// Basic info modal
<InfoModal
  isOpen={isOpen}
  onClose={onClose}
  title="Information"
  message="This is an informational message."
  type="info"
  actions={[
    { label: 'OK', variant: 'primary', onClick: onClose }
  ]}
/>

// Alert modal
<AlertModal
  isOpen={showAlert}
  onClose={() => setShowAlert(false)}
  title="Alert"
  message="Please save your changes before continuing."
  type="warning"
/>

// Confirmation modal
<ConfirmationModal
  isOpen={showConfirm}
  onClose={() => setShowConfirm(false)}
  onConfirm={handleDelete}
  title="Delete Item"
  message="Are you sure you want to delete this item? This action cannot be undone."
  type="error"
  confirmText="Delete"
  confirmVariant="danger"
/>

// Success modal
<SuccessModal
  isOpen={showSuccess}
  onClose={() => setShowSuccess(false)}
  title="Success!"
  message="Your classroom has been created successfully."
  actionText="View Classroom"
  onAction={handleViewClassroom}
/>

// Error modal
<ErrorModal
  isOpen={showError}
  onClose={() => setShowError(false)}
  title="Error"
  message="Failed to save changes."
  error={error}
  showDetails={true}
  onRetry={handleRetry}
/>
```

### ContentModal
Modal for displaying rich content like images, videos, and documents.

**Props:**
- `contentType` (string): Content type - 'image', 'video', 'iframe', 'custom'
- `src` (string): Content source URL
- `alt` (string): Alt text for images
- `fullscreenEnabled` (boolean): Enable fullscreen mode
- `content` (node): Custom content
- `actions` (array): Action buttons

**Specialized Variants:**
- `ImageModal`: Image gallery with navigation
- `VideoModal`: Video player
- `DocumentModal`: Document viewer with download
- `PreviewModal`: Generic content preview

**Usage:**
```jsx
import { 
  ContentModal, 
  ImageModal, 
  VideoModal, 
  DocumentModal 
} from '../components/ui/modals';

// Image modal
<ImageModal
  isOpen={isOpen}
  onClose={onClose}
  images={imageList}
  currentIndex={currentIndex}
  onNavigate={setCurrentIndex}
  title="Image Gallery"
/>

// Video modal
<VideoModal
  isOpen={isOpen}
  onClose={onClose}
  src={videoUrl}
  title="Training Video"
  poster={thumbnailUrl}
/>

// Document modal
<DocumentModal
  isOpen={isOpen}
  onClose={onClose}
  src={documentUrl}
  title="Course Material"
  downloadUrl={downloadUrl}
/>

// Custom content modal
<ContentModal
  isOpen={isOpen}
  onClose={onClose}
  title="Custom Content"
  size="xl"
  fullscreenEnabled={true}
>
  <div className="p-4">
    <h3>Custom Content</h3>
    <p>Any custom content can go here.</p>
  </div>
</ContentModal>
```

### FormModal
Modal specifically designed for forms (from forms directory).

**Usage:**
```jsx
import { FormModal } from '../components/ui/modals';

<FormModal
  isOpen={isOpen}
  onClose={onClose}
  onSubmit={handleSubmit}
  title="Create Item"
  isSubmitting={isLoading}
>
  <FormField label="Name">
    <TextInput value={name} onChange={setName} />
  </FormField>
</FormModal>
```

## Modal Structure Components

### ModalHeader
Consistent header styling for modals.

```jsx
<ModalHeader 
  title="Modal Title" 
  subtitle="Optional subtitle"
  showBorder={true}
/>
```

### ModalBody
Content area with optional scrolling and padding.

```jsx
<ModalBody 
  padding={true}
  scrollable={true}
  className="custom-class"
>
  Content goes here
</ModalBody>
```

### ModalFooter
Footer area for actions and buttons.

```jsx
<ModalFooter 
  alignment="right"
  showBorder={true}
>
  <Button variant="outline" onClick={onClose}>Cancel</Button>
  <Button variant="primary" onClick={onSave}>Save</Button>
</ModalFooter>
```

## Accessibility Features

- **Focus Management**: Automatically focuses modal and restores focus on close
- **Keyboard Navigation**: Escape key to close, tab navigation within modal
- **Screen Reader Support**: Proper ARIA labels and roles
- **Backdrop Interaction**: Configurable backdrop click behavior
- **Scroll Lock**: Prevents body scrolling when modal is open

## Best Practices

1. **Use appropriate modal types** for different use cases
2. **Provide clear titles and messages** for better UX
3. **Handle loading states** properly in confirmation modals
4. **Use consistent action button placement** (cancel left, primary right)
5. **Implement proper error handling** with retry options
6. **Consider mobile responsiveness** with appropriate sizes
7. **Provide escape routes** (close button, escape key, backdrop click)

## Animation and Transitions

All modals support smooth animations:
- **Scale**: Default zoom-in effect
- **Slide**: Slide from top effect
- **Fade**: Simple fade effect

## Integration Examples

### With State Management
```jsx
const [modalState, setModalState] = useState({
  type: null,
  isOpen: false,
  data: null
});

const openModal = (type, data = null) => {
  setModalState({ type, isOpen: true, data });
};

const closeModal = () => {
  setModalState({ type: null, isOpen: false, data: null });
};

// Usage
<ConfirmationModal
  isOpen={modalState.type === 'delete' && modalState.isOpen}
  onClose={closeModal}
  onConfirm={() => handleDelete(modalState.data)}
  title="Delete Item"
  message={`Are you sure you want to delete "${modalState.data?.name}"?`}
/>
```

### With Form Validation
```jsx
<FormModal
  isOpen={isOpen}
  onClose={onClose}
  onSubmit={handleSubmit}
  title="Create Classroom"
  isSubmitting={isSubmitting}
  submitDisabled={!isValid}
>
  <FormField label="Name" error={errors.name}>
    <TextInput
      value={values.name}
      onChange={(e) => handleChange('name', e.target.value)}
      error={!!errors.name}
    />
  </FormField>
  
  {errors.name && <ValidationMessage error={errors.name} />}
</FormModal>
```
