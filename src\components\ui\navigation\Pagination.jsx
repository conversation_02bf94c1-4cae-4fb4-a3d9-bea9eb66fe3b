import React from 'react';
import { 
  FiChevronLeft, 
  FiChevronRight, 
  FiChevronsLeft, 
  FiChevronsRight,
  FiMoreHorizontal
} from 'react-icons/fi';

/**
 * Reusable Pagination component for data navigation
 * Provides consistent pagination with various display options
 */
const Pagination = ({
  currentPage = 1,
  totalPages = 1,
  totalItems = 0,
  itemsPerPage = 10,
  onPageChange,
  onItemsPerPageChange,
  showItemsPerPage = true,
  showPageInfo = true,
  showFirstLast = true,
  showPrevNext = true,
  maxVisiblePages = 5,
  itemsPerPageOptions = [10, 25, 50, 100],
  size = 'default', // 'sm', 'default', 'lg'
  variant = 'default', // 'default', 'simple', 'compact'
  className = '',
  disabled = false,
  ...props
}) => {
  // Size configurations
  const sizeClasses = {
    sm: {
      button: 'px-2 py-1 text-xs',
      select: 'text-xs px-2 py-1'
    },
    default: {
      button: 'px-3 py-2 text-sm',
      select: 'text-sm px-3 py-2'
    },
    lg: {
      button: 'px-4 py-3 text-base',
      select: 'text-base px-4 py-3'
    }
  };

  const sizeConfig = sizeClasses[size] || sizeClasses.default;

  // Calculate visible page numbers
  const getVisiblePages = () => {
    const pages = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, currentPage + halfVisible);
    
    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxVisiblePages) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      } else {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
    }
    
    // Add ellipsis and first page if needed
    if (startPage > 1) {
      pages.push(1);
      if (startPage > 2) {
        pages.push('ellipsis-start');
      }
    }
    
    // Add visible pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // Add ellipsis and last page if needed
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push('ellipsis-end');
      }
      pages.push(totalPages);
    }
    
    return pages;
  };

  const visiblePages = getVisiblePages();

  // Calculate item range
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  // Button component
  const PaginationButton = ({ 
    onClick, 
    disabled: buttonDisabled, 
    children, 
    isActive = false,
    ...buttonProps 
  }) => (
    <button
      onClick={onClick}
      disabled={disabled || buttonDisabled}
      className={`
        ${sizeConfig.button} font-medium rounded-lg border transition-colors
        ${isActive 
          ? 'bg-violet-600 text-white border-violet-600' 
          : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
        }
        ${(disabled || buttonDisabled) 
          ? 'opacity-50 cursor-not-allowed' 
          : 'cursor-pointer'
        }
      `}
      {...buttonProps}
    >
      {children}
    </button>
  );

  // Simple variant (just prev/next)
  if (variant === 'simple') {
    return (
      <div className={`flex items-center justify-between ${className}`} {...props}>
        <PaginationButton
          onClick={() => onPageChange?.(currentPage - 1)}
          disabled={currentPage <= 1}
        >
          <FiChevronLeft className="w-4 h-4 mr-1" />
          Previous
        </PaginationButton>
        
        {showPageInfo && (
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Page {currentPage} of {totalPages}
          </span>
        )}
        
        <PaginationButton
          onClick={() => onPageChange?.(currentPage + 1)}
          disabled={currentPage >= totalPages}
        >
          Next
          <FiChevronRight className="w-4 h-4 ml-1" />
        </PaginationButton>
      </div>
    );
  }

  // Compact variant (minimal controls)
  if (variant === 'compact') {
    return (
      <div className={`flex items-center space-x-2 ${className}`} {...props}>
        <PaginationButton
          onClick={() => onPageChange?.(currentPage - 1)}
          disabled={currentPage <= 1}
        >
          <FiChevronLeft className="w-4 h-4" />
        </PaginationButton>
        
        <span className="text-sm text-gray-700 dark:text-gray-300 px-2">
          {currentPage} / {totalPages}
        </span>
        
        <PaginationButton
          onClick={() => onPageChange?.(currentPage + 1)}
          disabled={currentPage >= totalPages}
        >
          <FiChevronRight className="w-4 h-4" />
        </PaginationButton>
      </div>
    );
  }

  // Default variant (full pagination)
  return (
    <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 ${className}`} {...props}>
      {/* Items per page and info */}
      <div className="flex items-center space-x-4">
        {showItemsPerPage && onItemsPerPageChange && (
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-700 dark:text-gray-300">
              Show:
            </label>
            <select
              value={itemsPerPage}
              onChange={(e) => onItemsPerPageChange?.(Number(e.target.value))}
              disabled={disabled}
              className={`
                ${sizeConfig.select} border border-gray-300 dark:border-gray-600 
                rounded-lg bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300
                focus:ring-2 focus:ring-violet-500 focus:border-violet-500
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              {itemsPerPageOptions.map(option => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        )}
        
        {showPageInfo && (
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Showing {startItem} to {endItem} of {totalItems} results
          </div>
        )}
      </div>

      {/* Pagination controls */}
      <div className="flex items-center space-x-1">
        {/* First page */}
        {showFirstLast && (
          <PaginationButton
            onClick={() => onPageChange?.(1)}
            disabled={currentPage <= 1}
            title="First page"
          >
            <FiChevronsLeft className="w-4 h-4" />
          </PaginationButton>
        )}

        {/* Previous page */}
        {showPrevNext && (
          <PaginationButton
            onClick={() => onPageChange?.(currentPage - 1)}
            disabled={currentPage <= 1}
            title="Previous page"
          >
            <FiChevronLeft className="w-4 h-4" />
          </PaginationButton>
        )}

        {/* Page numbers */}
        {visiblePages.map((page, index) => {
          if (typeof page === 'string') {
            return (
              <span
                key={page}
                className="px-3 py-2 text-gray-400 dark:text-gray-500"
              >
                <FiMoreHorizontal className="w-4 h-4" />
              </span>
            );
          }

          return (
            <PaginationButton
              key={page}
              onClick={() => onPageChange?.(page)}
              isActive={page === currentPage}
              title={`Page ${page}`}
            >
              {page}
            </PaginationButton>
          );
        })}

        {/* Next page */}
        {showPrevNext && (
          <PaginationButton
            onClick={() => onPageChange?.(currentPage + 1)}
            disabled={currentPage >= totalPages}
            title="Next page"
          >
            <FiChevronRight className="w-4 h-4" />
          </PaginationButton>
        )}

        {/* Last page */}
        {showFirstLast && (
          <PaginationButton
            onClick={() => onPageChange?.(totalPages)}
            disabled={currentPage >= totalPages}
            title="Last page"
          >
            <FiChevronsRight className="w-4 h-4" />
          </PaginationButton>
        )}
      </div>
    </div>
  );
};

/**
 * Hook for managing pagination state
 */
export const usePagination = ({
  initialPage = 1,
  initialItemsPerPage = 10,
  totalItems = 0
}) => {
  const [currentPage, setCurrentPage] = React.useState(initialPage);
  const [itemsPerPage, setItemsPerPage] = React.useState(initialItemsPerPage);

  const totalPages = Math.ceil(totalItems / itemsPerPage);

  const handlePageChange = (page) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Calculate offset for API calls
  const offset = (currentPage - 1) * itemsPerPage;

  return {
    currentPage,
    itemsPerPage,
    totalPages,
    offset,
    handlePageChange,
    handleItemsPerPageChange,
    setCurrentPage,
    setItemsPerPage
  };
};

/**
 * Simple Pagination variant
 */
export const SimplePagination = (props) => {
  return <Pagination variant="simple" {...props} />;
};

/**
 * Compact Pagination variant
 */
export const CompactPagination = (props) => {
  return <Pagination variant="compact" {...props} />;
};

export default Pagination;
