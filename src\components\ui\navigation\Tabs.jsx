import React, { useState } from 'react';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';

/**
 * Reusable Tabs component for content organization
 * Provides consistent tab navigation with various styles and behaviors
 */
const Tabs = ({
  tabs = [],
  activeTab,
  onTabChange,
  variant = 'default', // 'default', 'pills', 'underline', 'cards'
  size = 'default', // 'sm', 'default', 'lg'
  orientation = 'horizontal', // 'horizontal', 'vertical'
  fullWidth = false,
  scrollable = false,
  className = '',
  tabClassName = '',
  contentClassName = '',
  ...props
}) => {
  const [scrollPosition, setScrollPosition] = useState(0);
  const tabsRef = React.useRef(null);

  // Size configurations
  const sizeClasses = {
    sm: 'text-sm px-3 py-1.5',
    default: 'text-sm px-4 py-2',
    lg: 'text-base px-6 py-3'
  };

  // Variant configurations
  const variantClasses = {
    default: {
      container: 'border-b border-gray-200 dark:border-gray-700',
      tab: 'border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-600',
      activeTab: 'border-violet-500 text-violet-600 dark:text-violet-400',
      inactiveTab: 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
    },
    pills: {
      container: '',
      tab: 'rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800',
      activeTab: 'bg-violet-100 text-violet-700 dark:bg-violet-900 dark:text-violet-300',
      inactiveTab: 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
    },
    underline: {
      container: '',
      tab: 'border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-600',
      activeTab: 'border-violet-500 text-violet-600 dark:text-violet-400',
      inactiveTab: 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
    },
    cards: {
      container: '',
      tab: 'rounded-t-lg border border-gray-200 dark:border-gray-700 border-b-0 hover:bg-gray-50 dark:hover:bg-gray-800',
      activeTab: 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-700',
      inactiveTab: 'bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400'
    }
  };

  const config = variantClasses[variant] || variantClasses.default;

  // Scroll functions for scrollable tabs
  const scrollLeft = () => {
    if (tabsRef.current) {
      const newPosition = Math.max(0, scrollPosition - 200);
      setScrollPosition(newPosition);
      tabsRef.current.scrollTo({ left: newPosition, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (tabsRef.current) {
      const maxScroll = tabsRef.current.scrollWidth - tabsRef.current.clientWidth;
      const newPosition = Math.min(maxScroll, scrollPosition + 200);
      setScrollPosition(newPosition);
      tabsRef.current.scrollTo({ left: newPosition, behavior: 'smooth' });
    }
  };

  const canScrollLeft = scrollPosition > 0;
  const canScrollRight = tabsRef.current && 
    scrollPosition < tabsRef.current.scrollWidth - tabsRef.current.clientWidth;

  const renderTab = (tab, index) => {
    const isActive = activeTab === tab.id || activeTab === index;
    const IconComponent = tab.icon;
    const isDisabled = tab.disabled;

    return (
      <button
        key={tab.id || index}
        onClick={() => !isDisabled && onTabChange?.(tab.id || index, tab)}
        disabled={isDisabled}
        className={`
          relative flex items-center space-x-2 font-medium transition-all duration-200
          ${sizeClasses[size]}
          ${config.tab}
          ${isActive ? config.activeTab : config.inactiveTab}
          ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${fullWidth && orientation === 'horizontal' ? 'flex-1 justify-center' : ''}
          ${tabClassName}
        `}
        role="tab"
        aria-selected={isActive}
        aria-controls={`tabpanel-${tab.id || index}`}
        id={`tab-${tab.id || index}`}
      >
        {IconComponent && (
          <IconComponent className="w-4 h-4 flex-shrink-0" />
        )}
        <span className={orientation === 'vertical' ? '' : 'whitespace-nowrap'}>
          {tab.label}
        </span>
        
        {tab.badge && (
          <span className="ml-2 inline-flex items-center justify-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            {tab.badge}
          </span>
        )}
        
        {tab.notification && (
          <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
        )}
      </button>
    );
  };

  const activeTabContent = tabs.find((tab, index) => 
    activeTab === tab.id || activeTab === index
  );

  return (
    <div 
      className={`${orientation === 'vertical' ? 'flex' : ''} ${className}`}
      {...props}
    >
      {/* Tab List */}
      <div className={`
        ${orientation === 'vertical' ? 'flex-shrink-0 mr-6' : ''}
        ${config.container}
      `}>
        <div className={`
          ${orientation === 'horizontal' ? 'flex' : 'flex flex-col space-y-1'}
          ${scrollable && orientation === 'horizontal' ? 'relative' : ''}
        `}>
          {/* Scroll Left Button */}
          {scrollable && orientation === 'horizontal' && canScrollLeft && (
            <button
              onClick={scrollLeft}
              className="absolute left-0 top-0 bottom-0 z-10 flex items-center justify-center w-8 bg-gradient-to-r from-white to-transparent dark:from-gray-800"
            >
              <FiChevronLeft className="w-4 h-4 text-gray-400" />
            </button>
          )}

          {/* Tabs Container */}
          <div
            ref={tabsRef}
            className={`
              ${orientation === 'horizontal' ? 'flex' : 'flex flex-col'}
              ${scrollable && orientation === 'horizontal' ? 'overflow-x-auto scrollbar-hide' : ''}
              ${scrollable && orientation === 'horizontal' ? 'px-8' : ''}
            `}
            role="tablist"
            aria-orientation={orientation}
          >
            {tabs.map(renderTab)}
          </div>

          {/* Scroll Right Button */}
          {scrollable && orientation === 'horizontal' && canScrollRight && (
            <button
              onClick={scrollRight}
              className="absolute right-0 top-0 bottom-0 z-10 flex items-center justify-center w-8 bg-gradient-to-l from-white to-transparent dark:from-gray-800"
            >
              <FiChevronRight className="w-4 h-4 text-gray-400" />
            </button>
          )}
        </div>
      </div>

      {/* Tab Content */}
      {activeTabContent && activeTabContent.content && (
        <div
          className={`
            ${orientation === 'vertical' ? 'flex-1' : 'mt-4'}
            ${contentClassName}
          `}
          role="tabpanel"
          id={`tabpanel-${activeTabContent.id || activeTab}`}
          aria-labelledby={`tab-${activeTabContent.id || activeTab}`}
        >
          {activeTabContent.content}
        </div>
      )}
    </div>
  );
};

/**
 * Hook for managing tab state
 */
export const useTabs = (initialTab = 0) => {
  const [activeTab, setActiveTab] = useState(initialTab);

  const handleTabChange = (tabId, tab) => {
    setActiveTab(tabId);
  };

  return {
    activeTab,
    setActiveTab,
    handleTabChange
  };
};

/**
 * Simple Tabs variant without content management
 */
export const SimpleTabs = ({
  tabs = [],
  activeTab,
  onTabChange,
  ...props
}) => {
  const simpleTabs = tabs.map(tab => ({
    ...tab,
    content: null // Remove content to prevent rendering
  }));

  return (
    <Tabs
      tabs={simpleTabs}
      activeTab={activeTab}
      onTabChange={onTabChange}
      {...props}
    />
  );
};

/**
 * Vertical Tabs variant
 */
export const VerticalTabs = (props) => {
  return <Tabs orientation="vertical" {...props} />;
};

/**
 * Pill Tabs variant
 */
export const PillTabs = (props) => {
  return <Tabs variant="pills" {...props} />;
};

export default Tabs;
