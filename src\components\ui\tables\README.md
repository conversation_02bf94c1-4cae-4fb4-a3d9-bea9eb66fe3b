# Table Components

This directory contains specialized table components built on top of the base DataTable component. These components provide domain-specific data presentation with consistent styling and behavior.

## Components

### DataTable (Base Component)
The foundation table component that provides comprehensive table functionality including sorting, pagination, filtering, selection, and responsive design.

**Key Features:**
- Responsive design with mobile card view
- Sorting and pagination
- Row selection and bulk actions
- Filtering and search
- Loading and error states
- Export functionality
- Customizable columns and actions

### UserTable
Specialized table for displaying user data with user-specific columns and actions.

**Props:**
- `users` (array): Array of user objects
- `onView` (function): View user callback
- `onEdit` (function): Edit user callback
- `onDelete` (function): Delete user callback
- `onBulkAction` (function): Bulk action callback
- `showActions` (boolean): Show action buttons (default: true)
- `showVerificationStatus` (boolean): Show verification badges (default: true)
- `showJoinDate` (boolean): Show join date column (default: true)
- `selectable` (boolean): Enable row selection (default: false)

**Built-in Columns:**
- Avatar with initials or image
- User info (name, email, phone) with verification status
- Role badge with color coding
- Verification status
- Join date
- Actions (view, edit, delete)

**Bulk Actions:**
- Verify selected users
- Deactivate selected users
- Delete selected users

**Usage:**
```jsx
import { UserTable } from '../components/ui/tables';

<UserTable
  users={userList}
  onView={handleViewUser}
  onEdit={handleEditUser}
  onDelete={handleDeleteUser}
  onBulkAction={handleBulkAction}
  showVerificationStatus={true}
  selectable={true}
  loading={isLoading}
  error={error}
  onRetry={refetchUsers}
/>
```

### CompactUserTable
Compact variant of UserTable for smaller spaces or sidebars.

**Usage:**
```jsx
import { CompactUserTable } from '../components/ui/tables';

<CompactUserTable
  users={recentUsers}
  onRowClick={handleUserClick}
  loading={isLoading}
/>
```

### ClassroomTable
Specialized table for displaying classroom data with classroom-specific columns and actions.

**Props:**
- `classrooms` (array): Array of classroom objects
- `onView` (function): View classroom callback
- `onEdit` (function): Edit classroom callback
- `onDelete` (function): Delete classroom callback
- `onBulkAction` (function): Bulk action callback
- `showActions` (boolean): Show action buttons (default: true)
- `showTeacher` (boolean): Show teacher column (default: false)
- `showStats` (boolean): Show statistics columns (default: true)
- `userRole` (string): Current user role - 'teacher', 'student', 'admin'
- `selectable` (boolean): Enable row selection (default: false)

**Built-in Columns:**
- Classroom icon
- Classroom info (name, description, subject)
- Teacher (if enabled)
- Student count and content statistics
- Status with color coding
- Created date
- Actions (view, edit, delete - role-based)

**Bulk Actions (Teacher only):**
- Archive selected classrooms
- Activate selected classrooms
- Delete selected classrooms

**Usage:**
```jsx
import { ClassroomTable } from '../components/ui/tables';

// Teacher view
<ClassroomTable
  classrooms={classroomList}
  onView={handleViewClassroom}
  onEdit={handleEditClassroom}
  onDelete={handleDeleteClassroom}
  userRole="teacher"
  showStats={true}
  selectable={true}
/>

// Student view
<ClassroomTable
  classrooms={enrolledClassrooms}
  onView={handleViewClassroom}
  userRole="student"
  showTeacher={true}
  showActions={false}
/>
```

### CompactClassroomTable
Compact variant of ClassroomTable for smaller spaces.

**Usage:**
```jsx
import { CompactClassroomTable } from '../components/ui/tables';

<CompactClassroomTable
  classrooms={recentClassrooms}
  onRowClick={handleClassroomClick}
  showTeacher={true}
/>
```

### ExamTable
Specialized table for displaying exam data with exam-specific columns and actions.

**Props:**
- `exams` (array): Array of exam objects
- `onView` (function): View exam callback
- `onEdit` (function): Edit exam callback
- `onDelete` (function): Delete exam callback
- `onStart` (function): Start exam callback (for students)
- `onBulkAction` (function): Bulk action callback
- `showActions` (boolean): Show action buttons (default: true)
- `showClassroom` (boolean): Show classroom column (default: false)
- `showResults` (boolean): Show results column (default: false)
- `userRole` (string): Current user role - 'teacher', 'student', 'admin'
- `selectable` (boolean): Enable row selection (default: false)

**Built-in Columns:**
- Exam icon
- Exam info (title, description, duration, questions, marks)
- Classroom (if enabled)
- Schedule (start date/time)
- Participants (submitted/total)
- Status with smart status detection
- Results (if enabled, teacher only)
- Actions (view, edit, delete, start - role and status based)

**Smart Status Detection:**
- Draft: Exam not published
- Scheduled: Published but not started
- Active: Currently running
- Completed: Past end time

**Bulk Actions (Teacher only):**
- Publish selected exams
- Archive selected exams
- Delete selected exams

**Usage:**
```jsx
import { ExamTable } from '../components/ui/tables';

// Teacher view
<ExamTable
  exams={examList}
  onView={handleViewExam}
  onEdit={handleEditExam}
  onDelete={handleDeleteExam}
  userRole="teacher"
  showResults={true}
  selectable={true}
/>

// Student view
<ExamTable
  exams={availableExams}
  onView={handleViewExam}
  onStart={handleStartExam}
  userRole="student"
  showClassroom={true}
/>
```

## Common Features

### Responsive Design
All table components automatically switch to card view on mobile devices for better usability.

### Loading States
Built-in loading states with skeleton loaders:
```jsx
<UserTable
  users={users}
  loading={isLoading}
  error={error}
  onRetry={refetchData}
/>
```

### Empty States
Custom empty states for each table type with contextual messages and actions.

### Bulk Actions
Consistent bulk action interface across all table types:
```jsx
const handleBulkAction = (action, selectedItems) => {
  switch (action) {
    case 'delete':
      // Handle bulk delete
      break;
    case 'archive':
      // Handle bulk archive
      break;
    // ... other actions
  }
};
```

### Filtering and Search
All tables support filtering and search through the base DataTable:
```jsx
<UserTable
  users={filteredUsers}
  filterable={true}
  filters={currentFilters}
  onFilterChange={handleFilterChange}
/>
```

## Customization

### Custom Columns
Add custom columns to any table:
```jsx
const customColumns = [
  {
    key: 'custom_field',
    label: 'Custom',
    sortable: true,
    render: (value, item) => <CustomComponent data={item} />
  }
];

<UserTable
  users={users}
  columns={[...defaultColumns, ...customColumns]}
/>
```

### Custom Actions
Override or extend default actions:
```jsx
const customActions = [
  {
    label: 'Custom Action',
    onClick: (item) => handleCustomAction(item),
    icon: FiStar
  }
];

<ClassroomTable
  classrooms={classrooms}
  actions={customActions}
/>
```

## Best Practices

1. **Use appropriate table types** for different data domains
2. **Enable bulk actions** for administrative interfaces
3. **Show relevant columns** based on user role and context
4. **Handle loading and error states** properly
5. **Provide clear empty states** with actionable guidance
6. **Use compact variants** in sidebars or limited spaces
7. **Implement proper pagination** for large datasets
8. **Consider mobile experience** with responsive design

## Migration Guide

**Before:**
```jsx
<div className="overflow-x-auto">
  <table className="min-w-full">
    <thead>
      <tr>
        <th>Name</th>
        <th>Email</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {users.map(user => (
        <tr key={user.id}>
          <td>{user.name}</td>
          <td>{user.email}</td>
          <td>
            <button onClick={() => handleEdit(user)}>Edit</button>
          </td>
        </tr>
      ))}
    </tbody>
  </table>
</div>
```

**After:**
```jsx
<UserTable
  users={users}
  onEdit={handleEdit}
  loading={isLoading}
  selectable={true}
/>
```
