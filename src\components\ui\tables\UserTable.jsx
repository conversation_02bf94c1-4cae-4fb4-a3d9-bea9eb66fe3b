import React from 'react';
import { FiMail, FiPhone, FiShield, FiCalendar } from 'react-icons/fi';
import DataTable from '../DataTable';
import { ViewButton, EditButton, DeleteButton, QuickActionBar } from '../buttons';

/**
 * Specialized UserTable component for displaying user data
 * Provides consistent user data presentation with built-in actions
 */
const UserTable = ({
  users = [],
  onView,
  onEdit,
  onDelete,
  onBulkAction,
  showActions = true,
  showVerificationStatus = true,
  showJoinDate = true,
  selectable = false,
  ...props
}) => {
  // Define user-specific columns
  const columns = [
    {
      key: 'avatar',
      label: '',
      sortable: false,
      hideOnMobile: false,
      width: '60px',
      render: (value, user) => (
        <div className="w-10 h-10 bg-violet-100 dark:bg-violet-900/30 rounded-full flex items-center justify-center flex-shrink-0">
          {user.profile_picture_thumbnail_url || user.profile_picture_url || user.avatar ? (
            <img
              src={user.profile_picture_thumbnail_url || user.profile_picture_url || user.avatar}
              alt={user.username}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <span className="text-sm font-semibold text-violet-600 dark:text-violet-400">
              {user.username?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() || 'U'}
            </span>
          )}
        </div>
      )
    },
    {
      key: 'username',
      label: 'User',
      sortable: true,
      render: (value, user) => (
        <div className="min-w-0">
          <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
            {user.username || user.name || 'N/A'}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center truncate">
            <FiMail className="w-3 h-3 mr-1 flex-shrink-0" />
            {user.email || 'N/A'}
            {showVerificationStatus && user.is_email_verified && (
              <FiShield className="w-3 h-3 ml-1 text-green-500 flex-shrink-0" title="Email verified" />
            )}
          </div>
          {user.mobile && (
            <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center truncate">
              <FiPhone className="w-3 h-3 mr-1 flex-shrink-0" />
              {user.mobile}
              {showVerificationStatus && user.is_mobile_verified && (
                <FiShield className="w-3 h-3 ml-1 text-green-500 flex-shrink-0" title="Mobile verified" />
              )}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'user_type',
      label: 'Role',
      sortable: true,
      hideOnMobile: true,
      render: (value) => {
        const getRoleColor = (userType) => {
          const colors = {
            admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
            teacher: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
            student: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
            institute: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
            sponsor: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
          };
          return colors[userType] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
        };

        return (
          <span className={`
            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
            ${getRoleColor(value)}
          `}>
            {value?.charAt(0).toUpperCase() + value?.slice(1) || 'Unknown'}
          </span>
        );
      }
    },
    {
      key: 'verification_status',
      label: 'Status',
      sortable: false,
      hideOnMobile: true,
      render: (value, user) => {
        if (!showVerificationStatus) return null;
        
        const isVerified = user.is_email_verified;
        return (
          <span className={`
            inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
            ${isVerified 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
            }
          `}>
            {isVerified ? 'Verified' : 'Unverified'}
          </span>
        );
      }
    }
  ];

  // Add join date column if enabled
  if (showJoinDate) {
    columns.push({
      key: 'created_at',
      label: 'Joined',
      sortable: true,
      hideOnMobile: true,
      render: (value) => {
        if (!value) return 'N/A';
        return (
          <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
            <FiCalendar className="w-3 h-3 mr-1" />
            {new Date(value).toLocaleDateString()}
          </div>
        );
      }
    });
  }

  // Add actions column if enabled
  if (showActions && (onView || onEdit || onDelete)) {
    columns.push({
      key: 'actions',
      label: 'Actions',
      sortable: false,
      width: '120px',
      render: (value, user) => (
        <QuickActionBar
          onView={onView ? () => onView(user) : undefined}
          onEdit={onEdit ? () => onEdit(user) : undefined}
          onDelete={onDelete ? () => onDelete(user) : undefined}
          showView={!!onView}
          showEdit={!!onEdit}
          showDelete={!!onDelete}
        />
      )
    });
  }

  // Bulk actions for user management
  const bulkActions = [];
  if (onBulkAction) {
    bulkActions.push(
      {
        label: 'Verify Selected',
        action: 'verify',
        onClick: (selectedUsers) => onBulkAction('verify', selectedUsers)
      },
      {
        label: 'Deactivate Selected',
        action: 'deactivate',
        onClick: (selectedUsers) => onBulkAction('deactivate', selectedUsers)
      },
      {
        label: 'Delete Selected',
        action: 'delete',
        onClick: (selectedUsers) => onBulkAction('delete', selectedUsers),
        variant: 'danger'
      }
    );
  }

  // Custom empty state for users
  const emptyState = (
    <div className="text-center py-12">
      <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
        <FiMail className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        No users found
      </h3>
      <p className="text-gray-500 dark:text-gray-400">
        No users match your current filters. Try adjusting your search criteria.
      </p>
    </div>
  );

  return (
    <DataTable
      data={users}
      columns={columns}
      selectable={selectable}
      bulkActions={bulkActions}
      emptyState={emptyState}
      {...props}
    />
  );
};

/**
 * Compact UserTable variant for smaller spaces
 */
export const CompactUserTable = ({
  users = [],
  onRowClick,
  ...props
}) => {
  const columns = [
    {
      key: 'user_info',
      label: 'User',
      sortable: true,
      render: (value, user) => (
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-violet-100 dark:bg-violet-900/30 rounded-full flex items-center justify-center flex-shrink-0">
            <span className="text-xs font-semibold text-violet-600 dark:text-violet-400">
              {user.username?.charAt(0).toUpperCase() || 'U'}
            </span>
          </div>
          <div className="min-w-0">
            <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
              {user.username || user.name || 'N/A'}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {user.email || 'N/A'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'user_type',
      label: 'Role',
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
          {value || 'Unknown'}
        </span>
      )
    }
  ];

  return (
    <DataTable
      data={users}
      columns={columns}
      onRowClick={onRowClick}
      pagination={false}
      className="compact-table"
      {...props}
    />
  );
};

export default UserTable;
