/**
 * Environment Configuration
 * Centralized configuration for environment variables
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:5000',
  WS_URL: import.meta.env.VITE_WS_URL || 'ws://localhost:8000',
  TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
};

// Exam Configuration
export const EXAM_CONFIG = {
  TIMEOUT: parseInt(import.meta.env.VITE_EXAM_TIMEOUT) || 3600,
  MAX_RECONNECT_ATTEMPTS: parseInt(import.meta.env.VITE_MAX_RECONNECT_ATTEMPTS) || 5,
  MAX_RECONNECT_DELAY: parseInt(import.meta.env.VITE_MAX_RECONNECT_DELAY) || 30000,
  RECONNECT_DELAY: parseInt(import.meta.env.VITE_RECONNECT_DELAY) || 1000,
  HEARTBEAT_FREQUENCY: parseInt(import.meta.env.VITE_HEARTBEAT_FREQUENCY) || 20000,
  MAX_STRIKES: parseInt(import.meta.env.VITE_MAX_STRIKES) || 3,
};

// Security Configuration
export const SECURITY_CONFIG = {
  ENABLE_ANTI_CHEAT: import.meta.env.VITE_ENABLE_ANTI_CHEAT === 'true',
  ENABLE_PROCTORING: import.meta.env.VITE_ENABLE_PROCTORING === 'true',
  ENABLE_BROWSER_LOCKDOWN: import.meta.env.VITE_ENABLE_BROWSER_LOCKDOWN === 'true',
};

// Development Configuration
export const DEV_CONFIG = {
  IS_DEVELOPMENT: import.meta.env.MODE === 'development',
  IS_PRODUCTION: import.meta.env.MODE === 'production',
  DEBUG_MODE: import.meta.env.VITE_DEBUG_MODE === 'true',
  LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL || 'info',
};

// Application Configuration
export const APP_CONFIG = {
  NAME: 'EduFair',
  VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  BUILD_DATE: import.meta.env.VITE_BUILD_DATE || new Date().toISOString(),
};

// Export all configurations
export default {
  API: API_CONFIG,
  EXAM: EXAM_CONFIG,
  SECURITY: SECURITY_CONFIG,
  DEV: DEV_CONFIG,
  APP: APP_CONFIG,
};

// Helper functions
export const getApiUrl = (endpoint = '') => {
  return `${API_CONFIG.BASE_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
};

export const getWsUrl = (endpoint = '') => {
  return `${API_CONFIG.WS_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
};

export const isFeatureEnabled = (feature) => {
  const featureMap = {
    'anti-cheat': SECURITY_CONFIG.ENABLE_ANTI_CHEAT,
    'proctoring': SECURITY_CONFIG.ENABLE_PROCTORING,
    'browser-lockdown': SECURITY_CONFIG.ENABLE_BROWSER_LOCKDOWN,
    'debug': DEV_CONFIG.DEBUG_MODE,
  };
  
  return featureMap[feature] || false;
};
