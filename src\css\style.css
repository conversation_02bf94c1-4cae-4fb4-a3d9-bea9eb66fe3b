@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=fallback');

@import 'tailwindcss';
@import './additional-styles/utility-patterns.css' layer(components);
@import '../styles/mobile-optimizations.css' layer(utilities);

@plugin "@tailwindcss/forms" {
  strategy: base;
}

@custom-variant dark (&:is(.dark *));
@custom-variant sidebar-expanded (&:is(.sidebar-expanded *));

@theme {
  --shadow-sm: 0 1px 1px 0 rgb(0 0 0 / 0.05), 0 1px 2px 0 rgb(0 0 0 / 0.02);

  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #bfc4cd;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;

  --color-violet-50: #f1eeff;
  --color-violet-100: #e6e1ff;
  --color-violet-200: #d2cbff;
  --color-violet-300: #b7acff;
  --color-violet-400: #9c8cff;
  --color-violet-500: #8470ff;
  --color-violet-600: #755ff8;
  --color-violet-700: #5d47de;
  --color-violet-800: #4634b1;
  --color-violet-900: #2f227c;
  --color-violet-950: #1c1357;

  --color-sky-50: #e3f3ff;
  --color-sky-100: #d1ecff;
  --color-sky-200: #b6e1ff;
  --color-sky-300: #a0d7ff;
  --color-sky-400: #7bc8ff;
  --color-sky-500: #67bfff;
  --color-sky-600: #56b1f3;
  --color-sky-700: #3193da;
  --color-sky-800: #1c71ae;
  --color-sky-900: #124d79;
  --color-sky-950: #0b324f;

  --color-green-50: #d2ffe2;
  --color-green-100: #b1fdcd;
  --color-green-200: #8bf0b0;
  --color-green-300: #67e294;
  --color-green-400: #4bd37d;
  --color-green-500: #3ec972;
  --color-green-600: #34bd68;
  --color-green-700: #239f52;
  --color-green-800: #15773a;
  --color-green-900: #0f5429;
  --color-green-950: #0a3f1e;

  --color-red-50: #ffe8e8;
  --color-red-100: #ffd1d1;
  --color-red-200: #ffb2b2;
  --color-red-300: #ff9494;
  --color-red-400: #ff7474;
  --color-red-500: #ff5656;
  --color-red-600: #fa4949;
  --color-red-700: #e63939;
  --color-red-800: #c52727;
  --color-red-900: #941818;
  --color-red-950: #600f0f;

  --color-yellow-50: #fff2c9;
  --color-yellow-100: #ffe7a0;
  --color-yellow-200: #ffe081;
  --color-yellow-300: #ffd968;
  --color-yellow-400: #f7cd4c;
  --color-yellow-500: #f0bb33;
  --color-yellow-600: #dfad2b;
  --color-yellow-700: #bc9021;
  --color-yellow-800: #816316;
  --color-yellow-900: #4f3d0e;
  --color-yellow-950: #342809;

  --font-inter: "Inter", "sans-serif";

  --text-xs: 0.75rem;
  --text-xs--line-height: 1.5;
  --text-sm: 0.875rem;
  --text-sm--line-height: 1.5715;
  --text-base: 1rem;
  --text-base--line-height: 1.5;
  --text-base--letter-spacing: -0.01em;
  --text-lg: 1.125rem;
  --text-lg--line-height: 1.5;
  --text-lg--letter-spacing: -0.01em;
  --text-xl: 1.25rem;
  --text-xl--line-height: 1.5;
  --text-xl--letter-spacing: -0.01em;
  --text-2xl: 1.5rem;
  --text-2xl--line-height: 1.33;
  --text-2xl--letter-spacing: -0.01em;
  --text-3xl: 1.88rem;
  --text-3xl--line-height: 1.33;
  --text-3xl--letter-spacing: -0.01em;
  --text-4xl: 2.25rem;
  --text-4xl--line-height: 1.25;
  --text-4xl--letter-spacing: -0.02em;
  --text-5xl: 3rem;
  --text-5xl--line-height: 1.25;
  --text-5xl--letter-spacing: -0.02em;
  --text-6xl: 3.75rem;
  --text-6xl--line-height: 1.2;
  --text-6xl--letter-spacing: -0.02em;

  --breakpoint-xs: 480px;
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

/* Custom animations for exam creation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}