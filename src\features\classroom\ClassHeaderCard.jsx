import React from 'react';

const ClassHeaderCard = ({ 
  classroom, 
  students, 
  currentTheme,
  isStudent = false 
}) => {
  return (
    <div 
      className="rounded-lg overflow-hidden shadow-sm border border-gray-200 dark:border-gray-700"
      style={{
        background: `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`,
      }}
    >
      <div className="p-6 text-white">
        <h2 className="text-2xl font-bold mb-2">{classroom?.name || 'Loading...'}</h2>
        <p className="text-blue-100 mb-4">{classroom?.description || 'No description available'}</p>
        <div className="flex items-center space-x-4 text-sm text-blue-100">
          <span>{classroom?.subject?.name || 'No subject'}</span>
          <span>•</span>
          <span>{students?.length || 0} students</span>
          {!isStudent && classroom?.teacher && (
            <>
              <span>•</span>
              <span>Teacher: {classroom.teacher.username}</span>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClassHeaderCard;
