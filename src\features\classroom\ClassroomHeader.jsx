import React from 'react';
import { FiArrowLeft, FiMoreVertical, FiUsers, FiBookOpen } from 'react-icons/fi';

const ClassroomHeader = ({
  classroom,
  onBack,
  currentTheme,
  isStudent = false,
  actions = null
}) => {
  // Theme classes
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  return (
    <div className={`${bgSecondary} border-b ${borderColor} shadow-sm`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-6">
          {/* Left Section */}
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 ${textSecondary} hover:${textPrimary} transition-colors`}
              aria-label="Go back"
            >
              <FiArrowLeft className="w-5 h-5" />
            </button>

            <div className="min-w-0 flex-1">
              <h1 className={`text-2xl font-bold ${textPrimary} truncate`}>
                {classroom?.name || 'Loading...'}
              </h1>
              <div className="flex items-center space-x-4 mt-2">
                {classroom?.subject?.name && (
                  <div className="flex items-center space-x-1">
                    <FiBookOpen className={`w-4 h-4 ${textSecondary}`} />
                    <span className={`text-sm ${textSecondary}`}>
                      {classroom.subject.name}
                    </span>
                  </div>
                )}
                {classroom?.students && (
                  <div className="flex items-center space-x-1">
                    <FiUsers className={`w-4 h-4 ${textSecondary}`} />
                    <span className={`text-sm ${textSecondary}`}>
                      {classroom.students.length} students
                    </span>
                  </div>
                )}
              </div>
              {classroom?.description && (
                <p className={`text-sm ${textSecondary} mt-2 line-clamp-2 max-w-2xl`}>
                  {classroom.description}
                </p>
              )}
            </div>
          </div>

          {/* Right Section */}
          <div className="flex items-center space-x-2">
            {actions}
            <button className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 ${textSecondary} transition-colors`}>
              <FiMoreVertical className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClassroomHeader;
