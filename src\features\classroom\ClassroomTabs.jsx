import React from 'react';

const ClassroomTabs = ({ 
  tabs, 
  activeTab, 
  onTabChange, 
  currentTheme 
}) => {
  // Theme classes
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  return (
    <div className={`${bgSecondary} border-b ${borderColor}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-600 text-blue-600'
                    : `border-transparent ${textSecondary} hover:${textPrimary} hover:border-gray-300`
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

export default ClassroomTabs;
