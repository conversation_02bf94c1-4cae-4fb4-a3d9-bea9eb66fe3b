import React from 'react';
import { FiCalendar, FiClock, FiFileText, FiArrowRight } from 'react-icons/fi';
import EmptyState from '../../components/ui/EmptyState';

const UpcomingAssignments = ({ 
  assignments = [], 
  currentTheme = 'light',
  onViewAssignment,
  isStudent = false
}) => {
  // Theme classes
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  const formatDueDate = (dateString) => {
    if (!dateString) return 'No due date';
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = date - now;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays < 0) return 'Overdue';
      if (diffDays === 0) return 'Due today';
      if (diffDays === 1) return 'Due tomorrow';
      if (diffDays <= 7) return `Due in ${diffDays} days`;
      
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Invalid date';
    }
  };

  const getDueDateColor = (dateString) => {
    if (!dateString) return 'text-gray-500';
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = date - now;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays < 0) return 'text-red-600 dark:text-red-400';
      if (diffDays === 0) return 'text-orange-600 dark:text-orange-400';
      if (diffDays <= 3) return 'text-yellow-600 dark:text-yellow-400';
      return 'text-green-600 dark:text-green-400';
    } catch {
      return 'text-gray-500';
    }
  };

  if (!assignments || assignments.length === 0) {
    return (
      <div className={`${bgSecondary} rounded-xl p-8 border ${borderColor}`}>
        <EmptyState
          icon={FiFileText}
          title="No upcoming work"
          description={isStudent ? "You're all caught up!" : "No assignments have been created yet."}
          currentTheme={currentTheme}
        />
      </div>
    );
  }

  return (
    <div className={`${bgSecondary} rounded-xl border ${borderColor}`}>
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className={`text-lg font-semibold ${textPrimary}`}>
          Upcoming
        </h3>
      </div>
      
      <div className="p-6 space-y-4">
        {assignments.slice(0, 5).map((assignment) => (
          <div
            key={assignment.id}
            className={`p-4 border ${borderColor} rounded-lg hover:shadow-sm transition-shadow cursor-pointer`}
            onClick={() => onViewAssignment && onViewAssignment(assignment)}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 flex-1">
                <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <FiFileText className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className={`font-medium ${textPrimary} truncate`}>
                    {assignment.title || 'Untitled Assignment'}
                  </h4>
                  
                  <p className={`text-sm ${textSecondary} mt-1 line-clamp-2`}>
                    {assignment.description || 'No description available'}
                  </p>
                  
                  <div className="flex items-center space-x-4 mt-2">
                    <div className="flex items-center space-x-1">
                      <FiCalendar className={`w-4 h-4 ${getDueDateColor(assignment.due_date)}`} />
                      <span className={`text-sm ${getDueDateColor(assignment.due_date)}`}>
                        {formatDueDate(assignment.due_date)}
                      </span>
                    </div>
                    
                    {assignment.points && (
                      <div className="flex items-center space-x-1">
                        <span className={`text-sm ${textSecondary}`}>
                          {assignment.points} points
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              {onViewAssignment && (
                <FiArrowRight className={`w-5 h-5 ${textSecondary} flex-shrink-0 ml-2`} />
              )}
            </div>
          </div>
        ))}
        
        {assignments.length > 5 && (
          <div className="text-center pt-4">
            <button className={`text-sm text-violet-600 dark:text-violet-400 hover:underline`}>
              View all assignments ({assignments.length})
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default UpcomingAssignments;
