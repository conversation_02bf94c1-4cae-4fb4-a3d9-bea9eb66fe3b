import React from 'react';
import { FiFileText } from 'react-icons/fi';

const GradesTab = ({ currentTheme }) => {
  // Theme classes
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  return (
    <div className="space-y-6">
      <div className={`${bgSecondary} rounded-lg p-6 border ${borderColor}`}>
        <h2 className={`text-xl font-semibold ${textPrimary} mb-6`}>Grades</h2>

        {/* Grade Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">Overall Grade</h3>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">A-</p>
            <p className="text-sm text-blue-600 dark:text-blue-400">92.5%</p>
          </div>
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
            <h3 className="text-sm font-medium text-green-800 dark:text-green-200">Assignments</h3>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">8/10</p>
            <p className="text-sm text-green-600 dark:text-green-400">Completed</p>
          </div>
          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
            <h3 className="text-sm font-medium text-purple-800 dark:text-purple-200">Participation</h3>
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">95%</p>
            <p className="text-sm text-purple-600 dark:text-purple-400">Excellent</p>
          </div>
        </div>

        {/* Grades Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className={`border-b ${borderColor}`}>
                <th className={`text-left py-3 px-4 font-medium ${textPrimary}`}>Assignment</th>
                <th className={`text-left py-3 px-4 font-medium ${textPrimary}`}>Due Date</th>
                <th className={`text-left py-3 px-4 font-medium ${textPrimary}`}>Status</th>
                <th className={`text-left py-3 px-4 font-medium ${textPrimary}`}>Grade</th>
              </tr>
            </thead>
            <tbody>
              <tr className={`border-b ${borderColor} hover:bg-gray-50 dark:hover:bg-gray-700/50`}>
                <td className={`py-3 px-4 ${textPrimary}`}>Sample Assignment 1</td>
                <td className={`py-3 px-4 ${textSecondary}`}>Oct 15, 2024</td>
                <td className="py-3 px-4">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    Submitted
                  </span>
                </td>
                <td className={`py-3 px-4 font-medium ${textPrimary}`}>95/100</td>
              </tr>
              <tr className={`border-b ${borderColor} hover:bg-gray-50 dark:hover:bg-gray-700/50`}>
                <td className={`py-3 px-4 ${textPrimary}`}>Quiz 1</td>
                <td className={`py-3 px-4 ${textSecondary}`}>Oct 20, 2024</td>
                <td className="py-3 px-4">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                    Pending
                  </span>
                </td>
                <td className={`py-3 px-4 ${textSecondary}`}>-</td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* No Grades State */}
        <div className="text-center py-12">
          <FiFileText className={`w-12 h-12 ${textSecondary} mx-auto mb-4`} />
          <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>No grades yet</h3>
          <p className={textSecondary}>Grades will appear here once your teacher posts them.</p>
        </div>
      </div>
    </div>
  );
};

export default GradesTab;
