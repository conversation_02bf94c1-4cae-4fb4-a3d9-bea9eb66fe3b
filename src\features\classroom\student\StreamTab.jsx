import React from 'react';
import { FiMessageSquare } from 'react-icons/fi';
import AnnouncementCard from '../AnnouncementCard';
import ClassHeaderCard from '../ClassHeaderCard';
import LoadingSpinner from '../../../components/ui/LoadingSpinner';
import EmptyState from '../../../components/ui/EmptyState';
import UpcomingAssignments from '../UpcomingAssignments';

const StreamTab = ({ 
  classroom, 
  students, 
  announcements, 
  announcementsLoading, 
  currentTheme 
}) => {
  // Theme classes
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Main Content */}
      <div className="lg:col-span-3 space-y-6">
        {/* Class Header Card */}
        <ClassHeaderCard 
          classroom={classroom}
          students={students}
          currentTheme={currentTheme}
          isStudent={true}
        />

        {/* Announcements */}
        <div className="space-y-4">
          {announcementsLoading ? (
            <div className={`${bgSecondary} rounded-lg p-8 border ${borderColor}`}>
              <LoadingSpinner
                size="md"
                text="Loading announcements..."
                currentTheme={currentTheme}
              />
            </div>
          ) : announcements && announcements.length > 0 ? (
            announcements.map((announcement) => (
              <AnnouncementCard
                key={announcement.id}
                announcement={announcement}
                currentTheme={currentTheme}
                isTeacher={false}
              />
            ))
          ) : (
            <EmptyState
              icon={FiMessageSquare}
              title="No announcements yet"
              description="Your teacher hasn't posted any announcements."
              currentTheme={currentTheme}
            />
          )}
        </div>
      </div>

      {/* Sidebar */}
      <div className="space-y-6">
        {/* Upcoming Assignments */}
        <UpcomingAssignments
          assignments={[
            {
              id: 1,
              title: "Sample Assignment",
              description: "Complete the reading assignment and submit your analysis.",
              due_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
              points: 100
            }
          ]}
          currentTheme={currentTheme}
          isStudent={true}
          onViewAssignment={(assignment) => {
            // Handle assignment click
            console.log('View assignment:', assignment);
          }}
        />
      </div>
    </div>
  );
};

export default StreamTab;
