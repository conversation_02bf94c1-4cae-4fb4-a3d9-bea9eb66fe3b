import React from 'react';
import { FiRefreshCw, <PERSON><PERSON>lock, FiMail, FiPlus } from 'react-icons/fi';

const RequestsTab = ({ 
  classroom, 
  refreshingRequests,
  onRefreshRequests,
  onRequestStudents,
  currentTheme 
}) => {
  // Theme classes
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  const getInitials = (name) => {
    if (!name) return '?';
    return name.charAt(0).toUpperCase();
  };

  return (
    <div className="space-y-3 sm:space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <h3 className={`text-lg font-semibold ${textPrimary}`}>
            Student Requests
          </h3>
          <button
            onClick={onRefreshRequests}
            disabled={refreshingRequests}
            className={`flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 ${textSecondary} rounded-full font-medium shadow hover:bg-gray-200 dark:hover:bg-gray-600 transition disabled:opacity-50 disabled:cursor-not-allowed`}
            title="Refresh requests"
          >
            <FiRefreshCw className={`w-4 h-4 ${refreshingRequests ? 'animate-spin' : ''}`} />
            <span className="hidden sm:inline">{refreshingRequests ? 'Refreshing...' : 'Refresh'}</span>
          </button>
        </div>
        <button
          onClick={onRequestStudents}
          className="px-6 py-2 bg-violet-600 dark:bg-violet-500 text-white rounded-full font-semibold shadow hover:bg-violet-700 dark:hover:bg-violet-600 transition"
        >
          + Request Students
        </button>
      </div>

      {refreshingRequests ? (
        <div className={`${bgSecondary} p-8 rounded-2xl shadow border ${borderColor}`}>
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600"></div>
            <span className={`ml-3 ${textSecondary}`}>Loading requests...</span>
          </div>
        </div>
      ) : classroom?.class_requests?.length > 0 ? (
        classroom.class_requests.map((req) => (
          <div
            key={req.id}
            className={`${bgSecondary} p-4 sm:p-6 rounded-2xl shadow flex flex-col sm:flex-row items-center gap-3 sm:gap-4 border ${borderColor}`}
          >
            {/* Profile Picture */}
            <div className="flex-shrink-0">
              {req.student_user?.profile_picture ? (
                <img
                  src={req.student_user.profile_picture}
                  alt={req.student_user.username}
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <div className="w-12 h-12 bg-violet-100 dark:bg-violet-900/30 rounded-full flex items-center justify-center">
                  <span className="text-violet-600 dark:text-violet-400 font-semibold text-lg">
                    {getInitials(req.student_user?.username)}
                  </span>
                </div>
              )}
            </div>

            {/* Student Info */}
            <div className="flex-1 text-center sm:text-left">
              <h4 className={`font-semibold ${textPrimary}`}>
                {req.student_user?.username || 'Unknown Student'}
              </h4>
              <div className="flex items-center justify-center sm:justify-start space-x-2 mt-1">
                <FiMail className={`w-3 h-3 ${textSecondary}`} />
                <span className={`text-sm ${textSecondary}`}>
                  {req.student_user?.email || 'No email'}
                </span>
              </div>
              <div className="flex items-center justify-center sm:justify-start space-x-2 mt-1">
                <FiClock className={`w-3 h-3 ${textSecondary}`} />
                <span className={`text-sm ${textSecondary}`}>Pending response</span>
              </div>
            </div>

            {/* Status Badge */}
            <div className="flex items-center space-x-2">
              <span className="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 rounded-full text-sm font-medium">
                Pending
              </span>
            </div>
          </div>
        ))
      ) : (
        <div className={`text-center py-12 ${bgSecondary} rounded-2xl shadow border ${borderColor}`}>
          <FiClock className={`w-12 h-12 ${textSecondary} mx-auto mb-4`} />
          <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>No pending invites</h3>
          <p className={`${textSecondary} mb-4`}>
            Students you invite will appear here until they accept or decline your invitation.
          </p>
          <button
            onClick={onRequestStudents}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
          >
            <FiPlus className="w-4 h-4" />
            <span>Invite Students</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default RequestsTab;
