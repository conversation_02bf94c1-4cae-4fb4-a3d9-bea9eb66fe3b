import React from 'react';
import { FiUsers, FiPlus, FiUserMinus } from 'react-icons/fi';
import UserList from '../../../components/ui/UserList';

const StudentsTab = ({ 
  classroom, 
  students, 
  onRequestStudents,
  currentTheme 
}) => {
  // Theme classes
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  const getInitials = (name) => {
    if (!name) return '?';
    return name.charAt(0).toUpperCase();
  };

  const getAvatarColor = (index) => {
    const colors = [
      'bg-blue-600', 'bg-green-600', 'bg-purple-600', 'bg-red-600', 
      'bg-yellow-600', 'bg-indigo-600', 'bg-pink-600', 'bg-gray-600'
    ];
    return colors[index % colors.length];
  };

  return (
    <div className="space-y-6">
      <div className={`${bgSecondary} rounded-lg p-6 border ${borderColor}`}>
        <div className="flex items-center justify-between mb-6">
          <h2 className={`text-xl font-semibold ${textPrimary}`}>Students</h2>
          <button
            onClick={onRequestStudents}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <FiPlus className="w-4 h-4" />
            <span>Request Students</span>
          </button>
        </div>

        {/* Students List */}
        <UserList
          users={students || []}
          onFollow={(user) => console.log('Follow student:', user)}
          onVisitProfile={(user) => console.log('Visit profile:', user)}
          showEmail={true}
          showPhone={false}
          showLocation={false}
          searchable={true}
          searchPlaceholder="Search students..."
          emptyTitle="No students enrolled"
          emptyDescription="Start building your class by requesting students to join."
          emptyIcon={FiUsers}
          size="md"
          className={currentTheme === "dark" ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200"}
          customActions={[
            {
              id: 'remove',
              label: 'Remove from Class',
              icon: FiUserMinus,
              onClick: (student) => console.log('Remove student:', student)
            }
          ]}
        />

        {/* Class Statistics */}
        {students && students.length > 0 && (
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <p className={`text-2xl font-bold ${textPrimary}`}>{students.length}</p>
                <p className={`text-sm ${textSecondary}`}>Total Students</p>
              </div>
              <div className="text-center">
                <p className={`text-2xl font-bold text-green-600`}>{students.length}</p>
                <p className={`text-sm ${textSecondary}`}>Active</p>
              </div>
              <div className="text-center">
                <p className={`text-2xl font-bold text-blue-600`}>
                  {Math.round((students.length / (students.length + 2)) * 100)}%
                </p>
                <p className={`text-sm ${textSecondary}`}>Engagement</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentsTab;
