# Student Dashboard Redux Integration

This module contains the Redux slice and related functionality for the student dashboard APIs.

## Overview

The student dashboard integration provides access to comprehensive student data through multiple API endpoints:

- `/api/student/dashboard` - Complete dashboard data
- `/api/student/dashboard/summary` - Summary statistics
- `/api/student/dashboard/quick-actions` - Quick action items
- `/api/student/dashboard/performance` - Performance and study metrics
- `/api/student/dashboard/schedule` - Student schedule

## Files

### `StudentDashboardSlice.js`
Redux slice containing:
- **State**: Comprehensive student dashboard state including summary, performance, assignments, etc.
- **Actions**: Async thunks for fetching data from all dashboard endpoints
- **Reducers**: State management for loading, success, and error states

### `StudentDashboardSlice.test.js`
Unit tests for the Redux slice functionality.

## Usage

### In Components

```javascript
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchStudentDashboard,
  fetchStudentDashboardSummary,
  fetchStudentQuickActions,
  fetchStudentPerformance,
  fetchStudentSchedule
} from '../features/student/StudentDashboardSlice';

function StudentComponent() {
  const dispatch = useDispatch();
  const { 
    summary,
    quickActions,
    performance,
    studyMetrics,
    assignments,
    recentActivity,
    loading,
    error
  } = useSelector((state) => state.studentDashboard);

  useEffect(() => {
    // Fetch all dashboard data
    dispatch(fetchStudentDashboard());
    
    // Or fetch specific sections
    dispatch(fetchStudentDashboardSummary());
    dispatch(fetchStudentQuickActions());
    dispatch(fetchStudentPerformance());
    dispatch(fetchStudentSchedule());
  }, [dispatch]);

  // Use the data in your component
  return (
    <div>
      <h1>Welcome, {student?.username}!</h1>
      <p>Total Classes: {summary.total_classes}</p>
      <p>Pending Assignments: {summary.pending_assignments}</p>
      {/* ... */}
    </div>
  );
}
```

### Available Data

#### Summary Data
- `total_classes` - Number of enrolled classes
- `pending_assignments` - Number of pending assignments
- `upcoming_exams` - Number of upcoming exams
- `overall_grade` - Overall grade percentage
- `unread_notifications` - Number of unread notifications
- `total_points` - Total points earned
- `current_level` - Current student level
- `quick_actions_count` - Number of quick actions available

#### Performance Data
- `overall_grade` - Overall grade percentage
- `subject_grades` - Grades by subject
- `recent_scores` - Recent test/assignment scores
- `improvement_trend` - Performance trend (stable, improving, declining)
- `rank_in_class` - Class ranking

#### Study Metrics
- `total_points` - Total points earned
- `level` - Current level
- `tasks_completed` - Number of completed tasks
- `exams_taken` - Number of exams taken
- `average_grade` - Average grade across all assessments
- `badges_earned` - Array of earned badges

#### Other Data
- `quickActions` - Array of quick action items
- `assignments` - Array of assignments
- `recentActivity` - Array of recent activities
- `schedule` - Array of scheduled items
- `student` - Student profile information
- `classes` - Array of enrolled classes
- `exams` - Array of exams

## Integration with Existing Components

The `StudentDashboard.jsx` component has been updated to use these new APIs while maintaining backward compatibility with existing data sources. The component will:

1. Fetch data from new student dashboard APIs
2. Use new data when available
3. Fall back to existing data sources when new data is not available
4. Display additional information from the new APIs (assignments, recent activity, study metrics)

## Error Handling

Each API call has its own error state in the Redux store:
- `error.dashboard` - Main dashboard API errors
- `error.summary` - Summary API errors
- `error.quickActions` - Quick actions API errors
- `error.performance` - Performance API errors
- `error.schedule` - Schedule API errors

## Loading States

Similarly, each API call has its own loading state:
- `loading.dashboard` - Main dashboard API loading
- `loading.summary` - Summary API loading
- `loading.quickActions` - Quick actions API loading
- `loading.performance` - Performance API loading
- `loading.schedule` - Schedule API loading

## Testing

Run the tests with:
```bash
npm test StudentDashboardSlice.test.js
```

The tests cover:
- Initial state verification
- Reducer actions (clearErrors, clearDashboardData)
- Async thunk handling (pending, fulfilled, rejected states)
- Data transformation and state updates
