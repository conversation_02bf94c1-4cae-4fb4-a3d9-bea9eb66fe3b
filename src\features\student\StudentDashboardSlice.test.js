import { configureStore } from '@reduxjs/toolkit';
import studentDashboardReducer, {
  fetchStudentDashboard,
  fetchStudentDashboardSummary,
  fetchStudentQuickActions,
  fetchStudentPerformance,
  fetchStudentSchedule,
  clearErrors,
  clearDashboardData
} from './StudentDashboardSlice';

// Mock axios
jest.mock('axios');

describe('StudentDashboardSlice', () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        studentDashboard: studentDashboardReducer,
      },
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().studentDashboard;
      
      expect(state.dashboardData).toBeNull();
      expect(state.summary.total_classes).toBe(0);
      expect(state.quickActions).toEqual([]);
      expect(state.performance.overall_grade).toBe(0.0);
      expect(state.studyMetrics.level).toBe(1);
      expect(state.schedule).toEqual([]);
      expect(state.student).toBeNull();
      expect(state.classes).toEqual([]);
      expect(state.exams).toEqual([]);
      expect(state.assignments).toEqual([]);
      expect(state.recentActivity).toEqual([]);
      
      // Check loading states
      expect(state.loading.dashboard).toBe(false);
      expect(state.loading.summary).toBe(false);
      expect(state.loading.quickActions).toBe(false);
      expect(state.loading.performance).toBe(false);
      expect(state.loading.schedule).toBe(false);
      
      // Check error states
      expect(state.error.dashboard).toBeNull();
      expect(state.error.summary).toBeNull();
      expect(state.error.quickActions).toBeNull();
      expect(state.error.performance).toBeNull();
      expect(state.error.schedule).toBeNull();
    });
  });

  describe('reducers', () => {
    it('should clear errors', () => {
      // First set some errors
      store.dispatch({
        type: fetchStudentDashboard.rejected.type,
        payload: 'Dashboard error'
      });
      
      // Then clear them
      store.dispatch(clearErrors());
      
      const state = store.getState().studentDashboard;
      expect(state.error.dashboard).toBeNull();
      expect(state.error.summary).toBeNull();
      expect(state.error.quickActions).toBeNull();
      expect(state.error.performance).toBeNull();
      expect(state.error.schedule).toBeNull();
    });

    it('should clear dashboard data', () => {
      // First set some data
      store.dispatch({
        type: fetchStudentDashboard.fulfilled.type,
        payload: {
          data: {
            student: { username: 'test' },
            classes: [{ id: 1, name: 'Math' }],
            exams: [{ id: 1, title: 'Test Exam' }],
            assignments: [{ id: 1, name: 'Assignment 1' }],
            recent_activity: [{ id: 1, title: 'Activity 1' }]
          }
        }
      });
      
      // Then clear it
      store.dispatch(clearDashboardData());
      
      const state = store.getState().studentDashboard;
      expect(state.dashboardData).toBeNull();
      expect(state.student).toBeNull();
      expect(state.classes).toEqual([]);
      expect(state.exams).toEqual([]);
      expect(state.assignments).toEqual([]);
      expect(state.recentActivity).toEqual([]);
    });
  });

  describe('async thunks', () => {
    it('should handle fetchStudentDashboard pending', () => {
      store.dispatch({ type: fetchStudentDashboard.pending.type });
      
      const state = store.getState().studentDashboard;
      expect(state.loading.dashboard).toBe(true);
      expect(state.error.dashboard).toBeNull();
    });

    it('should handle fetchStudentDashboard fulfilled', () => {
      const mockData = {
        data: {
          student: { username: 'testuser', email: '<EMAIL>' },
          classes: [{ id: 1, name: 'Mathematics' }],
          exams: [{ id: 1, title: 'Math Exam' }],
          assignments: [{ id: 1, name: 'Math Assignment' }],
          recent_activity: [{ id: 1, title: 'Completed Quiz' }],
          schedule: [{ id: 1, title: 'Math Class' }],
          performance: { overall_grade: 85.5 },
          study_metrics: { level: 3, total_points: 150 },
          quick_actions: [{ id: 1, title: 'Submit Assignment' }],
          last_updated: '2025-07-23T23:44:06.912691+00:00'
        }
      };

      store.dispatch({
        type: fetchStudentDashboard.fulfilled.type,
        payload: mockData
      });
      
      const state = store.getState().studentDashboard;
      expect(state.loading.dashboard).toBe(false);
      expect(state.dashboardData).toEqual(mockData.data);
      expect(state.student).toEqual(mockData.data.student);
      expect(state.classes).toEqual(mockData.data.classes);
      expect(state.exams).toEqual(mockData.data.exams);
      expect(state.assignments).toEqual(mockData.data.assignments);
      expect(state.recentActivity).toEqual(mockData.data.recent_activity);
      expect(state.schedule).toEqual(mockData.data.schedule);
      expect(state.performance).toEqual(mockData.data.performance);
      expect(state.studyMetrics).toEqual(mockData.data.study_metrics);
      expect(state.quickActions).toEqual(mockData.data.quick_actions);
      expect(state.lastUpdated.dashboard).toBe(mockData.data.last_updated);
    });

    it('should handle fetchStudentDashboard rejected', () => {
      const errorMessage = 'Failed to fetch dashboard';
      
      store.dispatch({
        type: fetchStudentDashboard.rejected.type,
        payload: errorMessage
      });
      
      const state = store.getState().studentDashboard;
      expect(state.loading.dashboard).toBe(false);
      expect(state.error.dashboard).toBe(errorMessage);
    });

    it('should handle fetchStudentDashboardSummary fulfilled', () => {
      const mockSummary = {
        data: {
          total_classes: 5,
          pending_assignments: 3,
          upcoming_exams: 2,
          overall_grade: 87.5,
          unread_notifications: 1,
          total_points: 250,
          current_level: 4,
          quick_actions_count: 2,
          last_updated: '2025-07-23T23:44:06.912691+00:00'
        }
      };

      store.dispatch({
        type: fetchStudentDashboardSummary.fulfilled.type,
        payload: mockSummary
      });
      
      const state = store.getState().studentDashboard;
      expect(state.loading.summary).toBe(false);
      expect(state.summary).toEqual(mockSummary.data);
      expect(state.lastUpdated.summary).toBe(mockSummary.data.last_updated);
    });
  });
});
