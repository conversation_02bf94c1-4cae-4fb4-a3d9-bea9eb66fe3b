import { useStudentQuickActions } from '../../hooks/useStudentDashboard';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  FiArrowRight,
  FiClock,
  FiAlertCircle,
  FiCheckCircle,
  FiRefreshCw
} from 'react-icons/fi';

/**
 * StudentQuickActions - A component that displays quick action items for students
 * Uses the new student dashboard APIs through the custom hook
 */
function StudentQuickActions({ className = '', maxItems = 6, showRefresh = false }) {
  const { currentTheme } = useThemeProvider();
  const { quickActions, isLoading, error, refresh } = useStudentQuickActions();

  // Theme classes
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";

  if (error) {
    return (
      <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-red-200 dark:border-red-700 ${className}`}>
        <div className="text-center">
          <FiAlertCircle className="w-12 h-12 text-red-400 mx-auto mb-3" />
          <p className="text-red-600 dark:text-red-400 mb-2">Failed to load quick actions</p>
          {showRefresh && (
            <button
              onClick={refresh}
              className="px-3 py-1 text-sm bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors"
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!quickActions || quickActions.length === 0) {
    return (
      <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="text-center py-8">
          <FiCheckCircle className="w-12 h-12 text-green-400 mx-auto mb-3" />
          <p className={`${textSecondary}`}>No quick actions available</p>
          <p className={`text-sm ${textSecondary} mt-1`}>You're all caught up!</p>
        </div>
      </div>
    );
  }

  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const handleActionClick = (action) => {
    // Handle action click - could navigate to specific page or trigger action
    console.log('Quick action clicked:', action);
    
    // Example: Navigate based on action type
    if (action.action_type === 'navigate' && action.url) {
      window.location.href = action.url;
    } else if (action.action_type === 'submit_assignment' && action.assignment_id) {
      // Navigate to assignment submission page
      window.location.href = `/student/assignments/${action.assignment_id}`;
    } else if (action.action_type === 'take_exam' && action.exam_id) {
      // Navigate to exam page
      window.location.href = `/student/take-exam/${action.exam_id}`;
    }
  };

  const displayActions = quickActions.slice(0, maxItems);

  return (
    <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className={`text-lg font-semibold ${textPrimary}`}>Quick Actions</h2>
        <div className="flex items-center gap-2">
          <span className={`text-sm ${textSecondary}`}>
            {quickActions.length} action{quickActions.length !== 1 ? 's' : ''}
          </span>
          {showRefresh && (
            <button
              onClick={refresh}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              title="Refresh actions"
            >
              <FiRefreshCw className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      <div className="space-y-3">
        {displayActions.map((action) => (
          <div
            key={action.id}
            onClick={() => handleActionClick(action)}
            className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer group"
          >
            <div className="flex items-start justify-between mb-2">
              <h3 className={`font-medium ${textPrimary} group-hover:text-violet-600 dark:group-hover:text-violet-400 transition-colors`}>
                {action.title}
              </h3>
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(action.priority)}`}>
                  {action.priority || 'normal'}
                </span>
                <FiArrowRight className="w-4 h-4 text-gray-400 group-hover:text-violet-600 dark:group-hover:text-violet-400 transition-colors" />
              </div>
            </div>
            
            <p className={`text-sm ${textSecondary} mb-3`}>{action.description}</p>
            
            <div className="flex items-center justify-between text-xs">
              {action.due_date && (
                <span className={`flex items-center gap-1 ${textSecondary}`}>
                  <FiClock className="w-3 h-3" />
                  Due: {new Date(action.due_date).toLocaleDateString()}
                </span>
              )}
              
              {action.category && (
                <span className={`px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs ${textSecondary}`}>
                  {action.category}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {quickActions.length > maxItems && (
        <div className="mt-4 text-center">
          <p className={`text-sm ${textSecondary}`}>
            Showing {maxItems} of {quickActions.length} actions
          </p>
        </div>
      )}
    </div>
  );
}

export default StudentQuickActions;
