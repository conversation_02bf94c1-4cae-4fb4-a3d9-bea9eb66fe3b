import { useStudentSummary } from '../../hooks/useStudentDashboard';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  FiUsers,
  FiBookOpen,
  FiClock,
  FiTrendingUp,
  FiBell,
  FiStar,
  FiRefreshCw
} from 'react-icons/fi';

/**
 * StudentSummaryCard - A reusable component that displays student summary statistics
 * Uses the new student dashboard APIs through the custom hook
 */
function StudentSummaryCard({ className = '', showRefresh = false }) {
  const { currentTheme } = useThemeProvider();
  const { summary, isLoading, error, refresh } = useStudentSummary();

  // Theme classes
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";

  if (error) {
    return (
      <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-red-200 dark:border-red-700 ${className}`}>
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400">Failed to load summary data</p>
          {showRefresh && (
            <button
              onClick={refresh}
              className="mt-2 px-3 py-1 text-sm bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors"
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="text-center">
                <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-full mb-2"></div>
                <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-3/4 mx-auto"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const stats = [
    {
      label: 'Total Classes',
      value: summary.total_classes,
      icon: FiUsers,
      color: 'blue'
    },
    {
      label: 'Pending Assignments',
      value: summary.pending_assignments,
      icon: FiBookOpen,
      color: 'orange'
    },
    {
      label: 'Upcoming Exams',
      value: summary.upcoming_exams,
      icon: FiClock,
      color: 'red'
    },
    {
      label: 'Overall Grade',
      value: `${summary.overall_grade.toFixed(1)}%`,
      icon: FiTrendingUp,
      color: 'green'
    },
    {
      label: 'Notifications',
      value: summary.unread_notifications,
      icon: FiBell,
      color: 'purple'
    },
    {
      label: 'Total Points',
      value: summary.total_points,
      icon: FiStar,
      color: 'yellow'
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400',
      orange: 'bg-orange-100 text-orange-600 dark:bg-orange-900/30 dark:text-orange-400',
      red: 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400',
      green: 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400',
      purple: 'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400',
      yellow: 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400'
    };
    return colors[color] || colors.blue;
  };

  return (
    <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className={`text-lg font-semibold ${textPrimary}`}>Student Summary</h2>
        {showRefresh && (
          <button
            onClick={refresh}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            title="Refresh data"
          >
            <FiRefreshCw className="w-4 h-4" />
          </button>
        )}
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.label} className="text-center">
              <div className={`w-12 h-12 mx-auto mb-2 rounded-lg flex items-center justify-center ${getColorClasses(stat.color)}`}>
                <Icon className="w-6 h-6" />
              </div>
              <p className={`text-lg font-bold ${textPrimary}`}>{stat.value}</p>
              <p className={`text-xs ${textSecondary}`}>{stat.label}</p>
            </div>
          );
        })}
      </div>

      {summary.last_updated && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className={`text-xs ${textSecondary} text-center`}>
            Last updated: {new Date(summary.last_updated).toLocaleString()}
          </p>
        </div>
      )}
    </div>
  );
}

export default StudentSummaryCard;
