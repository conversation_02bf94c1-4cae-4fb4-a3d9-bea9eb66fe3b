import { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchClassroomsPaginated,
  fetchStudentsPaginated,
  clearPaginatedData
} from '../store/slices/ClassroomSlice';

/**
 * Custom hook for managing dropdown data with search and pagination
 * @param {string} type - 'classrooms' or 'students'
 * @param {Array} initialSelected - Initially selected items
 */
export const useDropdownData = (type, initialSelected = []) => {
  const dispatch = useDispatch();
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Get data from Redux store
  const {
    paginatedClassrooms,
    paginatedStudents,
    classroomsPagination,
    studentsPagination,
    loading: storeLoading
  } = useSelector(state => state.classroom);

  // Determine which data to use based on type
  const data = type === 'classrooms' ? paginatedClassrooms : paginatedStudents;
  const pagination = type === 'classrooms' ? classroomsPagination : studentsPagination;
  const loading = storeLoading || isLoading;

  // Transform data to consistent format for dropdown
  const transformedData = data.map(item => {
    if (type === 'classrooms') {
      return {
        id: item.id,
        name: item.name,
        description: `${item.students?.length || 0} students`,
        ...item
      };
    } else {
      return {
        id: item.id,
        name: item.username || item.name,
        description: item.email,
        ...item
      };
    }
  });

  // Load initial data
  const loadData = useCallback((search = '', reset = false) => {
    const skip = reset ? 0 : pagination.skip;
    const params = {
      skip,
      limit: pagination.limit,
      search: search.trim()
    };

    setIsLoading(true);

    const action = type === 'classrooms'
      ? fetchClassroomsPaginated(params)
      : fetchStudentsPaginated(params);

    dispatch(action).finally(() => {
      setIsLoading(false);
    });
  }, [dispatch, type, pagination.skip, pagination.limit]);

  // Handle search
  const handleSearch = useCallback((search) => {
    setSearchTerm(search);
    loadData(search, true); // Reset pagination for new search
  }, [loadData]);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (pagination.hasMore && !loading) {
      loadData(searchTerm, false);
    }
  }, [loadData, searchTerm, pagination.hasMore, loading]);

  // Load initial data on mount and cleanup on unmount
  useEffect(() => {
    if (transformedData.length === 0) {
      loadData('', true);
    }

    // Cleanup function
    return () => {
      dispatch(clearPaginatedData());
    };
  }, [type]); // Only depend on type to avoid infinite loops

  return {
    options: transformedData,
    loading,
    hasMore: pagination.hasMore,
    searchTerm,
    onSearch: handleSearch,
    onLoadMore: handleLoadMore,
    pagination
  };
};

/**
 * Custom hook for managing assignment state in EditTask
 * @param {Array} initialClassrooms - Initially assigned classrooms
 * @param {Array} initialStudents - Initially assigned students
 * @param {Object} originalTask - Original task data for tracking changes
 */
export const useAssignmentState = (initialClassrooms = [], initialStudents = [], originalTask = null) => {
  const [selectedClassrooms, setSelectedClassrooms] = useState([]);
  const [selectedStudents, setSelectedStudents] = useState([]);
  const [formData, setFormData] = useState({
    add_classroom_ids: [],
    remove_classroom_ids: [],
    add_student_ids: [],
    remove_student_ids: []
  });

  // Initialize state when data loads
  useEffect(() => {
    if (initialClassrooms.length > 0 || initialStudents.length > 0) {
      // Handle the classroom format from task details API: { task_id, classroom_id }
      const classroomIds = initialClassrooms.map(c => c.classroom_id || c.id);
      // Handle the student format from task details API: { task_id, student_id }
      const studentIds = initialStudents.map(s => s.student_id || s.id);



      setSelectedClassrooms(classroomIds);
      setSelectedStudents(studentIds);
    }
  }, [initialClassrooms, initialStudents]);

  // Handle classroom selection changes
  const handleClassroomChange = useCallback((newSelectedIds) => {
    setSelectedClassrooms(newSelectedIds);

    // If classrooms are selected, clear individual student selections
    if (newSelectedIds.length > 0) {
      setSelectedStudents([]);
    }

    if (originalTask) {
      // Get original classroom IDs from task.classrooms array
      const originalIds = (originalTask.classrooms || []).map(c => c.classroom_id || c.id);
      const toAdd = newSelectedIds.filter(id => !originalIds.includes(id));
      const toRemove = originalIds.filter(id => !newSelectedIds.includes(id));

      // Get original student IDs to remove them when classrooms are selected
      const originalStudentIds = (originalTask.students || []).map(s => s.student_id || s.id);

      setFormData(prev => ({
        ...prev,
        add_classroom_ids: toAdd,
        remove_classroom_ids: toRemove,
        // Clear student assignments when classrooms are selected
        add_student_ids: [],
        remove_student_ids: newSelectedIds.length > 0 ? originalStudentIds : []
      }));
    }
  }, [originalTask]);

  // Handle student selection changes
  const handleStudentChange = useCallback((newSelectedIds) => {
    setSelectedStudents(newSelectedIds);

    // If students are selected, clear classroom selections
    if (newSelectedIds.length > 0) {
      setSelectedClassrooms([]);
    }

    if (originalTask) {
      // Get original student IDs from task.students array
      const originalIds = (originalTask.students || []).map(s => s.student_id || s.id);
      const toAdd = newSelectedIds.filter(id => !originalIds.includes(id));
      const toRemove = originalIds.filter(id => !newSelectedIds.includes(id));

      // Get original classroom IDs to remove them when students are selected
      const originalClassroomIds = (originalTask.classrooms || []).map(c => c.classroom_id || c.id);

      setFormData(prev => ({
        ...prev,
        add_student_ids: toAdd,
        remove_student_ids: toRemove,
        // Clear classroom assignments when students are selected
        add_classroom_ids: [],
        remove_classroom_ids: newSelectedIds.length > 0 ? originalClassroomIds : []
      }));
    }
  }, [originalTask]);

  return {
    selectedClassrooms,
    selectedStudents,
    formData,
    handleClassroomChange,
    handleStudentChange,
    setFormData
  };
};
