import { useSelector, useDispatch } from 'react-redux';
import { useEffect, useCallback } from 'react';
import {
  fetchPublicEvents,
  fetchFeaturedEvents,
  fetchEventDetails,
  searchEvents,
  createEvent,
  updateEvent,
  registerForEvent,
  cancelRegistration,
  fetchMyEvents,
  fetchMyRegistrations,
  updateFilters,
  resetFilters,
  selectPublicEvents,
  selectPublicEventsLoading,
  selectPublicEventsError,
  selectFeaturedEvents,
  selectFeaturedEventsLoading,
  selectCurrentEvent,
  selectEventDetailsLoading,
  selectSearchResults,
  selectSearchLoading,
  selectMyEvents,
  selectMyEventsLoading,
  selectMyRegistrations,
  selectMyRegistrationsLoading,
  selectFilters,
  selectCreateLoading,
  selectCreateSuccess,
  selectRegistrationLoading,
  selectRegistrationSuccess,
  selectIsRegisteredForEvent
} from '../store/slices/EventsSlice';

/**
 * Custom hook for events management
 * Provides easy access to events data and actions
 */
export const useEvents = (options = {}) => {
  const {
    autoFetchPublic = true,
    autoFetchFeatured = true,
    autoFetchMyEvents = false,
    autoFetchMyRegistrations = false,
    featuredLimit = 5
  } = options;

  const dispatch = useDispatch();
  
  // Events data
  const publicEvents = useSelector(selectPublicEvents);
  const publicEventsLoading = useSelector(selectPublicEventsLoading);
  const publicEventsError = useSelector(selectPublicEventsError);
  
  const featuredEvents = useSelector(selectFeaturedEvents);
  const featuredEventsLoading = useSelector(selectFeaturedEventsLoading);
  
  const currentEvent = useSelector(selectCurrentEvent);
  const eventDetailsLoading = useSelector(selectEventDetailsLoading);
  
  const searchResults = useSelector(selectSearchResults);
  const searchLoading = useSelector(selectSearchLoading);
  
  const myEvents = useSelector(selectMyEvents);
  const myEventsLoading = useSelector(selectMyEventsLoading);
  
  const myRegistrations = useSelector(selectMyRegistrations);
  const myRegistrationsLoading = useSelector(selectMyRegistrationsLoading);
  
  const filters = useSelector(selectFilters);
  
  // Action states
  const createLoading = useSelector(selectCreateLoading);
  const createSuccess = useSelector(selectCreateSuccess);
  const registrationLoading = useSelector(selectRegistrationLoading);
  const registrationSuccess = useSelector(selectRegistrationSuccess);

  // Auto-fetch data on mount
  useEffect(() => {
    if (autoFetchPublic) {
      dispatch(fetchPublicEvents());
    }
    if (autoFetchFeatured) {
      dispatch(fetchFeaturedEvents({ limit: featuredLimit }));
    }
    if (autoFetchMyEvents) {
      dispatch(fetchMyEvents());
    }
    if (autoFetchMyRegistrations) {
      dispatch(fetchMyRegistrations());
    }
  }, [dispatch, autoFetchPublic, autoFetchFeatured, autoFetchMyEvents, autoFetchMyRegistrations, featuredLimit]);

  // Event actions
  const loadPublicEvents = useCallback((params = {}) => {
    dispatch(fetchPublicEvents(params));
  }, [dispatch]);

  const loadFeaturedEvents = useCallback((limit = 5) => {
    dispatch(fetchFeaturedEvents({ limit }));
  }, [dispatch]);

  const loadEventDetails = useCallback((eventId) => {
    dispatch(fetchEventDetails(eventId));
  }, [dispatch]);

  const searchForEvents = useCallback((query, limit = 20) => {
    dispatch(searchEvents({ q: query, limit }));
  }, [dispatch]);

  const createNewEvent = useCallback(async (eventData) => {
    try {
      const result = await dispatch(createEvent(eventData)).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  const updateExistingEvent = useCallback(async (eventId, eventData) => {
    try {
      const result = await dispatch(updateEvent({ eventId, eventData })).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  const registerForEventAction = useCallback(async (eventId, registrationData = {}) => {
    try {
      const result = await dispatch(registerForEvent({ eventId, registrationData })).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  const cancelEventRegistration = useCallback(async (eventId) => {
    try {
      const result = await dispatch(cancelRegistration(eventId)).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  const loadMyEvents = useCallback((params = {}) => {
    dispatch(fetchMyEvents(params));
  }, [dispatch]);

  const loadMyRegistrations = useCallback((params = {}) => {
    dispatch(fetchMyRegistrations(params));
  }, [dispatch]);

  // Filter actions
  const updateEventFilters = useCallback((newFilters) => {
    dispatch(updateFilters(newFilters));
  }, [dispatch]);

  const resetEventFilters = useCallback(() => {
    dispatch(resetFilters());
  }, [dispatch]);

  // Helper functions
  const isRegisteredForEvent = useCallback((eventId) => {
    return myRegistrations.some(registration => registration.event_id === eventId);
  }, [myRegistrations]);

  const getUpcomingEvents = useCallback(() => {
    const now = new Date();
    return publicEvents.filter(event => new Date(event.start_datetime) > now);
  }, [publicEvents]);

  const getEventsByCategory = useCallback((categoryId) => {
    return publicEvents.filter(event => event.category?.id === categoryId);
  }, [publicEvents]);

  const getCompetitionEvents = useCallback(() => {
    return publicEvents.filter(event => event.is_competition);
  }, [publicEvents]);

  const getMyUpcomingEvents = useCallback(() => {
    const now = new Date();
    return myRegistrations
      .filter(registration => new Date(registration.event.start_datetime) > now)
      .map(registration => registration.event);
  }, [myRegistrations]);

  // Event statistics
  const getEventStats = useCallback(() => {
    const now = new Date();
    const upcoming = publicEvents.filter(event => new Date(event.start_datetime) > now);
    const ongoing = publicEvents.filter(event => {
      const start = new Date(event.start_datetime);
      const end = new Date(event.end_datetime);
      return start <= now && end >= now;
    });
    const competitions = publicEvents.filter(event => event.is_competition);
    const featured = publicEvents.filter(event => event.is_featured);

    return {
      total: publicEvents.length,
      upcoming: upcoming.length,
      ongoing: ongoing.length,
      competitions: competitions.length,
      featured: featured.length,
      myRegistrations: myRegistrations.length
    };
  }, [publicEvents, myRegistrations]);

  // Check if user can register for event
  const canRegisterForEvent = useCallback((event) => {
    const now = new Date();
    const registrationEnd = new Date(event.registration_end);
    const isRegistrationOpen = now < registrationEnd;
    const isFull = event.current_attendees >= event.max_attendees;
    const isAlreadyRegistered = isRegisteredForEvent(event.id);

    return isRegistrationOpen && !isFull && !isAlreadyRegistered;
  }, [isRegisteredForEvent]);

  return {
    // Data
    publicEvents,
    featuredEvents,
    currentEvent,
    searchResults,
    myEvents,
    myRegistrations,
    filters,

    // Loading states
    publicEventsLoading,
    featuredEventsLoading,
    eventDetailsLoading,
    searchLoading,
    myEventsLoading,
    myRegistrationsLoading,
    createLoading,
    registrationLoading,

    // Success states
    createSuccess,
    registrationSuccess,

    // Errors
    publicEventsError,

    // Actions
    loadPublicEvents,
    loadFeaturedEvents,
    loadEventDetails,
    searchForEvents,
    createNewEvent,
    updateExistingEvent,
    registerForEventAction,
    cancelEventRegistration,
    loadMyEvents,
    loadMyRegistrations,
    updateEventFilters,
    resetEventFilters,

    // Helper functions
    isRegisteredForEvent,
    getUpcomingEvents,
    getEventsByCategory,
    getCompetitionEvents,
    getMyUpcomingEvents,
    getEventStats,
    canRegisterForEvent
  };
};

export default useEvents;
