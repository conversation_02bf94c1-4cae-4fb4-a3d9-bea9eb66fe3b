import { useState, useCallback, useEffect, useRef } from 'react';

// Validation rules
export const validationRules = {
  required: (value, message = 'This field is required') => {
    if (value === null || value === undefined || value === '') {
      return message;
    }
    return null;
  },

  email: (value, message = 'Please enter a valid email address') => {
    if (!value) return null;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value) ? null : message;
  },

  minLength: (min, message) => (value) => {
    if (!value) return null;
    return value.length >= min ? null : message || `Must be at least ${min} characters`;
  },

  maxLength: (max, message) => (value) => {
    if (!value) return null;
    return value.length <= max ? null : message || `Must be no more than ${max} characters`;
  },

  min: (min, message) => (value) => {
    if (!value && value !== 0) return null;
    const num = Number(value);
    return num >= min ? null : message || `Must be at least ${min}`;
  },

  max: (max, message) => (value) => {
    if (!value && value !== 0) return null;
    const num = Number(value);
    return num <= max ? null : message || `Must be no more than ${max}`;
  },

  pattern: (regex, message = 'Invalid format') => (value) => {
    if (!value) return null;
    return regex.test(value) ? null : message;
  },

  custom: (validator, message) => (value) => {
    try {
      const isValid = validator(value);
      return isValid ? null : message;
    } catch (error) {
      return message;
    }
  },

  match: (otherField, message) => (value, formData) => {
    if (!value) return null;
    return value === formData[otherField] ? null : message || `Must match ${otherField}`;
  },

  url: (value, message = 'Please enter a valid URL') => {
    if (!value) return null;
    try {
      new URL(value);
      return null;
    } catch {
      return message;
    }
  },

  phone: (value, message = 'Please enter a valid phone number') => {
    if (!value) return null;
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(value.replace(/\s/g, '')) ? null : message;
  }
};

// Form validation hook
export const useFormValidation = (initialValues = {}, validationSchema = {}) => {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [isValid, setIsValid] = useState(true);
  
  // Auto-save functionality
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const autoSaveTimeoutRef = useRef(null);
  const onAutoSaveRef = useRef(null);

  // Validate a single field
  const validateField = useCallback((name, value, allValues = values) => {
    const fieldRules = validationSchema[name];
    if (!fieldRules) return null;

    for (const rule of fieldRules) {
      const error = typeof rule === 'function' ? rule(value, allValues) : rule;
      if (error) return error;
    }
    return null;
  }, [validationSchema, values]);

  // Validate all fields
  const validateForm = useCallback((formValues = values) => {
    const newErrors = {};
    let formIsValid = true;

    Object.keys(validationSchema).forEach(fieldName => {
      const error = validateField(fieldName, formValues[fieldName], formValues);
      if (error) {
        newErrors[fieldName] = error;
        formIsValid = false;
      }
    });

    setErrors(newErrors);
    setIsValid(formIsValid);
    return formIsValid;
  }, [validationSchema, validateField, values]);

  // Handle field change
  const handleChange = useCallback((name, value) => {
    setValues(prev => {
      const newValues = { ...prev, [name]: value };
      
      // Real-time validation for touched fields
      if (touched[name]) {
        const error = validateField(name, value, newValues);
        setErrors(prev => ({
          ...prev,
          [name]: error
        }));
      }

      return newValues;
    });

    setIsDirty(true);

    // Auto-save logic
    if (autoSaveEnabled && onAutoSaveRef.current) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
      
      autoSaveTimeoutRef.current = setTimeout(() => {
        onAutoSaveRef.current(values);
        setLastSaved(new Date());
      }, 2000); // Auto-save after 2 seconds of inactivity
    }
  }, [touched, validateField, autoSaveEnabled, values]);

  // Handle field blur
  const handleBlur = useCallback((name) => {
    setTouched(prev => ({ ...prev, [name]: true }));
    
    // Validate field on blur
    const error = validateField(name, values[name]);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  }, [validateField, values]);

  // Handle form submission
  const handleSubmit = useCallback(async (onSubmit) => {
    setIsSubmitting(true);
    
    // Mark all fields as touched
    const allTouched = Object.keys(validationSchema).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {});
    setTouched(allTouched);

    // Validate form
    const isFormValid = validateForm();
    
    if (isFormValid) {
      try {
        await onSubmit(values);
        setIsDirty(false);
      } catch (error) {
        console.error('Form submission error:', error);
        throw error;
      }
    }
    
    setIsSubmitting(false);
    return isFormValid;
  }, [validationSchema, validateForm, values]);

  // Reset form
  const reset = useCallback((newValues = initialValues) => {
    setValues(newValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
    setIsDirty(false);
    setIsValid(true);
  }, [initialValues]);

  // Set field value programmatically
  const setValue = useCallback((name, value) => {
    setValues(prev => ({ ...prev, [name]: value }));
    setIsDirty(true);
  }, []);

  // Set field error programmatically
  const setFieldError = useCallback((name, error) => {
    setErrors(prev => ({ ...prev, [name]: error }));
  }, []);

  // Clear field error
  const clearFieldError = useCallback((name) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[name];
      return newErrors;
    });
  }, []);

  // Auto-save configuration
  const enableAutoSave = useCallback((onAutoSave, interval = 2000) => {
    onAutoSaveRef.current = onAutoSave;
    setAutoSaveEnabled(true);
  }, []);

  const disableAutoSave = useCallback(() => {
    setAutoSaveEnabled(false);
    onAutoSaveRef.current = null;
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }
  }, []);

  // Get field props for easy integration
  const getFieldProps = useCallback((name) => ({
    name,
    value: values[name] || '',
    onChange: (e) => {
      const value = e.target ? e.target.value : e;
      handleChange(name, value);
    },
    onBlur: () => handleBlur(name),
    error: touched[name] ? errors[name] : null,
    hasError: touched[name] && !!errors[name]
  }), [values, errors, touched, handleChange, handleBlur]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  // Validate form when values change
  useEffect(() => {
    if (Object.keys(touched).length > 0) {
      validateForm();
    }
  }, [values, validateForm, touched]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    isDirty,
    isValid,
    lastSaved,
    handleChange,
    handleBlur,
    handleSubmit,
    reset,
    setValue,
    setFieldError,
    clearFieldError,
    getFieldProps,
    validateField,
    validateForm,
    enableAutoSave,
    disableAutoSave
  };
};

// Validation schema builder
export const createValidationSchema = (schema) => {
  const processedSchema = {};
  
  Object.keys(schema).forEach(fieldName => {
    const rules = schema[fieldName];
    processedSchema[fieldName] = Array.isArray(rules) ? rules : [rules];
  });
  
  return processedSchema;
};

export default useFormValidation;
