import { useState, useEffect, useCallback } from 'react';

/**
 * Custom hook for managing classroom join requests
 * Handles fetching, approving, rejecting, and bulk operations
 */
export const useJoinRequests = (options = {}) => {
  const {
    autoFetch = true,
    refreshInterval = null,
    onError = null,
    onSuccess = null
  } = options;

  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [processingIds, setProcessingIds] = useState(new Set());

  // API endpoints
  const API_BASE = 'https://edufair.duckdns.org/api';
  const ENDPOINTS = {
    getAllRequests: `${API_BASE}/classrooms/all/requests/for/student`,
    approveRequest: (requestId) => `${API_BASE}/classrooms/requests/${requestId}/approve`,
    rejectRequest: (requestId) => `${API_BASE}/classrooms/requests/${requestId}/reject`,
    getClassroomDetails: (classroomId) => `${API_BASE}/classrooms/${classroomId}`
  };

  // Fetch all join requests
  const fetchRequests = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(ENDPOINTS.getAllRequests, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers as needed
          // 'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch requests: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      // Enhance requests with classroom details if needed
      const enhancedRequests = await Promise.all(
        data.map(async (request) => {
          // If classroom is just an ID, fetch classroom details
          if (typeof request.classroom === 'string') {
            try {
              const classroomResponse = await fetch(ENDPOINTS.getClassroomDetails(request.classroom));
              if (classroomResponse.ok) {
                const classroomData = await classroomResponse.json();
                return {
                  ...request,
                  classroom_details: classroomData
                };
              }
            } catch (err) {
              console.warn('Failed to fetch classroom details:', err);
            }
          }
          return request;
        })
      );

      setRequests(enhancedRequests);
      onSuccess?.(enhancedRequests);
    } catch (err) {
      console.error('Error fetching join requests:', err);
      setError(err.message);
      onError?.(err);
    } finally {
      setLoading(false);
    }
  }, [onError, onSuccess]);

  // Approve a join request
  const approveRequest = useCallback(async (requestId) => {
    setProcessingIds(prev => new Set(prev).add(requestId));
    
    try {
      const response = await fetch(ENDPOINTS.approveRequest(requestId), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers as needed
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to approve request: ${response.status} ${response.statusText}`);
      }

      // Update local state
      setRequests(prev => 
        prev.map(request => 
          request.id === requestId 
            ? { ...request, status: 'approved', processed_at: new Date().toISOString() }
            : request
        )
      );

      onSuccess?.({ action: 'approve', requestId });
      return true;
    } catch (err) {
      console.error('Error approving request:', err);
      setError(err.message);
      onError?.(err);
      return false;
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(requestId);
        return newSet;
      });
    }
  }, [onError, onSuccess]);

  // Reject a join request
  const rejectRequest = useCallback(async (requestId) => {
    setProcessingIds(prev => new Set(prev).add(requestId));
    
    try {
      const response = await fetch(ENDPOINTS.rejectRequest(requestId), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers as needed
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to reject request: ${response.status} ${response.statusText}`);
      }

      // Update local state
      setRequests(prev => 
        prev.map(request => 
          request.id === requestId 
            ? { ...request, status: 'rejected', processed_at: new Date().toISOString() }
            : request
        )
      );

      onSuccess?.({ action: 'reject', requestId });
      return true;
    } catch (err) {
      console.error('Error rejecting request:', err);
      setError(err.message);
      onError?.(err);
      return false;
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(requestId);
        return newSet;
      });
    }
  }, [onError, onSuccess]);

  // Bulk approve requests
  const bulkApprove = useCallback(async (requestIds) => {
    const results = await Promise.allSettled(
      requestIds.map(id => approveRequest(id))
    );
    
    const successful = results.filter(result => result.status === 'fulfilled' && result.value).length;
    const failed = results.length - successful;
    
    return { successful, failed, total: results.length };
  }, [approveRequest]);

  // Bulk reject requests
  const bulkReject = useCallback(async (requestIds) => {
    const results = await Promise.allSettled(
      requestIds.map(id => rejectRequest(id))
    );
    
    const successful = results.filter(result => result.status === 'fulfilled' && result.value).length;
    const failed = results.length - successful;
    
    return { successful, failed, total: results.length };
  }, [rejectRequest]);

  // Handle bulk actions
  const handleBulkAction = useCallback(async (action, selectedRequests) => {
    const requestIds = selectedRequests.map(request => request.id);
    
    switch (action) {
      case 'approve':
        return await bulkApprove(requestIds);
      case 'reject':
        return await bulkReject(requestIds);
      default:
        throw new Error(`Unknown bulk action: ${action}`);
    }
  }, [bulkApprove, bulkReject]);

  // Filter requests by status
  const getRequestsByStatus = useCallback((status) => {
    return requests.filter(request => (request.status || 'pending') === status);
  }, [requests]);

  // Get pending requests count
  const pendingCount = requests.filter(request => !request.status || request.status === 'pending').length;

  // Auto-fetch on mount
  useEffect(() => {
    if (autoFetch) {
      fetchRequests();
    }
  }, [autoFetch, fetchRequests]);

  // Set up refresh interval
  useEffect(() => {
    if (refreshInterval && refreshInterval > 0) {
      const interval = setInterval(fetchRequests, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshInterval, fetchRequests]);

  // Clear error when requests change
  useEffect(() => {
    if (error && requests.length > 0) {
      setError(null);
    }
  }, [requests, error]);

  return {
    // Data
    requests,
    pendingCount,
    
    // State
    loading,
    error,
    processingIds,
    
    // Actions
    fetchRequests,
    approveRequest,
    rejectRequest,
    handleBulkAction,
    
    // Utilities
    getRequestsByStatus,
    
    // Computed values
    pendingRequests: getRequestsByStatus('pending'),
    approvedRequests: getRequestsByStatus('approved'),
    rejectedRequests: getRequestsByStatus('rejected'),
    
    // Helper functions
    isProcessing: (requestId) => processingIds.has(requestId),
    refresh: fetchRequests,
    clearError: () => setError(null)
  };
};

export default useJoinRequests;
