import { useState, useCallback, useRef, useEffect } from 'react';

// Custom hook for managing loading states
export const useLoadingState = (initialState = false) => {
  const [isLoading, setIsLoading] = useState(initialState);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);
  const abortControllerRef = useRef(null);

  // Start loading
  const startLoading = useCallback(() => {
    setIsLoading(true);
    setError(null);
  }, []);

  // Stop loading
  const stopLoading = useCallback(() => {
    setIsLoading(false);
  }, []);

  // Set error and stop loading
  const setLoadingError = useCallback((error) => {
    setError(error);
    setIsLoading(false);
  }, []);

  // Set data and stop loading
  const setLoadingData = useCallback((data) => {
    setData(data);
    setError(null);
    setIsLoading(false);
  }, []);

  // Reset all states
  const reset = useCallback(() => {
    setIsLoading(false);
    setError(null);
    setData(null);
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // Execute async function with loading state management
  const execute = useCallback(async (asyncFunction, options = {}) => {
    const { 
      onSuccess, 
      onError, 
      retryCount = 0, 
      retryDelay = 1000,
      abortable = false 
    } = options;

    // Create abort controller if needed
    if (abortable) {
      abortControllerRef.current = new AbortController();
    }

    startLoading();

    let attempt = 0;
    while (attempt <= retryCount) {
      try {
        const signal = abortable ? abortControllerRef.current?.signal : undefined;
        const result = await asyncFunction(signal);
        
        setLoadingData(result);
        if (onSuccess) onSuccess(result);
        return result;
      } catch (err) {
        attempt++;
        
        // If it's an abort error, don't retry
        if (err.name === 'AbortError') {
          setLoadingError(err);
          return;
        }

        // If we've exhausted retries, set error
        if (attempt > retryCount) {
          setLoadingError(err);
          if (onError) onError(err);
          throw err;
        }

        // Wait before retrying
        if (attempt <= retryCount) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        }
      }
    }
  }, [startLoading, setLoadingData, setLoadingError]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    isLoading,
    error,
    data,
    startLoading,
    stopLoading,
    setError: setLoadingError,
    setData: setLoadingData,
    reset,
    execute
  };
};

// Hook for managing multiple loading states
export const useMultipleLoadingStates = (keys = []) => {
  const [loadingStates, setLoadingStates] = useState(
    keys.reduce((acc, key) => ({ ...acc, [key]: false }), {})
  );
  const [errors, setErrors] = useState(
    keys.reduce((acc, key) => ({ ...acc, [key]: null }), {})
  );

  const setLoading = useCallback((key, isLoading) => {
    setLoadingStates(prev => ({ ...prev, [key]: isLoading }));
    if (isLoading) {
      setErrors(prev => ({ ...prev, [key]: null }));
    }
  }, []);

  const setError = useCallback((key, error) => {
    setErrors(prev => ({ ...prev, [key]: error }));
    setLoadingStates(prev => ({ ...prev, [key]: false }));
  }, []);

  const isAnyLoading = Object.values(loadingStates).some(Boolean);
  const hasAnyError = Object.values(errors).some(Boolean);

  return {
    loadingStates,
    errors,
    setLoading,
    setError,
    isAnyLoading,
    hasAnyError
  };
};

// Hook for debounced loading (useful for search)
export const useDebouncedLoading = (delay = 300) => {
  const [isLoading, setIsLoading] = useState(false);
  const [debouncedLoading, setDebouncedLoading] = useState(false);
  const timeoutRef = useRef(null);

  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (isLoading) {
      setDebouncedLoading(true);
    } else {
      timeoutRef.current = setTimeout(() => {
        setDebouncedLoading(false);
      }, delay);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isLoading, delay]);

  return {
    isLoading,
    debouncedLoading,
    setLoading: setIsLoading
  };
};

// Hook for retry functionality
export const useRetry = (maxRetries = 3, initialDelay = 1000) => {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  const retry = useCallback(async (asyncFunction) => {
    if (retryCount >= maxRetries) {
      throw new Error(`Max retries (${maxRetries}) exceeded`);
    }

    setIsRetrying(true);
    setRetryCount(prev => prev + 1);

    try {
      // Exponential backoff delay
      const delay = initialDelay * Math.pow(2, retryCount);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      const result = await asyncFunction();
      setRetryCount(0); // Reset on success
      return result;
    } finally {
      setIsRetrying(false);
    }
  }, [retryCount, maxRetries, initialDelay]);

  const reset = useCallback(() => {
    setRetryCount(0);
    setIsRetrying(false);
  }, []);

  const canRetry = retryCount < maxRetries;

  return {
    retry,
    reset,
    retryCount,
    isRetrying,
    canRetry,
    maxRetries
  };
};

// Hook for progressive loading (pagination, infinite scroll)
export const useProgressiveLoading = (loadFunction, pageSize = 20) => {
  const [items, setItems] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);

  const loadMore = useCallback(async () => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);
    setError(null);

    try {
      const newItems = await loadFunction(page, pageSize);
      
      if (newItems.length < pageSize) {
        setHasMore(false);
      }

      setItems(prev => [...prev, ...newItems]);
      setPage(prev => prev + 1);
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }, [loadFunction, page, pageSize, isLoading, hasMore]);

  const reset = useCallback(() => {
    setItems([]);
    setHasMore(true);
    setIsLoading(false);
    setError(null);
    setPage(1);
  }, []);

  const refresh = useCallback(async () => {
    reset();
    await loadMore();
  }, [reset, loadMore]);

  return {
    items,
    hasMore,
    isLoading,
    error,
    loadMore,
    reset,
    refresh
  };
};

export default useLoadingState;
