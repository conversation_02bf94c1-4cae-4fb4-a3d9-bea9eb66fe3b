import { useSelector, useDispatch } from 'react-redux';
import { useEffect, useCallback } from 'react';
import {
  fetchPublicMentors,
  fetchMentorDetails,
  updateMentorProfile,
  registerAsMentor,
  applyToInstitute,
  inviteMentor,
  respondToAssociation,
  fetchMentorAssignments,
  respondToAssignment,
  fetchAnswersToCheck,
  submitAnswerScore,
  bulkScoreAnswers,
  fetchMentorStatistics,
  updateSearchFilters,
  resetSearchFilters,
  selectPublicMentors,
  selectPublicMentorsLoading,
  selectPublicMentorsError,
  selectCurrentMentor,
  selectMentorDetailsLoading,
  selectMyProfile,
  selectProfileUpdateLoading,
  selectProfileUpdateSuccess,
  selectRegistrationLoading,
  selectRegistrationSuccess,
  selectAssignments,
  selectAssignmentsLoading,
  selectAnswersToCheck,
  selectAnswersLoading,
  selectStatistics,
  selectStatisticsLoading,
  selectSearchFilters,
  selectMentorsByExpertise,
  selectVerifiedMentors,
  selectTopRatedMentors
} from '../store/slices/MentorsSlice';

/**
 * Custom hook for mentors management
 * Provides easy access to mentors data and actions
 */
export const useMentors = (options = {}) => {
  const {
    autoFetchPublic = true,
    autoFetchAssignments = false,
    autoFetchAnswers = false,
    autoFetchStatistics = false
  } = options;

  const dispatch = useDispatch();
  
  // Mentors data
  const publicMentors = useSelector(selectPublicMentors);
  const publicMentorsLoading = useSelector(selectPublicMentorsLoading);
  const publicMentorsError = useSelector(selectPublicMentorsError);
  
  const currentMentor = useSelector(selectCurrentMentor);
  const mentorDetailsLoading = useSelector(selectMentorDetailsLoading);
  
  const myProfile = useSelector(selectMyProfile);
  const profileUpdateLoading = useSelector(selectProfileUpdateLoading);
  const profileUpdateSuccess = useSelector(selectProfileUpdateSuccess);
  
  const registrationLoading = useSelector(selectRegistrationLoading);
  const registrationSuccess = useSelector(selectRegistrationSuccess);
  
  const assignments = useSelector(selectAssignments);
  const assignmentsLoading = useSelector(selectAssignmentsLoading);
  
  const answersToCheck = useSelector(selectAnswersToCheck);
  const answersLoading = useSelector(selectAnswersLoading);
  
  const statistics = useSelector(selectStatistics);
  const statisticsLoading = useSelector(selectStatisticsLoading);
  
  const searchFilters = useSelector(selectSearchFilters);

  // Auto-fetch data on mount
  useEffect(() => {
    if (autoFetchPublic) {
      dispatch(fetchPublicMentors());
    }
    if (autoFetchAssignments) {
      dispatch(fetchMentorAssignments());
    }
    if (autoFetchAnswers) {
      dispatch(fetchAnswersToCheck());
    }
    if (autoFetchStatistics) {
      dispatch(fetchMentorStatistics());
    }
  }, [dispatch, autoFetchPublic, autoFetchAssignments, autoFetchAnswers, autoFetchStatistics]);

  // Mentor actions
  const loadPublicMentors = useCallback((params = {}) => {
    dispatch(fetchPublicMentors(params));
  }, [dispatch]);

  const loadMentorDetails = useCallback((mentorId) => {
    dispatch(fetchMentorDetails(mentorId));
  }, [dispatch]);

  const updateProfile = useCallback(async (profileData) => {
    try {
      const result = await dispatch(updateMentorProfile(profileData)).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  const registerMentor = useCallback(async (mentorData) => {
    try {
      const result = await dispatch(registerAsMentor(mentorData)).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  const applyToInstituteAction = useCallback(async (applicationData) => {
    try {
      const result = await dispatch(applyToInstitute(applicationData)).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  const inviteMentorAction = useCallback(async (invitationData) => {
    try {
      const result = await dispatch(inviteMentor(invitationData)).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  const respondToAssociationAction = useCallback(async (associationId, responseData) => {
    try {
      const result = await dispatch(respondToAssociation({ associationId, responseData })).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  // Assignment actions
  const loadAssignments = useCallback((params = {}) => {
    dispatch(fetchMentorAssignments(params));
  }, [dispatch]);

  const respondToAssignmentAction = useCallback(async (assignmentId, responseData) => {
    try {
      const result = await dispatch(respondToAssignment({ assignmentId, responseData })).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  // Answer checking actions
  const loadAnswersToCheck = useCallback((params = {}) => {
    dispatch(fetchAnswersToCheck(params));
  }, [dispatch]);

  const submitScore = useCallback(async (answerId, mentorScore, mentorFeedback) => {
    try {
      const result = await dispatch(submitAnswerScore({ 
        answerId, 
        mentor_score: mentorScore, 
        mentor_feedback: mentorFeedback 
      })).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  const bulkSubmitScores = useCallback(async (scoresData) => {
    try {
      const result = await dispatch(bulkScoreAnswers(scoresData)).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  // Statistics actions
  const loadStatistics = useCallback((params = {}) => {
    dispatch(fetchMentorStatistics(params));
  }, [dispatch]);

  // Filter actions
  const updateFilters = useCallback((newFilters) => {
    dispatch(updateSearchFilters(newFilters));
  }, [dispatch]);

  const resetFilters = useCallback(() => {
    dispatch(resetSearchFilters());
  }, [dispatch]);

  // Helper functions with selectors
  const getMentorsByExpertise = useCallback((expertiseArea) => {
    return useSelector(selectMentorsByExpertise(expertiseArea));
  }, []);

  const getVerifiedMentors = useCallback(() => {
    return useSelector(selectVerifiedMentors);
  }, []);

  const getTopRatedMentors = useCallback(() => {
    return useSelector(selectTopRatedMentors);
  }, []);

  // Local helper functions
  const getMentorsByRating = useCallback((minRating = 4.0) => {
    return publicMentors.filter(mentor => mentor.rating >= minRating);
  }, [publicMentors]);

  const getMentorsByExperienceLevel = useCallback((minYears = 5) => {
    return publicMentors.filter(mentor => mentor.experience_years >= minYears);
  }, [publicMentors]);

  const getAvailableMentors = useCallback(() => {
    return publicMentors.filter(mentor => mentor.is_available);
  }, [publicMentors]);

  const getMentorsByPriceRange = useCallback((minRate = 0, maxRate = 1000) => {
    return publicMentors.filter(mentor => 
      mentor.hourly_rate >= minRate && mentor.hourly_rate <= maxRate
    );
  }, [publicMentors]);

  // Assignment statistics
  const getAssignmentStats = useCallback(() => {
    const pending = assignments.filter(a => a.status === 'pending').length;
    const accepted = assignments.filter(a => a.status === 'accepted').length;
    const completed = assignments.filter(a => a.status === 'completed').length;
    const rejected = assignments.filter(a => a.status === 'rejected').length;
    
    const totalEarnings = assignments
      .filter(a => a.status === 'completed')
      .reduce((sum, a) => sum + (a.hourly_rate * a.estimated_hours || 0), 0);

    return {
      total: assignments.length,
      pending,
      accepted,
      completed,
      rejected,
      totalEarnings
    };
  }, [assignments]);

  // Answer checking statistics
  const getAnswerStats = useCallback(() => {
    const total = answersToCheck.length;
    const checked = answersToCheck.filter(a => a.mentor_score !== null).length;
    const unchecked = total - checked;
    
    const averageScore = checked > 0 
      ? answersToCheck
          .filter(a => a.mentor_score !== null)
          .reduce((sum, a) => sum + a.mentor_score, 0) / checked
      : 0;

    return {
      total,
      checked,
      unchecked,
      averageScore: Math.round(averageScore * 100) / 100
    };
  }, [answersToCheck]);

  return {
    // Data
    publicMentors,
    currentMentor,
    myProfile,
    assignments,
    answersToCheck,
    statistics,
    searchFilters,

    // Loading states
    publicMentorsLoading,
    mentorDetailsLoading,
    profileUpdateLoading,
    registrationLoading,
    assignmentsLoading,
    answersLoading,
    statisticsLoading,

    // Success states
    profileUpdateSuccess,
    registrationSuccess,

    // Errors
    publicMentorsError,

    // Actions
    loadPublicMentors,
    loadMentorDetails,
    updateProfile,
    registerMentor,
    applyToInstituteAction,
    inviteMentorAction,
    respondToAssociationAction,
    loadAssignments,
    respondToAssignmentAction,
    loadAnswersToCheck,
    submitScore,
    bulkSubmitScores,
    loadStatistics,
    updateFilters,
    resetFilters,

    // Helper functions
    getMentorsByExpertise,
    getVerifiedMentors,
    getTopRatedMentors,
    getMentorsByRating,
    getMentorsByExperienceLevel,
    getAvailableMentors,
    getMentorsByPriceRange,
    getAssignmentStats,
    getAnswerStats
  };
};

export default useMentors;
