import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchStudentDashboard,
  fetchStudentDashboardSummary,
  fetchStudentQuickActions,
  fetchStudentPerformance,
  fetchStudentSchedule,
  clearErrors,
  clearDashboardData
} from '../store/slices/StudentDashboardSlice';

/**
 * Custom hook for managing student dashboard data
 * 
 * @param {Object} options - Configuration options
 * @param {boolean} options.autoFetch - Whether to automatically fetch data on mount (default: true)
 * @param {boolean} options.fetchAll - Whether to fetch all dashboard data at once (default: false)
 * @param {Array} options.sections - Specific sections to fetch ['summary', 'quickActions', 'performance', 'schedule']
 * @returns {Object} Dashboard data and utility functions
 */
export const useStudentDashboard = (options = {}) => {
  const {
    autoFetch = true,
    fetchAll = false,
    sections = ['summary', 'quickActions', 'performance', 'schedule']
  } = options;

  const dispatch = useDispatch();
  
  // Select all student dashboard data from Redux store
  const {
    dashboardData,
    summary,
    quickActions,
    performance,
    studyMetrics,
    schedule,
    student,
    classes,
    exams,
    assignments,
    recentActivity,
    loading,
    error,
    lastUpdated
  } = useSelector((state) => state.studentDashboard);

  // Utility functions
  const fetchDashboardData = () => {
    if (fetchAll) {
      dispatch(fetchStudentDashboard());
    } else {
      if (sections.includes('summary')) {
        dispatch(fetchStudentDashboardSummary());
      }
      if (sections.includes('quickActions')) {
        dispatch(fetchStudentQuickActions());
      }
      if (sections.includes('performance')) {
        dispatch(fetchStudentPerformance());
      }
      if (sections.includes('schedule')) {
        dispatch(fetchStudentSchedule());
      }
    }
  };

  const refreshData = () => {
    fetchDashboardData();
  };

  const clearAllErrors = () => {
    dispatch(clearErrors());
  };

  const clearData = () => {
    dispatch(clearDashboardData());
  };

  // Auto-fetch data on mount if enabled
  useEffect(() => {
    if (autoFetch) {
      fetchDashboardData();
    }
  }, [autoFetch, fetchAll, sections.join(',')]); // eslint-disable-line react-hooks/exhaustive-deps

  // Computed values
  const isLoading = Object.values(loading).some(Boolean);
  const hasErrors = Object.values(error).some(Boolean);
  const hasData = Boolean(
    dashboardData || 
    summary.total_classes > 0 || 
    quickActions.length > 0 || 
    assignments.length > 0
  );

  // Get the most recent update time
  const mostRecentUpdate = Object.values(lastUpdated)
    .filter(Boolean)
    .sort((a, b) => new Date(b) - new Date(a))[0];

  return {
    // Data
    dashboardData,
    summary,
    quickActions,
    performance,
    studyMetrics,
    schedule,
    student,
    classes,
    exams,
    assignments,
    recentActivity,
    
    // Status
    loading,
    error,
    isLoading,
    hasErrors,
    hasData,
    lastUpdated,
    mostRecentUpdate,
    
    // Actions
    fetchDashboardData,
    refreshData,
    clearAllErrors,
    clearData,
    
    // Individual fetch functions
    fetchSummary: () => dispatch(fetchStudentDashboardSummary()),
    fetchQuickActions: () => dispatch(fetchStudentQuickActions()),
    fetchPerformance: () => dispatch(fetchStudentPerformance()),
    fetchSchedule: () => dispatch(fetchStudentSchedule()),
    fetchAll: () => dispatch(fetchStudentDashboard())
  };
};

/**
 * Hook for getting just the summary data
 */
export const useStudentSummary = () => {
  const { summary, loading, error } = useStudentDashboard({
    sections: ['summary'],
    fetchAll: false
  });

  return {
    summary,
    isLoading: loading.summary,
    error: error.summary,
    refresh: () => useDispatch()(fetchStudentDashboardSummary())
  };
};

/**
 * Hook for getting just the performance data
 */
export const useStudentPerformance = () => {
  const { performance, studyMetrics, loading, error } = useStudentDashboard({
    sections: ['performance'],
    fetchAll: false
  });

  return {
    performance,
    studyMetrics,
    isLoading: loading.performance,
    error: error.performance,
    refresh: () => useDispatch()(fetchStudentPerformance())
  };
};

/**
 * Hook for getting just the quick actions
 */
export const useStudentQuickActions = () => {
  const { quickActions, loading, error } = useStudentDashboard({
    sections: ['quickActions'],
    fetchAll: false
  });

  return {
    quickActions,
    isLoading: loading.quickActions,
    error: error.quickActions,
    refresh: () => useDispatch()(fetchStudentQuickActions())
  };
};

export default useStudentDashboard;
