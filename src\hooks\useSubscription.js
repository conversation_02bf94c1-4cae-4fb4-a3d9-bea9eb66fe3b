import { useSelector, useDispatch } from 'react-redux';
import { useEffect, useCallback } from 'react';
import {
  selectMySubscription,
  selectUsage,
  selectHasActiveSubscription,
  selectCanUseFeature,
  selectIsOverLimit,
  fetchMySubscription,
  fetchMyUsage,
  updateUsageMetric
} from '../store/slices/SubscriptionSlice';

/**
 * Custom hook for subscription management and feature gating
 * Provides easy access to subscription status, usage limits, and feature availability
 */
export const useSubscription = () => {
  const dispatch = useDispatch();
  
  // Subscription data
  const subscription = useSelector(selectMySubscription);
  const usage = useSelector(selectUsage);
  const hasActiveSubscription = useSelector(selectHasActiveSubscription);
  
  // Load subscription data on mount
  useEffect(() => {
    if (!subscription) {
      dispatch(fetchMySubscription());
    }
    if (!usage) {
      dispatch(fetchMyUsage());
    }
  }, [dispatch, subscription, usage]);

  // Feature checking function
  const canUseFeature = useCallback((feature) => {
    if (!subscription || subscription.status !== 'active') return false;
    return subscription.plan?.features?.[feature] || false;
  }, [subscription]);

  // Usage limit checking
  const isOverLimit = useCallback((limitType) => {
    if (!usage || !subscription) return false;
    
    const currentUsage = usage.current_usage;
    const planLimits = usage.plan_limits;
    
    if (!currentUsage || !planLimits) return false;
    
    switch (limitType) {
      case 'classrooms':
        return currentUsage.classrooms_created >= planLimits.max_classrooms;
      case 'exams':
        return currentUsage.exams_created >= planLimits.max_exams_per_month;
      case 'students':
        return currentUsage.students_enrolled >= (planLimits.max_students_per_classroom * planLimits.max_classrooms);
      default:
        return false;
    }
  }, [usage, subscription]);

  // Usage percentage calculation
  const getUsagePercentage = useCallback((limitType) => {
    if (!usage) return 0;
    
    const currentUsage = usage.current_usage;
    const planLimits = usage.plan_limits;
    
    if (!currentUsage || !planLimits) return 0;
    
    switch (limitType) {
      case 'classrooms':
        return planLimits.max_classrooms > 0 
          ? (currentUsage.classrooms_created / planLimits.max_classrooms) * 100 
          : 0;
      case 'exams':
        return planLimits.max_exams_per_month > 0 
          ? (currentUsage.exams_created / planLimits.max_exams_per_month) * 100 
          : 0;
      case 'students':
        const maxStudents = planLimits.max_students_per_classroom * planLimits.max_classrooms;
        return maxStudents > 0 
          ? (currentUsage.students_enrolled / maxStudents) * 100 
          : 0;
      default:
        return 0;
    }
  }, [usage]);

  // Track usage function
  const trackUsage = useCallback(async (metricName, increment = 1) => {
    try {
      await dispatch(updateUsageMetric({ metric_name: metricName, increment })).unwrap();
      // Refresh usage data after update
      dispatch(fetchMyUsage());
    } catch (error) {
      console.error('Failed to track usage:', error);
    }
  }, [dispatch]);

  // Get plan info
  const getPlanInfo = useCallback(() => {
    if (!subscription) return null;
    
    return {
      name: subscription.plan?.name,
      type: subscription.plan?.plan_type,
      price: subscription.plan?.price,
      features: subscription.plan?.features,
      limits: {
        max_classrooms: subscription.plan?.max_classrooms,
        max_students_per_classroom: subscription.plan?.max_students_per_classroom,
        max_exams_per_month: subscription.plan?.max_exams_per_month,
        max_questions_per_exam: subscription.plan?.max_questions_per_exam
      }
    };
  }, [subscription]);

  // Check if upgrade is needed for feature
  const needsUpgradeFor = useCallback((feature) => {
    return !canUseFeature(feature);
  }, [canUseFeature]);

  // Get upgrade suggestions
  const getUpgradeSuggestions = useCallback(() => {
    if (!subscription) return [];
    
    const suggestions = [];
    const currentPlan = subscription.plan?.plan_type;
    
    if (currentPlan === 'basic') {
      suggestions.push({
        reason: 'Access AI Question Generation',
        targetPlan: 'premium',
        features: ['ai_question_generation', 'advanced_analytics', 'bulk_operations']
      });
      
      suggestions.push({
        reason: 'Enable Home Tutoring',
        targetPlan: 'home_tutor',
        features: ['home_tutoring', 'payment_processing', 'ai_question_generation']
      });
    } else if (currentPlan === 'premium') {
      suggestions.push({
        reason: 'Enable Home Tutoring',
        targetPlan: 'home_tutor',
        features: ['home_tutoring', 'payment_processing']
      });
    }
    
    return suggestions;
  }, [subscription]);

  return {
    // Subscription data
    subscription,
    usage,
    hasActiveSubscription,
    
    // Feature checking
    canUseFeature,
    needsUpgradeFor,
    
    // Usage limits
    isOverLimit,
    getUsagePercentage,
    
    // Usage tracking
    trackUsage,
    
    // Plan information
    getPlanInfo,
    getUpgradeSuggestions,
    
    // Convenience flags
    isPremium: subscription?.plan?.plan_type === 'premium',
    isHomeTutor: subscription?.plan?.plan_type === 'home_tutor',
    isBasic: subscription?.plan?.plan_type === 'basic',
    
    // Feature flags
    canCreateClassroom: canUseFeature('create_classroom'),
    canCreateExam: canUseFeature('create_exam'),
    canUseAI: canUseFeature('ai_question_generation'),
    canUseAnalytics: canUseFeature('advanced_analytics'),
    canUseHomeTutoring: canUseFeature('home_tutoring'),
    canUseBulkOperations: canUseFeature('bulk_operations'),
    hasPrioritySupport: canUseFeature('priority_support'),
    
    // Usage status
    classroomUsage: getUsagePercentage('classrooms'),
    examUsage: getUsagePercentage('exams'),
    studentUsage: getUsagePercentage('students'),
    
    // Limit status
    isClassroomLimitReached: isOverLimit('classrooms'),
    isExamLimitReached: isOverLimit('exams'),
    isStudentLimitReached: isOverLimit('students')
  };
};

/**
 * Hook for feature gating components
 * Returns whether a feature is available and provides upgrade info if not
 */
export const useFeatureGate = (feature) => {
  const { canUseFeature, needsUpgradeFor, getUpgradeSuggestions } = useSubscription();
  
  const isAvailable = canUseFeature(feature);
  const needsUpgrade = needsUpgradeFor(feature);
  const suggestions = getUpgradeSuggestions();
  
  return {
    isAvailable,
    needsUpgrade,
    suggestions: suggestions.filter(s => s.features.includes(feature))
  };
};

/**
 * Hook for usage tracking
 * Provides easy functions to track common usage metrics
 */
export const useUsageTracking = () => {
  const { trackUsage } = useSubscription();
  
  return {
    trackClassroomCreated: () => trackUsage('classrooms_created'),
    trackStudentEnrolled: () => trackUsage('students_enrolled'),
    trackExamCreated: () => trackUsage('exams_created'),
    trackQuestionGenerated: () => trackUsage('questions_generated'),
    trackAIQuestionUsed: () => trackUsage('ai_questions_used'),
    trackAnalyticsView: () => trackUsage('analytics_views'),
    trackCompetitionCreated: () => trackUsage('competitions_created'),
    trackCustomUsage: trackUsage
  };
};

export default useSubscription;
