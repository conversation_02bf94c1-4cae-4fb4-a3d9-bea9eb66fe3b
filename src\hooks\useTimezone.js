/**
 * Custom hook for timezone detection and management
 * Automatically detects user's timezone via IP geolocation
 */

import { useState, useEffect } from 'react';
import { detectUserTimezone, formatDateTimeSync } from '../utils/timezone';

export function useTimezone() {
  const [timezoneData, setTimezoneData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const initializeTimezone = async () => {
      try {
        setLoading(true);
        const data = await detectUserTimezone();
        setTimezoneData(data);
        setError(null);
      } catch (err) {
        console.error('Failed to detect timezone:', err);
        setError(err);
        // Set fallback data
        setTimezoneData({
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          country: 'Unknown',
          city: 'Unknown',
          source: 'browser-fallback',
          detected: false
        });
      } finally {
        setLoading(false);
      }
    };

    initializeTimezone();
  }, []);

  // Helper function to format dates with detected timezone
  const formatDateTime = (utcDateTime, showLocation = false) => {
    if (!timezoneData) {
      return formatDateTimeSync(utcDateTime);
    }
    
    if (showLocation && timezoneData.detected) {
      return formatDateTimeSync(utcDateTime, timezoneData.timezone).replace(
        /\s[A-Z]{3,4}$/,
        ` (${timezoneData.city}, ${timezoneData.country})`
      );
    }
    
    return formatDateTimeSync(utcDateTime, timezoneData.timezone);
  };

  return {
    timezoneData,
    loading,
    error,
    formatDateTime,
    timezone: timezoneData?.timezone,
    location: timezoneData ? {
      country: timezoneData.country,
      city: timezoneData.city,
      region: timezoneData.region
    } : null,
    isDetected: timezoneData?.detected || false
  };
}

export default useTimezone;
