import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(input: string | number): string {
  const date = new Date(input)
  return date.toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric",
  })
}

/**
 * Format date and time for display
 */
export function formatDateTime(input: string | number): string {
  const date = new Date(input)
  return date.toLocaleString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  })
}

/**
 * Convert ISO string to datetime-local format (YYYY-MM-DDTHH:MM)
 * Used for HTML5 datetime-local inputs
 */
export function toDateTimeLocal(isoString: string): string {
  if (!isoString) return '';
  try {
    const date = new Date(isoString);
    if (isNaN(date.getTime())) return '';

    // Adjust for local timezone to prevent timezone offset issues
    const localDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));

    // Format to YYYY-MM-DDTHH:MM
    const year = localDate.getFullYear();
    const month = String(localDate.getMonth() + 1).padStart(2, '0');
    const day = String(localDate.getDate()).padStart(2, '0');
    const hours = String(localDate.getHours()).padStart(2, '0');
    const minutes = String(localDate.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  } catch (error) {
    console.warn('Error formatting datetime:', error);
    return '';
  }
}

/**
 * Convert datetime-local format to ISO string
 */
export function fromDateTimeLocal(dateTimeLocal: string): string {
  if (!dateTimeLocal) return '';
  try {
    const date = new Date(dateTimeLocal);
    return date.toISOString();
  } catch (error) {
    console.warn('Error parsing datetime:', error);
    return '';
  }
}

export function absoluteUrl(path: string) {
  return `${import.meta.env.VITE_APP_URL}${path}`
}