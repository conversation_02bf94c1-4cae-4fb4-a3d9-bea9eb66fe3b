import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import store from './store/index.js';
import ThemeProvider from './providers/ThemeContext';
import App from './App';

ReactDOM.createRoot(document.getElementById('root')).render(
    <Provider store={store}>
      <Router>
        <ThemeProvider>
          <App />
        </ThemeProvider>
      </Router>
    </Provider>
);
