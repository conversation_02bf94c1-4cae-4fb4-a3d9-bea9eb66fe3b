import React, { useState } from 'react';
import { CustomDateTimePicker } from '../components/ui/CustomDateTimePicker';

/**
 * Test page for the improved CustomDateTimePicker
 * Shows how the component handles timezone conversions properly
 */
const DatePickerTest = () => {
  const [selectedDateTime, setSelectedDateTime] = useState('');
  const [formData, setFormData] = useState({
    startTime: '',
    endTime: ''
  });

  const handleDateTimeChange = (e) => {
    setSelectedDateTime(e.target.value);
    console.log('📅 DateTime selected:', {
      utcValue: e.target.value,
      localDisplay: new Date(e.target.value).toLocaleString()
    });
  };

  const handleFormChange = (field, e) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));
    console.log(`📝 ${field} changed:`, {
      utcValue: e.target.value,
      localDisplay: new Date(e.target.value).toLocaleString()
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8">
            📅 Improved Date Picker Test
          </h1>
          
          <div className="space-y-8">
            {/* Single Date Picker Test */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                Single Date Picker
              </h2>
              <div className="max-w-md">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Select Date & Time
                </label>
                <CustomDateTimePicker
                  value={selectedDateTime}
                  onChange={handleDateTimeChange}
                  placeholder="Choose date and time"
                  className="w-full"
                />
              </div>
              {selectedDateTime && (
                <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                    Selected Value:
                  </h3>
                  <div className="space-y-1 text-sm">
                    <p className="text-blue-800 dark:text-blue-200">
                      <strong>UTC (for API):</strong> {selectedDateTime}
                    </p>
                    <p className="text-blue-800 dark:text-blue-200">
                      <strong>Local Display:</strong> {new Date(selectedDateTime).toLocaleString()}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Form Example */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                Form Example (Start & End Times)
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Start Time
                  </label>
                  <CustomDateTimePicker
                    value={formData.startTime}
                    onChange={(e) => handleFormChange('startTime', e)}
                    placeholder="Select start time"
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    End Time
                  </label>
                  <CustomDateTimePicker
                    value={formData.endTime}
                    onChange={(e) => handleFormChange('endTime', e)}
                    placeholder="Select end time"
                    className="w-full"
                  />
                </div>
              </div>
              
              {(formData.startTime || formData.endTime) && (
                <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <h3 className="font-medium text-green-900 dark:text-green-100 mb-2">
                    Form Data:
                  </h3>
                  <div className="space-y-2 text-sm">
                    {formData.startTime && (
                      <div>
                        <p className="text-green-800 dark:text-green-200">
                          <strong>Start Time (UTC):</strong> {formData.startTime}
                        </p>
                        <p className="text-green-700 dark:text-green-300">
                          <strong>Start Time (Local):</strong> {new Date(formData.startTime).toLocaleString()}
                        </p>
                      </div>
                    )}
                    {formData.endTime && (
                      <div>
                        <p className="text-green-800 dark:text-green-200">
                          <strong>End Time (UTC):</strong> {formData.endTime}
                        </p>
                        <p className="text-green-700 dark:text-green-300">
                          <strong>End Time (Local):</strong> {new Date(formData.endTime).toLocaleString()}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Instructions */}
            <div className="mt-8 p-6 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg">
              <h3 className="font-semibold text-amber-900 dark:text-amber-100 mb-3">
                🔧 How the Improved Date Picker Works:
              </h3>
              <div className="space-y-2 text-sm text-amber-800 dark:text-amber-200">
                <p>✅ <strong>Simple Display:</strong> Shows clean "Jan 15, 2024 - 14:30" format</p>
                <p>✅ <strong>Local Time Input:</strong> You select time in your local timezone</p>
                <p>✅ <strong>UTC Storage:</strong> Automatically converts to UTC for API storage</p>
                <p>✅ <strong>Proper Conversion:</strong> No more confusing timezone displays</p>
                <p>✅ <strong>Clear Preview:</strong> Shows exactly what you selected in the picker</p>
                <p>✅ <strong>Consistent Behavior:</strong> Same logic everywhere in the app</p>
              </div>
            </div>

            {/* Debug Info */}
            <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                🐛 Debug Information:
              </h3>
              <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                <p><strong>Browser Timezone:</strong> {Intl.DateTimeFormat().resolvedOptions().timeZone}</p>
                <p><strong>Current Time (Local):</strong> {new Date().toLocaleString()}</p>
                <p><strong>Current Time (UTC):</strong> {new Date().toISOString()}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DatePickerTest;
