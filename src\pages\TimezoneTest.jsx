import React, { useState, useEffect } from 'react';
import { FiGlobe, FiClock, FiMapPin, FiWifi } from 'react-icons/fi';
import useTimezone from '../hooks/useTimezone';
import { 
  detectUserTimezone, 
  formatDateTimeSync, 
  convertLocalToUTC, 
  convertUTCToLocalInput,
  getCurrentUTC 
} from '../utils/timezone';

const TimezoneTest = () => {
  const { timezoneData, formatDateTime, loading, error } = useTimezone();
  const [testDateTime, setTestDateTime] = useState('');
  const [utcResult, setUtcResult] = useState('');
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Handle test conversion
  const handleTestConversion = () => {
    if (testDateTime) {
      const utc = convertLocalToUTC(testDateTime);
      setUtcResult(utc);
    }
  };

  const currentUTC = getCurrentUTC();
  const currentLocal = formatDateTimeSync(currentUTC);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 flex items-center gap-3">
            <FiGlobe className="text-blue-600" />
            EduFair Timezone Detection Test
          </h1>

          {/* Current Time Display */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-blue-900 dark:text-blue-100 mb-4 flex items-center gap-2">
                <FiClock className="w-5 h-5" />
                Current Time
              </h2>
              <div className="space-y-2">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <strong>Local:</strong> {currentTime.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <strong>UTC:</strong> {currentUTC}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <strong>Formatted:</strong> {currentLocal}
                </p>
              </div>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-green-900 dark:text-green-100 mb-4 flex items-center gap-2">
                <FiMapPin className="w-5 h-5" />
                Location Detection
              </h2>
              {loading ? (
                <div className="flex items-center gap-2 text-blue-600">
                  <FiWifi className="w-4 h-4 animate-pulse" />
                  <span>Detecting your location...</span>
                </div>
              ) : error ? (
                <div className="text-red-600">
                  <p>Detection failed: {error.message}</p>
                </div>
              ) : timezoneData ? (
                <div className="space-y-2">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <strong>Country:</strong> {timezoneData.country}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <strong>City:</strong> {timezoneData.city}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <strong>Timezone:</strong> {timezoneData.timezone}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <strong>Source:</strong> {timezoneData.source}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <strong>IP:</strong> {timezoneData.ip}
                  </p>
                  <div className={`inline-flex px-2 py-1 rounded text-xs font-medium ${
                    timezoneData.detected 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                  }`}>
                    {timezoneData.detected ? '✅ IP Detected' : '🌐 Browser Fallback'}
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">No timezone data available</p>
              )}
            </div>
          </div>

          {/* Conversion Test */}
          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-purple-900 dark:text-purple-100 mb-4">
              Timezone Conversion Test
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Enter Local Date & Time:
                </label>
                <input
                  type="datetime-local"
                  value={testDateTime}
                  onChange={(e) => setTestDateTime(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              <button
                onClick={handleTestConversion}
                disabled={!testDateTime}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Convert to UTC
              </button>
              {utcResult && (
                <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-md">
                  <p className="text-sm">
                    <strong>Local Input:</strong> {testDateTime}
                  </p>
                  <p className="text-sm">
                    <strong>UTC Result:</strong> {utcResult}
                  </p>
                  <p className="text-sm">
                    <strong>Back to Local:</strong> {convertUTCToLocalInput(utcResult)}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Example Exam Times */}
          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-orange-900 dark:text-orange-100 mb-4">
              Example Exam Times (How Students See Them)
            </h2>
            <div className="space-y-3">
              {[
                '2024-12-25T09:00:00', // Christmas morning exam
                '2024-12-31T23:30:00', // New Year's Eve exam
                '2024-06-15T14:00:00', // Afternoon exam
              ].map((utcTime, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-white dark:bg-gray-700 rounded-md">
                  <div>
                    <p className="text-sm font-medium">Exam {index + 1}</p>
                    <p className="text-xs text-gray-500">UTC: {utcTime}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-orange-600 dark:text-orange-400">
                      {formatDateTime(utcTime, true)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Technical Info */}
          <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">How It Works:</h3>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• 🌍 Detects your location using IP geolocation APIs</li>
              <li>• 🕒 Backend stores all exam times in UTC</li>
              <li>• 🔄 Frontend converts UTC to your local time for display</li>
              <li>• 📤 When creating exams, local time is converted to UTC before sending to API</li>
              <li>• 🎯 Students see exam times in their own timezone automatically</li>
              <li>• 🔒 Fallback to browser timezone if IP detection fails</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimezoneTest;
