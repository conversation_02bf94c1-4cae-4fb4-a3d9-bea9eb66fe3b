import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import { fetchAllExamsList, resetExamsList, deleteExam } from '../../store/slices/ExamSlice';
import ExamTable from '../../components/ui/tables/ExamTable';
import { LoadMoreButton } from '../../components/ui/Pagination';
import { 
  FiSearch, 
  FiFilter, 
  FiRefreshCw,
  FiFileText,
  FiCalendar,
  FiUsers,
  FiTrendingUp,
  FiEye,
  FiTrash2
} from 'react-icons/fi';

const AdminExams = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();
  
  // Redux state - using optimized exam list
  const { examsList } = useSelector((state) => state.exams);
  const { data: exams, loading, error, pagination } = examsList;
  
  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [refreshing, setRefreshing] = useState(false);

  // Theme classes
  const themeClasses = useMemo(() => ({
    bg: currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50",
    text: currentTheme === "dark" ? "text-gray-100" : "text-gray-900",
    cardBg: currentTheme === "dark" ? "bg-gray-800" : "bg-white",
    input: currentTheme === "dark" ? "bg-gray-800 text-gray-100 border-gray-700" : "bg-white text-gray-900 border-gray-300",
    button: currentTheme === "dark" ? "bg-violet-600 hover:bg-violet-700" : "bg-violet-600 hover:bg-violet-700"
  }), [currentTheme]);

  // Fetch exams on component mount using optimized API
  useEffect(() => {
    dispatch(resetExamsList()); // Reset state first
    dispatch(fetchAllExamsList({ skip: 0, limit: 50 })); // Load first page
  }, [dispatch]);

  // Filter exams based on search term and status
  const filteredExams = useMemo(() => {
    if (!exams) return [];
    
    return exams.filter(exam => {
      const matchesSearch = !searchTerm || 
        exam.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.description?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || exam.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    });
  }, [exams, searchTerm, statusFilter]);

  // Calculate stats
  const stats = useMemo(() => {
    if (!exams) return { total: 0, draft: 0, active: 0, completed: 0 };
    
    return {
      total: exams.length,
      draft: exams.filter(e => e.status === 'draft').length,
      active: exams.filter(e => e.status === 'active').length,
      completed: exams.filter(e => e.status === 'completed').length
    };
  }, [exams]);

  // Handlers
  const handleViewExam = (exam) => {
    navigate(`/admin/exam/${exam.id}`);
  };

  const handleDeleteExam = async (exam) => {
    if (window.confirm(`Are you sure you want to delete "${exam.title}"? This action cannot be undone.`)) {
      try {
        await dispatch(deleteExam(exam.id)).unwrap();
        // Refresh the list using optimized API
        dispatch(resetExamsList());
        dispatch(fetchAllExamsList({ skip: 0, limit: 50 }));
      } catch (error) {
        console.error('Failed to delete exam:', error);
      }
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      dispatch(resetExamsList()); // Reset state first
      await dispatch(fetchAllExamsList({ skip: 0, limit: 50 })).unwrap();
    } catch (error) {
      console.error('Failed to refresh exams:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleBulkAction = (action, selectedExams) => {
    console.log('Bulk action:', action, selectedExams);
    // Implement bulk actions like bulk delete, bulk publish, etc.
  };

  return (
    <div className={`min-h-screen ${themeClasses.bg} transition-colors duration-200`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div className="flex-1">
            <h1 className={`text-3xl font-bold ${themeClasses.text} mb-2`}>
              All Exams
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage and monitor all exams in the system
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3 mt-4 lg:mt-0">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200 flex items-center gap-2"
            >
              <FiRefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className={`${themeClasses.cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <FiFileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Exams</p>
                <p className={`text-2xl font-bold ${themeClasses.text}`}>{stats.total}</p>
              </div>
            </div>
          </div>

          <div className={`${themeClasses.cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="flex items-center">
              <div className="p-3 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                <FiCalendar className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Draft</p>
                <p className={`text-2xl font-bold ${themeClasses.text}`}>{stats.draft}</p>
              </div>
            </div>
          </div>

          <div className={`${themeClasses.cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="flex items-center">
              <div className="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
                <FiUsers className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active</p>
                <p className={`text-2xl font-bold ${themeClasses.text}`}>{stats.active}</p>
              </div>
            </div>
          </div>

          <div className={`${themeClasses.cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <FiTrendingUp className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed</p>
                <p className={`text-2xl font-bold ${themeClasses.text}`}>{stats.completed}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className={`${themeClasses.cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-6`}>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search exams by title or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent ${themeClasses.input}`}
                />
              </div>
            </div>
            
            <div className="flex gap-3">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className={`px-4 py-2 border rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent ${themeClasses.input}`}
              >
                <option value="all">All Status</option>
                <option value="draft">Draft</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          </div>
        </div>

        {/* Exams Table */}
        <div className={`${themeClasses.cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden`}>
          <ExamTable
            exams={filteredExams}
            loading={loading}
            onView={handleViewExam}
            onDelete={handleDeleteExam}
            onBulkAction={handleBulkAction}
            showActions={true}
            userRole="admin"
            selectable={true}
            showClassroom={true}
          />
        </div>

        {/* Load More Button */}
        <LoadMoreButton
          hasMore={pagination.hasMore}
          loading={loading}
          onLoadMore={() => dispatch(fetchAllExamsList({
            skip: pagination.skip,
            limit: pagination.limit
          }))}
          text="Load More Exams"
          theme={currentTheme}
          className="mt-6"
        />

        {/* Error Display */}
        {error && (
          <div className="mt-6 p-4 bg-red-100 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg">
            <p className="text-red-700 dark:text-red-300">
              Error loading exams: {typeof error === 'string' ? error : 'An unexpected error occurred'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminExams;
