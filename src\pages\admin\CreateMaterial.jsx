import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchSubjects,
  createSubject,
} from "../../store/slices/SubjectSlice";
import {
  fetchChaptersBySubject,
  createChapter,
} from "../../store/slices/ChapterSlice";
import {
  fetchTopicsByChapter,
  createTopic,
} from "../../store/slices/TopicSlice";
import {
  fetchSubtopicsByTopic,
  createSubtopic,
} from "../../store/slices/SubtopicSlice";

function CreateMaterial() {
  const dispatch = useDispatch();

  // State for selections
  const [selectedSubject, setSelectedSubject] = useState("");
  const [selectedChapter, setSelectedChapter] = useState("");
  const [selectedTopic, setSelectedTopic] = useState("");

  // State for create forms
  const [newSubject, setNewSubject] = useState("");
  const [newChapter, setNewChapter] = useState("");
  const [newTopic, setNewTopic] = useState("");
  const [newSubtopic, setNewSubtopic] = useState("");

  // Redux state
  const { subjects, loading: subjectsLoading } = useSelector((state) => state.subjects);
  const { chaptersBySubject = [], loading: chaptersLoading } = useSelector((state) => state.chapters);
  const { topicsByChapter = [], loading: topicsLoading } = useSelector((state) => state.topics);
  const { subtopicsByTopic = [], loading: subtopicsLoading } = useSelector((state) => state.subtopics);

  // Fetch subjects on mount
  useEffect(() => {
    dispatch(fetchSubjects());
  }, [dispatch]);

  // Fetch chapters when subject changes
  useEffect(() => {
    if (selectedSubject) {
      dispatch(fetchChaptersBySubject({ subjectId: selectedSubject }));
    }
  }, [dispatch, selectedSubject]);

  // Fetch topics when chapter changes
  useEffect(() => {
    if (selectedChapter) {
      dispatch(fetchTopicsByChapter({ chapterId: selectedChapter }));
    }
  }, [dispatch, selectedChapter]);

  // Fetch subtopics when topic changes
  useEffect(() => {
    if (selectedTopic) {
      dispatch(fetchSubtopicsByTopic({ topicId: selectedTopic }));
    }
  }, [dispatch, selectedTopic]);

  // Create handlers
  const handleCreateSubject = (e) => {
    e.preventDefault();
    if (newSubject.trim()) {
      dispatch(createSubject({ name: newSubject.trim() })).then(() => {
        setNewSubject("");
        dispatch(fetchSubjects());
      });
    }
  };
  const handleCreateChapter = (e) => {
    e.preventDefault();
    if (newChapter.trim() && selectedSubject) {
      dispatch(createChapter({ name: newChapter.trim(), subject_id: selectedSubject })).then(() => {
        setNewChapter("");
        dispatch(fetchChaptersBySubject({ subjectId: selectedSubject }));
      });
    }
  };
  const handleCreateTopic = (e) => {
    e.preventDefault();
    if (newTopic.trim() && selectedChapter) {
      dispatch(createTopic({ name: newTopic.trim(), chapter_id: selectedChapter })).then(() => {
        setNewTopic("");
        dispatch(fetchTopicsByChapter({ chapterId: selectedChapter }));
      });
    }
  };
  const handleCreateSubtopic = (e) => {
    e.preventDefault();
    if (newSubtopic.trim() && selectedTopic) {
      dispatch(createSubtopic({ name: newSubtopic.trim(), topic_id: selectedTopic })).then(() => {
        setNewSubtopic("");
        dispatch(fetchSubtopicsByTopic({ topicId: selectedTopic }));
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">Create Material</h1>
      </div>
      <div className="max-w-4xl space-y-6">
        {/* Subject Dropdown & Create */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <label className="block text-lg font-semibold text-gray-700 dark:text-gray-200 mb-2">Subject</label>
          <select
            className="w-full px-4 py-2 rounded border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 mb-2"
            value={selectedSubject}
            onChange={(e) => {
              setSelectedSubject(e.target.value);
              setSelectedChapter("");
              setSelectedTopic("");
            }}
          >
            <option value="">Select Subject</option>
            {subjects && subjects.map((s) => (
              <option key={s.id} value={s.id}>{s.name}</option>
            ))}
          </select>
          <form className="flex gap-2 mt-2" onSubmit={handleCreateSubject}>
            <input
              type="text"
              className="flex-1 px-3 py-2 rounded border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              placeholder="New Subject Name"
              value={newSubject}
              onChange={(e) => setNewSubject(e.target.value)}
            />
            <button type="submit" className="px-4 py-2 bg-violet-600 text-white rounded hover:bg-violet-700 transition">Create</button>
          </form>
        </div>
        {/* Chapter Dropdown & Create */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <label className="block text-lg font-semibold text-gray-700 dark:text-gray-200 mb-2">Chapter</label>
          <select
            className="w-full px-4 py-2 rounded border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 mb-2"
            value={selectedChapter}
            onChange={(e) => {
              setSelectedChapter(e.target.value);
              setSelectedTopic("");
            }}
            disabled={!selectedSubject}
          >
            <option value="">{selectedSubject ? "Select Chapter" : "Select Subject First"}</option>
            {chaptersBySubject && chaptersBySubject.map((c) => (
              <option key={c.id} value={c.id}>{c.name}</option>
            ))}
          </select>
          <form className="flex gap-2 mt-2" onSubmit={handleCreateChapter}>
            <input
              type="text"
              className="flex-1 px-3 py-2 rounded border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              placeholder="New Chapter Name"
              value={newChapter}
              onChange={(e) => setNewChapter(e.target.value)}
              disabled={!selectedSubject}
            />
            <button type="submit" className="px-4 py-2 bg-violet-600 text-white rounded hover:bg-violet-700 transition" disabled={!selectedSubject}>Create</button>
          </form>
        </div>
        {/* Topic Dropdown & Create */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <label className="block text-lg font-semibold text-gray-700 dark:text-gray-200 mb-2">Topic</label>
          <select
            className="w-full px-4 py-2 rounded border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 mb-2"
            value={selectedTopic}
            onChange={(e) => setSelectedTopic(e.target.value)}
            disabled={!selectedChapter}
          >
            <option value="">{selectedChapter ? "Select Topic" : "Select Chapter First"}</option>
            {topicsByChapter && topicsByChapter.map((t) => (
              <option key={t.id} value={t.id}>{t.name}</option>
            ))}
          </select>
          <form className="flex gap-2 mt-2" onSubmit={handleCreateTopic}>
            <input
              type="text"
              className="flex-1 px-3 py-2 rounded border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              placeholder="New Topic Name"
              value={newTopic}
              onChange={(e) => setNewTopic(e.target.value)}
              disabled={!selectedChapter}
            />
            <button type="submit" className="px-4 py-2 bg-violet-600 text-white rounded hover:bg-violet-700 transition" disabled={!selectedChapter}>Create</button>
          </form>
        </div>
        {/* Subtopic Dropdown & Create */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <label className="block text-lg font-semibold text-gray-700 dark:text-gray-200 mb-2">Subtopic</label>
          <select
            className="w-full px-4 py-2 rounded border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 mb-2"
            value={""}
            disabled={!selectedTopic}
          >
            <option value="">{selectedTopic ? "Select Subtopic" : "Select Topic First"}</option>
            {subtopicsByTopic && subtopicsByTopic.map((s) => (
              <option key={s.id} value={s.id}>{s.name}</option>
            ))}
          </select>
          <form className="flex gap-2 mt-2" onSubmit={handleCreateSubtopic}>
            <input
              type="text"
              className="flex-1 px-3 py-2 rounded border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              placeholder="New Subtopic Name"
              value={newSubtopic}
              onChange={(e) => setNewSubtopic(e.target.value)}
              disabled={!selectedTopic}
            />
            <button type="submit" className="px-4 py-2 bg-violet-600 text-white rounded hover:bg-violet-700 transition" disabled={!selectedTopic}>Create</button>
          </form>
        </div>
      </div>
    </div>
  );
}

export default CreateMaterial;
