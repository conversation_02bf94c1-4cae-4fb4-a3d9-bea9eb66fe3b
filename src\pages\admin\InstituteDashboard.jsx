import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiUsers,
  FiUserCheck,
  FiTrendingUp,
  FiAward,
  FiCalendar,
  FiActivity,
  FiPlus,
  FiEye,
  FiFilter,
  FiRefreshCw,
  FiBell
} from 'react-icons/fi';
import {
  fetchInstituteDashboardSummary,
  fetchInstituteAnalytics,
  fetchInstituteRecentActivities,
  fetchInstituteNotifications,
  fetchInstituteQuickStats,
  selectSummary,
  selectSummaryLoading,
  selectSummaryError,
  selectAnalytics,
  selectAnalyticsLoading,
  selectRecentActivities,
  selectRecentActivitiesLoading,
  selectNotifications,
  selectNotificationsLoading,
  selectQuickStats,
  selectQuickStatsLoading,
  clearErrors
} from '../../store/slices/InstituteDashboardSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import { useNavigate } from 'react-router-dom';

function InstituteDashboard() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [refreshing, setRefreshing] = useState(false);

  // Redux state
  const summary = useSelector(selectSummary);
  const summaryLoading = useSelector(selectSummaryLoading);
  const summaryError = useSelector(selectSummaryError);
  const analytics = useSelector(selectAnalytics);
  const analyticsLoading = useSelector(selectAnalyticsLoading);
  const recentActivities = useSelector(selectRecentActivities);
  const recentActivitiesLoading = useSelector(selectRecentActivitiesLoading);

  const notifications = useSelector(selectNotifications);
  const notificationsLoading = useSelector(selectNotificationsLoading);

  const quickStats = useSelector(selectQuickStats);
  const quickStatsLoading = useSelector(selectQuickStatsLoading);

  // Load dashboard data on mount
  useEffect(() => {
    dispatch(fetchInstituteDashboardSummary());
    dispatch(fetchInstituteAnalytics());
    dispatch(fetchInstituteRecentActivities({ limit: 10 }));
    dispatch(fetchInstituteNotifications({ limit: 10 }));
    dispatch(fetchInstituteQuickStats());
  }, [dispatch]);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        dispatch(fetchInstituteDashboardSummary()),
        dispatch(fetchInstituteAnalytics()),
        dispatch(fetchInstituteRecentActivities({ limit: 10 })),
        dispatch(fetchInstituteNotifications({ limit: 10 })),
        dispatch(fetchInstituteQuickStats())
      ]);
    } finally {
      setRefreshing(false);
    }
  };

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      dispatch(clearErrors());
    };
  }, [dispatch]);

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
      {/* Dashboard Header */}
      <div className="sm:flex sm:justify-between sm:items-center mb-8">
        <div className="mb-4 sm:mb-0">
          <h1 className="text-2xl md:text-3xl text-gray-800 font-bold">Institute Dashboard</h1>
          <p className="text-gray-600">Welcome back! Here's what's happening at your institute.</p>
        </div>

        <div className="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <FiRefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Error Messages */}
      {summaryError && (
        <div className="mb-6">
          <ErrorMessage message={summaryError} />
        </div>
      )}

      {/* Summary Cards */}
      {summaryLoading ? (
        <div className="flex justify-center items-center h-32">
          <LoadingSpinner size="lg" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Mentors */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiUsers className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Mentors</p>
                <p className="text-2xl font-semibold text-gray-900">{summary.totalMentors}</p>
              </div>
            </div>
          </div>

          {/* Active Mentors */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiUserCheck className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Mentors</p>
                <p className="text-2xl font-semibold text-gray-900">{summary.activeMentors}</p>
              </div>
            </div>
          </div>

          {/* Total Events */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiCalendar className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Events</p>
                <p className="text-2xl font-semibold text-gray-900">{summary.totalEvents}</p>
              </div>
            </div>
          </div>

          {/* Upcoming Events */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiTrendingUp className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Upcoming Events</p>
                <p className="text-2xl font-semibold text-gray-900">{summary.upcomingEvents}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <button
                onClick={() => navigate('/institute/events/create')}
                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <FiPlus className="h-6 w-6 text-blue-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Create Event</span>
              </button>

              <button
                onClick={() => navigate('/institute/mentors')}
                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <FiUsers className="h-6 w-6 text-green-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Manage Mentors</span>
              </button>

              <button
                onClick={() => navigate('/institute/events')}
                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <FiCalendar className="h-6 w-6 text-purple-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">View Events</span>
              </button>

              <button
                onClick={() => navigate('/institute/mentors/applications')}
                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <FiAward className="h-6 w-6 text-orange-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Review Applications</span>
              </button>
            </div>
          </div>
        </div>

        {/* Pending Applications */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Pending Applications</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Mentor Applications</span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                {summary.pendingMentorApplications}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Event Registrations</span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {summary.totalEventAttendees}
              </span>
            </div>
            <button
              onClick={() => navigate('/institute/mentors')}
              className="w-full text-left text-sm text-blue-600 hover:text-blue-800"
            >
              View all applications →
            </button>
          </div>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Activities</h3>
        </div>
        <div className="p-6">
          {recentActivitiesLoading ? (
            <div className="flex justify-center items-center h-32">
              <LoadingSpinner size="md" />
            </div>
          ) : recentActivities && recentActivities.length > 0 ? (
            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <FiActivity className="h-5 w-5 text-gray-400" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{activity.description}</p>
                    <p className="text-xs text-gray-500">{activity.timestamp}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FiActivity className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No recent activities</h3>
              <p className="mt-1 text-sm text-gray-500">
                Activities will appear here as they happen.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Notifications */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Notifications</h3>
            <FiBell className="h-5 w-5 text-gray-400" />
          </div>
        </div>
        <div className="p-6">
          {notificationsLoading ? (
            <div className="flex justify-center items-center h-32">
              <LoadingSpinner size="md" />
            </div>
          ) : notifications && notifications.length > 0 ? (
            <div className="space-y-4">
              {notifications.map((notification, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className={`h-2 w-2 rounded-full mt-2 ${
                      notification.priority === 'high' ? 'bg-red-400' :
                      notification.priority === 'medium' ? 'bg-yellow-400' : 'bg-blue-400'
                    }`} />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{notification.title}</p>
                    <p className="text-xs text-gray-500">{notification.message}</p>
                    <p className="text-xs text-gray-400 mt-1">{notification.timestamp}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FiBell className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
              <p className="mt-1 text-sm text-gray-500">
                You're all caught up! Notifications will appear here.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default InstituteDashboard;