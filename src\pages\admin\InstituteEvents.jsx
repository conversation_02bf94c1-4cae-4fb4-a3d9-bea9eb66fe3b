import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiPlus,
  FiCalendar,
  FiUsers,
  FiMapPin,
  FiClock,
  FiEdit,
  FiTrash2,
  FiEye,
  FiFilter,
  FiRefreshCw
} from 'react-icons/fi';
import {
  fetchInstituteEvents,
  createInstituteEvent,
  deleteInstituteEvent,
  publishInstituteEvent,
  selectEvents,
  selectEventsLoading,
  selectEventsError,
  selectCreateLoading,
  selectCreateSuccess,
  clearErrors,
  clearSuccessStates
} from '../../store/slices/InstituteEventsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import { useNavigate } from 'react-router-dom';

function InstituteEvents() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState('all');

  // Redux selectors
  const events = useSelector(selectEvents);
  const eventsLoading = useSelector(selectEventsLoading);
  const eventsError = useSelector(selectEventsError);
  const createLoading = useSelector(selectCreateLoading);
  const createSuccess = useSelector(selectCreateSuccess);

  // Load events on component mount
  useEffect(() => {
    dispatch(fetchInstituteEvents({ skip: 0, limit: 20 }));
  }, [dispatch]);

  // Handle create success
  useEffect(() => {
    if (createSuccess) {
      dispatch(clearSuccessStates());
      // Optionally show success message
    }
  }, [createSuccess, dispatch]);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await dispatch(fetchInstituteEvents({ skip: 0, limit: 20 }));
    } finally {
      setRefreshing(false);
    }
  };

  // Handle filter change
  const handleFilterChange = (newFilter) => {
    setFilter(newFilter);
    dispatch(fetchInstituteEvents({ 
      skip: 0, 
      limit: 20, 
      status: newFilter === 'all' ? '' : newFilter 
    }));
  };

  // Handle delete event
  const handleDeleteEvent = async (eventId) => {
    if (window.confirm('Are you sure you want to delete this event?')) {
      await dispatch(deleteInstituteEvent(eventId));
    }
  };

  // Handle publish event
  const handlePublishEvent = async (eventId) => {
    await dispatch(publishInstituteEvent(eventId));
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      dispatch(clearErrors());
    };
  }, [dispatch]);

  if (eventsError) {
    return <ErrorMessage message={eventsError} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Events</h1>
          <p className="text-gray-600">Manage your institute events</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <FiRefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={() => navigate('/institute/events/create')}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiPlus className="h-4 w-4 mr-2" />
            Create Event
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex items-center space-x-4">
          <FiFilter className="h-5 w-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-700">Filter by status:</span>
          <div className="flex space-x-2">
            {['all', 'published', 'draft', 'cancelled'].map((status) => (
              <button
                key={status}
                onClick={() => handleFilterChange(status)}
                className={`px-3 py-1 rounded-full text-xs font-medium ${
                  filter === status
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Events List */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Events ({events.total})
          </h3>
        </div>
        <div className="p-6">
          {eventsLoading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner size="lg" />
            </div>
          ) : events.data && events.data.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {events.data.map((event) => (
                <div key={event.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start mb-3">
                    <h4 className="text-lg font-medium text-gray-900 truncate">{event.title}</h4>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(event.status)}`}>
                      {event.status}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-4 line-clamp-2">{event.description}</p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <FiCalendar className="h-4 w-4 mr-2" />
                      {formatDate(event.startDateTime)}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <FiMapPin className="h-4 w-4 mr-2" />
                      {event.location?.venue || 'TBD'}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <FiUsers className="h-4 w-4 mr-2" />
                      {event.attendeesCount || 0} attendees
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => navigate(`/institute/events/${event.id}`)}
                        className="text-blue-600 hover:text-blue-800"
                        title="View Details"
                      >
                        <FiEye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => navigate(`/institute/events/${event.id}/edit`)}
                        className="text-gray-600 hover:text-gray-800"
                        title="Edit Event"
                      >
                        <FiEdit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteEvent(event.id)}
                        className="text-red-600 hover:text-red-800"
                        title="Delete Event"
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </button>
                    </div>
                    
                    {event.status === 'draft' && (
                      <button
                        onClick={() => handlePublishEvent(event.id)}
                        className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                      >
                        Publish
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FiCalendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No events found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating your first event.
              </p>
              <div className="mt-6">
                <button
                  onClick={() => navigate('/institute/events/create')}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <FiPlus className="h-4 w-4 mr-2" />
                  Create Event
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Load More */}
      {events.pagination?.hasMore && (
        <div className="text-center">
          <button
            onClick={() => dispatch(fetchInstituteEvents({ 
              skip: events.pagination.skip, 
              limit: 20,
              status: filter === 'all' ? '' : filter 
            }))}
            disabled={eventsLoading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {eventsLoading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Loading...
              </>
            ) : (
              'Load More'
            )}
          </button>
        </div>
      )}
    </div>
  );
}

export default InstituteEvents;
