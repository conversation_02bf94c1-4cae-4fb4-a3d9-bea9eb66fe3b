import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchPlans,
  createPlan,
  updatePlan,
  deletePlan,
  activatePlan,
  deactivatePlan,
} from "../../store/slices/PlanSlice";
import SearchFilterCard from "../../components/ui/SearchFilterCard";
import SkeletonLoader from "../../components/ui/SkeletonLoader";
import ErrorStates from "../../components/ui/ErrorStates";
import ErrorBoundary from "../../components/ui/ErrorBoundary";
import { useFormValidation, validationRules, createValidationSchema } from "../../hooks/useFormValidation";
import FormComponents from "../../components/ui/FormComponents";
import AutoSaveIndicator from "../../components/ui/AutoSaveIndicator";

import {
  FiPlus,
  FiEdit,
  FiTrash2,
  FiDollarSign,
  FiCalendar,
  <PERSON><PERSON>heck,
  FiX,
  FiToggleLeft,
  FiToggleRight,
  FiCreditCard,
  FiRefreshCw
} from "react-icons/fi";

const initialForm = {
  name: "",
  description: "",
  price: 0,
  duration_days: 0,
  features: "",
  is_active: true,
};

function Plans() {
  const dispatch = useDispatch();

  // Redux state
  const { plans, loading, error } = useSelector((state) => state.plans);

  // Enhanced loading and error handling
  const [actionLoading, setActionLoading] = useState({});

  // Retry mechanism for data fetching
  const handleRetryFetch = async () => {
    try {
      await dispatch(fetchPlans()).unwrap();
    } catch (error) {
      console.error('Failed to fetch plans:', error);
    }
  };

  // UI state
  const [showForm, setShowForm] = useState(false);
  const [editId, setEditId] = useState(null);
  const [deleteId, setDeleteId] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState("grid"); // grid or table
  const [filterStatus, setFilterStatus] = useState("all"); // all, active, inactive
  const [sortBy, setSortBy] = useState("name"); // name, price, duration

  // Validation schema
  const validationSchema = createValidationSchema({
    name: [
      validationRules.required('Plan name is required'),
      validationRules.minLength(3, 'Plan name must be at least 3 characters'),
      validationRules.maxLength(50, 'Plan name must be no more than 50 characters')
    ],
    description: [
      validationRules.required('Description is required'),
      validationRules.minLength(10, 'Description must be at least 10 characters'),
      validationRules.maxLength(200, 'Description must be no more than 200 characters')
    ],
    price: [
      validationRules.required('Price is required'),
      validationRules.min(0, 'Price must be a positive number'),
      validationRules.max(9999, 'Price must be less than $10,000')
    ],
    duration_days: [
      validationRules.required('Duration is required'),
      validationRules.min(1, 'Duration must be at least 1 day'),
      validationRules.max(365, 'Duration must be no more than 365 days')
    ],
    features: [
      validationRules.required('Features are required'),
      validationRules.minLength(5, 'Please provide more details about the features')
    ]
  });

  // Enhanced form validation
  const {
    values: form,
    isSubmitting: formSubmitting,
    isDirty,
    handleSubmit: handleFormSubmit,
    reset: resetForm,
    getFieldProps
  } = useFormValidation(initialForm, validationSchema);

  // Fetch plans on mount
  useEffect(() => {
    dispatch(fetchPlans());
  }, [dispatch]);

  // Reset form on close
  useEffect(() => {
    if (!showForm) {
      resetForm();
      setEditId(null);
    }
  }, [showForm, resetForm]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    const isValid = await handleFormSubmit(async (formData) => {
      setActionLoading(prev => ({ ...prev, submit: true }));

      try {
        if (editId) {
          await dispatch(updatePlan({ id: editId, data: formData })).unwrap();
        } else {
          await dispatch(createPlan(formData)).unwrap();
        }
        setShowForm(false);
        resetForm();
        setEditId(null);
        dispatch(fetchPlans());
      } catch (error) {
        console.error('Failed to save plan:', error);
        throw error; // Re-throw to be handled by form validation
      } finally {
        setActionLoading(prev => ({ ...prev, submit: false }));
      }
    });

    return isValid;
  };

  const handleEdit = (plan) => {
    resetForm({
      name: plan.name,
      description: plan.description,
      price: plan.price,
      duration_days: plan.duration_days,
      features: plan.features,
      is_active: plan.is_active,
    });
    setEditId(plan.id);
    setShowForm(true);
  };

  const handleDelete = async () => {
    if (!deleteId) return;

    setActionLoading(prev => ({ ...prev, delete: true }));

    try {
      await dispatch(deletePlan(deleteId)).unwrap();
      setShowDeleteConfirm(false);
      setDeleteId(null);
      dispatch(fetchPlans());
    } catch (error) {
      console.error('Failed to delete plan:', error);
      // Keep modal open to show error
    } finally {
      setActionLoading(prev => ({ ...prev, delete: false }));
    }
  };

  const handleActivate = async (id) => {
    setActionLoading(prev => ({ ...prev, [`activate_${id}`]: true }));

    try {
      await dispatch(activatePlan(id)).unwrap();
      dispatch(fetchPlans());
    } catch (error) {
      console.error('Failed to activate plan:', error);
    } finally {
      setActionLoading(prev => ({ ...prev, [`activate_${id}`]: false }));
    }
  };

  const handleDeactivate = async (id) => {
    setActionLoading(prev => ({ ...prev, [`deactivate_${id}`]: true }));

    try {
      await dispatch(deactivatePlan(id)).unwrap();
      dispatch(fetchPlans());
    } catch (error) {
      console.error('Failed to deactivate plan:', error);
    } finally {
      setActionLoading(prev => ({ ...prev, [`deactivate_${id}`]: false }));
    }
  };

  const handleSearchChange = (value) => {
    setSearchTerm(value);
  };

  const handleSearchSubmit = () => {
    // Search is handled in real-time, no need for submit action
  };

  const handleSearchClear = () => {
    setSearchTerm("");
  };

  // Utility functions
  const getFilteredAndSortedPlans = () => {
    let filtered = plans.filter((plan) => {
      const matchesSearch = plan.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           plan.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === "all" ||
                           (filterStatus === "active" && plan.is_active) ||
                           (filterStatus === "inactive" && !plan.is_active);
      return matchesSearch && matchesStatus;
    });

    // Sort plans
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "price":
          return a.price - b.price;
        case "duration":
          return a.duration_days - b.duration_days;
        case "name":
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return filtered;
  };

  const filteredPlans = getFilteredAndSortedPlans();

  // Stats calculations
  const totalPlans = plans.length;
  const activePlans = plans.filter(plan => plan.is_active).length;
  const inactivePlans = totalPlans - activePlans;
  const averagePrice = plans.length > 0 ?
    (plans.reduce((sum, plan) => sum + plan.price, 0) / plans.length).toFixed(2) : 0;

  return (
    <ErrorBoundary>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
            Plans Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1 text-sm sm:text-base">
            Create and manage subscription plans for your platform
          </p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
          <button
            onClick={() => dispatch(fetchPlans())}
            className="btn bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 text-gray-600 dark:text-gray-300 min-h-[44px] px-4 py-2 sm:btn-sm"
          >
            <FiRefreshCw className="w-4 h-4 mr-2" />
            <span className="sm:hidden">Refresh Data</span>
            <span className="hidden sm:inline">Refresh</span>
          </button>
          <button
            onClick={() => { setShowForm(true); setEditId(null); resetForm(); }}
            className="btn bg-violet-500 hover:bg-violet-600 text-white min-h-[44px] px-4 py-2 sm:btn-sm"
          >
            <FiPlus className="w-4 h-4 mr-2" />
            <span className="sm:hidden">Create New Plan</span>
            <span className="hidden sm:inline">New Plan</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 hover:shadow-md transition-shadow touch-manipulation">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="mb-3 sm:mb-0">
              <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Total Plans</p>
              <p className="text-xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-1 sm:mt-2">{totalPlans}</p>
            </div>
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center self-end sm:self-auto">
              <FiCreditCard className="text-blue-600 dark:text-blue-400" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 hover:shadow-md transition-shadow touch-manipulation">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="mb-3 sm:mb-0">
              <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Active Plans</p>
              <p className="text-xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-1 sm:mt-2">{activePlans}</p>
            </div>
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center self-end sm:self-auto">
              <FiCheck className="text-green-600 dark:text-green-400" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 hover:shadow-md transition-shadow touch-manipulation">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="mb-3 sm:mb-0">
              <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Inactive Plans</p>
              <p className="text-xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-1 sm:mt-2">{inactivePlans}</p>
            </div>
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 dark:bg-red-900/30 rounded-xl flex items-center justify-center self-end sm:self-auto">
              <FiX className="text-red-600 dark:text-red-400" size={20} />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 hover:shadow-md transition-shadow touch-manipulation">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="mb-3 sm:mb-0">
              <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Avg. Price</p>
              <p className="text-xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 mt-1 sm:mt-2">${averagePrice}</p>
            </div>
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-violet-100 dark:bg-violet-900/30 rounded-xl flex items-center justify-center self-end sm:self-auto">
              <FiDollarSign className="text-violet-600 dark:text-violet-400" size={20} />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <SearchFilterCard
        searchValue={searchTerm}
        onSearchChange={handleSearchChange}
        onSearchSubmit={handleSearchSubmit}
        onSearchClear={handleSearchClear}
        searchPlaceholder="Search plans by name or description..."
        filters={[
          {
            label: 'Status',
            value: filterStatus,
            onChange: setFilterStatus,
            options: [
              { value: 'all', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' }
            ]
          },
          {
            label: 'Sort by',
            value: sortBy,
            onChange: setSortBy,
            options: [
              { value: 'name', label: 'Sort by Name' },
              { value: 'price', label: 'Sort by Price' },
              { value: 'duration', label: 'Sort by Duration' }
            ]
          }
        ]}
        resultsCount={filteredPlans.length}
        resultsType="plans"
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        showViewToggle={true}
      />

      {/* Loading State */}
      {loading && (
        <SkeletonLoader
          type="page"
          showStats={true}
          showTable={viewMode === "table"}
          showCards={viewMode === "grid"}
        />
      )}

      {/* Error State */}
      {error && (
        <ErrorStates.Retryable
          title="Failed to Load Plans"
          message={typeof error === 'string' ? error : 'We encountered an error while loading your plans. Please try again.'}
          onRetry={handleRetryFetch}
          retryText="Reload Plans"
          showSupport={true}
        />
      )}

      {/* Plans Content */}
      {!loading && !error && (
        <>
          {viewMode === "grid" ? (
            /* Grid View */
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {filteredPlans.length === 0 ? (
                <div className="col-span-full">
                  <ErrorStates.Empty
                    icon={FiCreditCard}
                    title={searchTerm ? "No plans match your search" : "No plans found"}
                    description={
                      searchTerm
                        ? "Try adjusting your search criteria or filters to find what you're looking for."
                        : "Get started by creating your first subscription plan for your platform."
                    }
                    action={
                      !searchTerm && (
                        <button
                          onClick={() => { setShowForm(true); setEditId(null); resetForm(); }}
                          className="btn bg-violet-500 hover:bg-violet-600 text-white min-h-[44px] px-6 py-3"
                        >
                          <FiPlus className="w-4 h-4 mr-2" />
                          Create Your First Plan
                        </button>
                      )
                    }
                  />
                </div>
              ) : (
                filteredPlans.map((plan) => (
                  <div
                    key={plan.id}
                    className="bg-white dark:bg-gray-800 rounded-xl shadow hover:shadow-md transition-all duration-200 border border-gray-200 dark:border-gray-700 touch-manipulation"
                  >
                    {/* Plan Header */}
                    <div className="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                            {plan.name}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                            {plan.description}
                          </p>
                        </div>
                        <div className="flex items-center gap-2 self-start">
                          {plan.is_active ? (
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                              Active
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                              <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                              Inactive
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Plan Details */}
                    <div className="p-4 sm:p-6">
                      <div className="grid grid-cols-2 gap-3 sm:gap-4 mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                            <FiDollarSign className="text-green-600 dark:text-green-400" size={16} />
                          </div>
                          <div className="min-w-0">
                            <p className="text-xs text-gray-500 dark:text-gray-400">Price</p>
                            <p className="text-sm sm:text-base font-semibold text-gray-900 dark:text-gray-100">${plan.price}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                            <FiCalendar className="text-blue-600 dark:text-blue-400" size={16} />
                          </div>
                          <div className="min-w-0">
                            <p className="text-xs text-gray-500 dark:text-gray-400">Duration</p>
                            <p className="text-sm sm:text-base font-semibold text-gray-900 dark:text-gray-100">{plan.duration_days} days</p>
                          </div>
                        </div>
                      </div>

                      <div className="mb-6">
                        <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">Features</p>
                        <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2 bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">{plan.features}</p>
                      </div>

                      {/* Actions */}
                      <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                        <button
                          onClick={() => handleEdit(plan)}
                          className="btn bg-blue-50 hover:bg-blue-100 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:hover:bg-blue-900/40 dark:text-blue-400 dark:border-blue-800 min-h-[44px] flex-1 sm:flex-none"
                        >
                          <FiEdit className="w-4 h-4 mr-2" />
                          Edit
                        </button>
                        <button
                          onClick={() => { setDeleteId(plan.id); setShowDeleteConfirm(true); }}
                          className="btn bg-red-50 hover:bg-red-100 text-red-700 border border-red-200 dark:bg-red-900/20 dark:hover:bg-red-900/40 dark:text-red-400 dark:border-red-800 min-h-[44px] flex-1 sm:flex-none"
                        >
                          <FiTrash2 className="w-4 h-4 mr-2" />
                          Delete
                        </button>
                        {plan.is_active ? (
                          <button
                            onClick={() => handleDeactivate(plan.id)}
                            className="btn bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border border-yellow-200 dark:bg-yellow-900/20 dark:hover:bg-yellow-900/40 dark:text-yellow-400 dark:border-yellow-800 min-h-[44px] flex-1 sm:flex-none"
                          >
                            <FiToggleLeft className="w-4 h-4 mr-2" />
                            Deactivate
                          </button>
                        ) : (
                          <button
                            onClick={() => handleActivate(plan.id)}
                            className="btn bg-green-50 hover:bg-green-100 text-green-700 border border-green-200 dark:bg-green-900/20 dark:hover:bg-green-900/40 dark:text-green-400 dark:border-green-800 min-h-[44px] flex-1 sm:flex-none"
                          >
                            <FiToggleRight className="w-4 h-4 mr-2" />
                            Activate
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          ) : (
            /* Table View */
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="border-b border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50">
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                        Plan Details
                      </th>
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                        Price
                      </th>
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                        Duration
                      </th>
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800">
                    {filteredPlans.length === 0 ? (
                      <tr>
                        <td colSpan={5} className="px-6 py-16">
                          <ErrorStates.Empty
                            icon={FiCreditCard}
                            title={searchTerm ? "No plans match your search" : "No plans found"}
                            description={
                              searchTerm
                                ? "Try adjusting your search criteria or filters to find what you're looking for."
                                : "Get started by creating your first subscription plan for your platform."
                            }
                            action={
                              !searchTerm && (
                                <button
                                  onClick={() => { setShowForm(true); setEditId(null); resetForm(); }}
                                  className="btn bg-violet-500 hover:bg-violet-600 text-white min-h-[44px] px-6 py-3"
                                >
                                  <FiPlus className="w-4 h-4 mr-2" />
                                  Create Your First Plan
                                </button>
                              )
                            }
                          />
                        </td>
                      </tr>
                    ) : (
                      filteredPlans.map((plan, index) => (
                        <tr key={plan.id} className={`hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors ${index !== filteredPlans.length - 1 ? 'border-b border-gray-100 dark:border-gray-700' : ''}`}>
                          <td className="px-6 py-5">
                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0 w-10 h-10 bg-violet-100 dark:bg-violet-900/30 rounded-lg flex items-center justify-center">
                                <FiCreditCard className="w-5 h-5 text-violet-600 dark:text-violet-400" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-1">
                                  {plan.name}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400 line-clamp-1 mb-1">
                                  {plan.description}
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-500 bg-gray-50 dark:bg-gray-700/50 px-2 py-1 rounded inline-block">
                                  {plan.features}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-5 whitespace-nowrap">
                            <div className="flex items-center space-x-2">
                              <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                                <FiDollarSign className="text-green-600 dark:text-green-400" size={16} />
                              </div>
                              <div>
                                <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                                  ${plan.price}
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  USD
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-5 whitespace-nowrap">
                            <div className="flex items-center space-x-2">
                              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                                <FiCalendar className="text-blue-600 dark:text-blue-400" size={16} />
                              </div>
                              <div>
                                <div className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                                  {plan.duration_days}
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  days
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-5 whitespace-nowrap">
                            {plan.is_active ? (
                              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800">
                                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                Active
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 border border-red-200 dark:border-red-800">
                                <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                                Inactive
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-5 whitespace-nowrap">
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => handleEdit(plan)}
                                className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                                title="Edit Plan"
                              >
                                <FiEdit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => { setDeleteId(plan.id); setShowDeleteConfirm(true); }}
                                className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                                title="Delete Plan"
                              >
                                <FiTrash2 className="w-4 h-4" />
                              </button>
                              {plan.is_active ? (
                                <button
                                  onClick={() => handleDeactivate(plan.id)}
                                  className="p-2 text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50 dark:text-yellow-400 dark:hover:text-yellow-300 dark:hover:bg-yellow-900/20 rounded-lg transition-colors"
                                  title="Deactivate Plan"
                                >
                                  <FiToggleLeft className="w-4 h-4" />
                                </button>
                              ) : (
                                <button
                                  onClick={() => handleActivate(plan.id)}
                                  className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 dark:text-green-400 dark:hover:text-green-300 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                                  title="Activate Plan"
                                >
                                  <FiToggleRight className="w-4 h-4" />
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      )}

      {/* Create/Edit Plan Modal */}
      {showForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setShowForm(false)}
          />
          <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-lg mx-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                {editId ? "Edit Plan" : "Create New Plan"}
              </h2>
              <button
                onClick={() => setShowForm(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <FiX size={24} />
              </button>
            </div>

            {/* Modal Body */}
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              <FormComponents.FormField
                label="Plan Name"
                required
                error={getFieldProps('name').error}
                hint="Choose a clear, descriptive name for your plan"
              >
                <FormComponents.TextInput
                  {...getFieldProps('name')}
                  placeholder="e.g., Premium Monthly, Basic Annual"
                  maxLength={50}
                  showCharCount
                />
              </FormComponents.FormField>

              <FormComponents.FormField
                label="Description"
                required
                error={getFieldProps('description').error}
                hint="Explain what this plan offers to potential subscribers"
              >
                <FormComponents.TextArea
                  {...getFieldProps('description')}
                  rows={3}
                  placeholder="Describe the key benefits and features included in this plan..."
                  maxLength={200}
                  showCharCount
                  autoResize
                />
              </FormComponents.FormField>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <FormComponents.FormField
                  label="Price"
                  required
                  error={getFieldProps('price').error}
                  hint="Set the subscription price in USD"
                >
                  <FormComponents.NumberInput
                    {...getFieldProps('price')}
                    min={0}
                    max={9999}
                    step={0.01}
                    prefix="$"
                    placeholder="0.00"
                    showControls={false}
                  />
                </FormComponents.FormField>

                <FormComponents.FormField
                  label="Duration"
                  required
                  error={getFieldProps('duration_days').error}
                  hint="How many days this plan lasts"
                >
                  <FormComponents.NumberInput
                    {...getFieldProps('duration_days')}
                    min={1}
                    max={365}
                    suffix="days"
                    placeholder="30"
                    showControls={true}
                  />
                </FormComponents.FormField>
              </div>

              <FormComponents.FormField
                label="Features"
                required
                error={getFieldProps('features').error}
                hint="Describe the key features and benefits included in this plan"
              >
                <FormComponents.TextArea
                  {...getFieldProps('features')}
                  rows={2}
                  placeholder="e.g., Unlimited access, Priority support, Advanced analytics..."
                  maxLength={500}
                  showCharCount
                />
              </FormComponents.FormField>

              <FormComponents.Checkbox
                checked={form.is_active}
                onChange={(e) => getFieldProps('is_active').onChange(e.target.checked)}
                label="Make this plan active immediately"
                description="Active plans will be available for users to subscribe to"
              />

              {/* Auto-save indicator */}
              {isDirty && (
                <AutoSaveIndicator
                  isDirty={isDirty}
                  position="inline"
                  className="mb-4"
                />
              )}

              {/* Modal Footer */}
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  className="btn bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 min-h-[44px]"
                  disabled={actionLoading.submit}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn bg-violet-500 hover:bg-violet-600 text-white min-h-[44px] disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={actionLoading.submit || formSubmitting}
                >
                  {actionLoading.submit || formSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      {editId ? "Updating..." : "Creating..."}
                    </>
                  ) : (
                    <>
                      <FiCheck className="w-4 h-4 mr-2" />
                      {editId ? "Update Plan" : "Create Plan"}
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setShowDeleteConfirm(false)}
          />
          <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-md mx-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                  <FiTrash2 className="w-5 h-5 text-red-600 dark:text-red-400" />
                </div>
                <h2 className="ml-3 text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Delete Plan
                </h2>
              </div>
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <FiX size={24} />
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-6">
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Are you sure you want to delete this plan? This action cannot be undone and will permanently remove the plan from your system.
              </p>
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                <p className="text-sm text-red-700 dark:text-red-400">
                  <strong>Warning:</strong> Any active subscriptions using this plan may be affected.
                </p>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="btn bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600"
                disabled={actionLoading.delete}
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="btn bg-red-600 hover:bg-red-700 text-white"
                disabled={actionLoading.delete}
              >
                {actionLoading.delete ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Deleting...
                  </>
                ) : (
                  <>
                    <FiTrash2 className="w-4 h-4 mr-2" />
                    Delete Plan
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
      </div>
    </ErrorBoundary>
  );
}

export default Plans;
