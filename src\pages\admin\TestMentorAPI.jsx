import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchMentorApplications,
  fetchInstituteMentors,
  selectApplications,
  selectApplicationsLoading,
  selectApplicationsError,
  selectMentors,
  selectMentorsLoading,
  selectMentorsError,
} from '../../store/slices/InstituteMentorsSlice';

function TestMentorAPI() {
  const dispatch = useDispatch();
  const [testResults, setTestResults] = useState({});

  // Redux selectors
  const applications = useSelector(selectApplications);
  const applicationsLoading = useSelector(selectApplicationsLoading);
  const applicationsError = useSelector(selectApplicationsError);
  
  const mentors = useSelector(selectMentors);
  const mentorsLoading = useSelector(selectMentorsLoading);
  const mentorsError = useSelector(selectMentorsError);

  // Test functions
  const testMentorApplicationsAPI = async () => {
    console.log('Testing Mentor Applications API...');
    try {
      const result = await dispatch(fetchMentorApplications({ skip: 0, limit: 10 }));
      console.log('Applications API Result:', result);
      setTestResults(prev => ({
        ...prev,
        applications: {
          success: true,
          data: result.payload,
          error: null
        }
      }));
    } catch (error) {
      console.error('Applications API Error:', error);
      setTestResults(prev => ({
        ...prev,
        applications: {
          success: false,
          data: null,
          error: error.message
        }
      }));
    }
  };

  const testMentorsAPI = async () => {
    console.log('Testing Mentors API...');
    try {
      const result = await dispatch(fetchInstituteMentors({ skip: 0, limit: 10 }));
      console.log('Mentors API Result:', result);
      setTestResults(prev => ({
        ...prev,
        mentors: {
          success: true,
          data: result.payload,
          error: null
        }
      }));
    } catch (error) {
      console.error('Mentors API Error:', error);
      setTestResults(prev => ({
        ...prev,
        mentors: {
          success: false,
          data: null,
          error: error.message
        }
      }));
    }
  };

  // Auto-test on component mount
  useEffect(() => {
    testMentorApplicationsAPI();
    testMentorsAPI();
  }, []);

  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Mentor API Test Page</h1>
        <p className="text-gray-600">Testing mentor-related API endpoints</p>
      </div>

      {/* Test Controls */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">API Tests</h2>
        <div className="flex space-x-4">
          <button
            onClick={testMentorApplicationsAPI}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Test Applications API
          </button>
          <button
            onClick={testMentorsAPI}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Test Mentors API
          </button>
        </div>
      </div>

      {/* Applications Test Results */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Mentor Applications API
          {applicationsLoading && <span className="text-blue-600 ml-2">(Loading...)</span>}
        </h3>
        
        {applicationsError && (
          <div className="bg-red-50 border border-red-200 rounded p-4 mb-4">
            <h4 className="text-red-800 font-medium">Error:</h4>
            <p className="text-red-600">{applicationsError}</p>
          </div>
        )}

        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-700">Redux State:</h4>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
              {JSON.stringify({
                data: applications.data,
                total: applications.total,
                loading: applicationsLoading,
                error: applicationsError
              }, null, 2)}
            </pre>
          </div>

          {testResults.applications && (
            <div>
              <h4 className="font-medium text-gray-700">Test Result:</h4>
              <div className={`p-3 rounded ${testResults.applications.success ? 'bg-green-50' : 'bg-red-50'}`}>
                <p className={`font-medium ${testResults.applications.success ? 'text-green-800' : 'text-red-800'}`}>
                  {testResults.applications.success ? 'SUCCESS' : 'FAILED'}
                </p>
                <pre className="text-sm mt-2 overflow-auto">
                  {JSON.stringify(testResults.applications, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Mentors Test Results */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Mentors API
          {mentorsLoading && <span className="text-blue-600 ml-2">(Loading...)</span>}
        </h3>
        
        {mentorsError && (
          <div className="bg-red-50 border border-red-200 rounded p-4 mb-4">
            <h4 className="text-red-800 font-medium">Error:</h4>
            <p className="text-red-600">{mentorsError}</p>
          </div>
        )}

        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-700">Redux State:</h4>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
              {JSON.stringify({
                data: mentors.data,
                total: mentors.total,
                loading: mentorsLoading,
                error: mentorsError
              }, null, 2)}
            </pre>
          </div>

          {testResults.mentors && (
            <div>
              <h4 className="font-medium text-gray-700">Test Result:</h4>
              <div className={`p-3 rounded ${testResults.mentors.success ? 'bg-green-50' : 'bg-red-50'}`}>
                <p className={`font-medium ${testResults.mentors.success ? 'text-green-800' : 'text-red-800'}`}>
                  {testResults.mentors.success ? 'SUCCESS' : 'FAILED'}
                </p>
                <pre className="text-sm mt-2 overflow-auto">
                  {JSON.stringify(testResults.mentors, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* API Endpoints Info */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">API Endpoints Being Tested</h3>
        <div className="space-y-2 text-sm">
          <div>
            <span className="font-medium">Applications:</span>
            <code className="ml-2 bg-gray-100 px-2 py-1 rounded">
              GET /api/institute/dashboard/mentors (for applications)
            </code>
          </div>
          <div>
            <span className="font-medium">Mentors:</span>
            <code className="ml-2 bg-gray-100 px-2 py-1 rounded">
              GET /api/institute/dashboard/mentors (for mentor data)
            </code>
          </div>
        </div>
      </div>

      {/* Debug Info */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Debug Information</h3>
        <div className="space-y-2 text-sm">
          <div>
            <span className="font-medium">Auth Token:</span>
            <span className="ml-2 text-gray-600">
              {localStorage.getItem('token') ? 'Present' : 'Missing'}
            </span>
          </div>
          <div>
            <span className="font-medium">API Base URL:</span>
            <span className="ml-2 text-gray-600">
              {import.meta.env.VITE_API_URL || 'https://edufair.duckdns.org'}
            </span>
          </div>
          <div>
            <span className="font-medium">Current User:</span>
            <span className="ml-2 text-gray-600">
              {JSON.stringify(JSON.parse(localStorage.getItem('user') || '{}'), null, 2)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TestMentorAPI;
