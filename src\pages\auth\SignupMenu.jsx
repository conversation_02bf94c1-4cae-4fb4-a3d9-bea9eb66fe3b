import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import API_BASE_URL from "../../utils/api/API_URL";

function SignupMenu() {
  const [selectedRole, setSelectedRole] = useState(null);
  const navigate = useNavigate();

  // Check if user is already authenticated on component mount
  useEffect(() => {
    const token = localStorage.getItem('token');
    const role = localStorage.getItem('role');
    
    if (token && role) {
      // Validate token before redirecting
      const validateToken = async () => {
        try {
          await axios.get(`${API_BASE_URL}/api/users/me`);
          
          // Token is valid, redirect to dashboard
          const rolePath = role.toLowerCase();
          navigate(`/${rolePath}/dashboard`);
        } catch (error) {
          // Token is invalid or network error, clear auth data
          localStorage.removeItem('token');
          localStorage.removeItem('role');
          localStorage.removeItem('userdata');
        }
      };

      validateToken();
    }
  }, [navigate]);

  const roles = [
    {
      key: "student",
      title: "Sign up as Student",
      description: "Access learning material and apply for services.",
    },
    {
      key: "teacher",
      title: "Sign up as Teacher",
      description: "Offer your expertise and share knowledge.",
    },
    {
      key: "Mentor",
      title: "Sign up as Mentor",
      description: "Offer your experience and knowledge.",
    },
    {
      key: "sponsor",
      title: "Sign up as Sponsor",
      description: "Support educational growth through sponsorships.",
    },
    {
      key: "institute",
      title: "Sign up as Institution",
      description: "Manage courses, teachers, and student programs.",
    },
  ];

  const handleContinue = () => {
    if (selectedRole) {
      navigate(`/signup/${selectedRole}`); // 👈 Route param added here
    }
  };

  return (
    <div className="min-h-screen flex flex-col justify-center items-center px-4 bg-white dark:bg-gray-900 pt-16 md:pt-0">
      <h1 className="text-3xl font-semibold mb-8 text-gray-800 dark:text-gray-100">Choose your role</h1>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 w-full max-w-3xl">
        {roles.map((role) => (
          <div
            key={role.key}
            onClick={() => setSelectedRole(role.key)}
            className={`border rounded-xl p-6 cursor-pointer text-center transition-all shadow-sm ${
              selectedRole === role.key
                ? "border-violet-500 bg-violet-50 dark:bg-violet-900/20 shadow-md"
                : "border-gray-200 dark:border-gray-700 hover:shadow-md bg-white dark:bg-gray-800"
            }`}
          >
            <h2 className="text-xl font-medium text-gray-800 dark:text-gray-100">{role.title}</h2>
            <p className="text-gray-500 dark:text-gray-400 mt-2">{role.description}</p>
          </div>
        ))}
      </div>

      <button
        onClick={handleContinue}
        disabled={!selectedRole}
        className={`mt-10 w-full max-w-xs py-2 rounded-md font-medium text-white transition ${
          selectedRole
            ? "bg-violet-600 hover:bg-violet-700 dark:bg-violet-500 dark:hover:bg-violet-600 cursor-pointer"
            : "bg-gray-300 dark:bg-gray-600 cursor-not-allowed"
        }`}
      >
        Continue
      </button>
    </div>
  );
}

export default SignupMenu;
