import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FiShield, FiArrowLeft, FiHome } from 'react-icons/fi';
import { getUserRole } from '../../utils/helpers/authHelpers';

const Unauthorized = () => {
  const navigate = useNavigate();
  const userRole = getUserRole();

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleGoHome = () => {
    if (userRole) {
      navigate(`/${userRole.toLowerCase()}/dashboard`);
    } else {
      navigate('/Login');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="rounded-full bg-red-100 p-3">
            <FiShield className="h-12 w-12 text-red-600" />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Access Denied
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          You don't have permission to access this page
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Insufficient Permissions
            </h3>
            <p className="text-sm text-gray-600 mb-6">
              {userRole ? (
                <>
                  Your current role (<span className="font-medium">{userRole}</span>) doesn't have 
                  access to this resource. Please contact your administrator if you believe this is an error.
                </>
              ) : (
                'You need to be logged in to access this page.'
              )}
            </p>

            <div className="space-y-3">
              <button
                onClick={handleGoBack}
                className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </button>
              
              <button
                onClick={handleGoHome}
                className="w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiHome className="h-4 w-4 mr-2" />
                {userRole ? 'Go to Dashboard' : 'Go to Login'}
              </button>
            </div>
          </div>

          {/* Role-specific guidance */}
          {userRole && (
            <div className="mt-6 p-4 bg-blue-50 rounded-md">
              <h4 className="text-sm font-medium text-blue-900 mb-2">
                What you can do as a {userRole}:
              </h4>
              <ul className="text-sm text-blue-800 space-y-1">
                {userRole === 'student' && (
                  <>
                    <li>• View and participate in competitions</li>
                    <li>• Access learning materials and classes</li>
                    <li>• Take exams and complete tasks</li>
                    <li>• Connect with mentors</li>
                  </>
                )}
                {userRole === 'teacher' && (
                  <>
                    <li>• View competitions (cannot create)</li>
                    <li>• Apply for mentorship roles</li>
                    <li>• Manage classes and students</li>
                    <li>• Create and grade exams</li>
                  </>
                )}
                {userRole === 'mentor' && (
                  <>
                    <li>• View competitions</li>
                    <li>• Apply for mentorship at institutions</li>
                    <li>• Check and score answers</li>
                    <li>• Manage student assignments</li>
                  </>
                )}
                {userRole === 'institute' && (
                  <>
                    <li>• Create and manage competitions</li>
                    <li>• Invite and manage mentors</li>
                    <li>• Oversee teachers and students</li>
                    <li>• Access analytics and reports</li>
                  </>
                )}
                {userRole === 'admin' && (
                  <>
                    <li>• Full system access</li>
                    <li>• Manage all users and content</li>
                    <li>• System configuration</li>
                    <li>• Analytics and monitoring</li>
                  </>
                )}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Unauthorized;
