import { useNavigate } from "react-router-dom";

export default function NotFound() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex items-center justify-center bg-white px-4">
      <div className="max-w-md text-center">
        <h1 className="text-9xl font-extrabold text-green-600 mb-4">404</h1>
        <h2 className="text-2xl md:text-3xl font-semibold text-black mb-2">
          Oops! Page not found.
        </h2>
        <p className="text-gray-600 mb-6">
          The page you're looking for doesn't exist or was moved.
        </p>
        <button
          onClick={() => navigate("/")}
          className="px-6 py-3 rounded-md bg-green-600 text-white font-medium hover:bg-green-700 transition"
        >
          Go to Homepage
        </button>
      </div>
    </div>
  );
}
