import { useState } from "react";
import { Menu, X } from "lucide-react";
import { useNavigate } from "react-router-dom";
import logo from "../../../assets/images/logo.png";
import ThemeToggle from "../../../components/common/ThemeToggle";

export default function Navbar() {
    const [isOpen, setIsOpen] = useState(false);
    const navigate = useNavigate();
    const toggleMenu = () => setIsOpen(!isOpen);

    return (
        <header className="fixed top-0 left-0 w-full z-50 backdrop-blur-md bg-white/80 dark:bg-gray-900/80 shadow-md">
            
            <div className="max-w-6xl mx-auto px-4 py-3 flex items-center justify-between">
                {/* Logo */}
                <a href="#hero">
                <div className="flex items-center cursor-pointer space-x-2"
                 onClick={() => {navigate("/")}}
                >
                    <img
                        src={logo}
                        alt="Logo"
                        className="h-12 w-12 object-contain"
                    />
                    <span className="text-xl font-bold">
                        <span style={{ color: '#3e3f49' }}>Edu</span>
                        <span style={{ color: '#755FF8' }}>Fair</span>
                    </span>
                </div>
                </a>
                {/* Desktop Navigation */}
                <nav className="hidden md:flex items-center space-x-6 text-sm font-medium">
                    <a href="#exams" className="text-gray-800 dark:text-gray-100 hover:text-violet-600 dark:hover:text-violet-400 transition-colors duration-200">
                        Exams
                    </a>
                    <a href="#certifications" className="text-gray-800 dark:text-gray-100 hover:text-violet-600 dark:hover:text-violet-400 transition-colors duration-200">
                        Certifications
                    </a>
                    <a href="#sponsors" className="text-gray-800 dark:text-gray-100 hover:text-violet-600 dark:hover:text-violet-400 transition-colors duration-200">
                        Sponsors
                    </a>                    
                    <a href="#faq" className="text-gray-800 dark:text-gray-100 hover:text-violet-600 dark:hover:text-violet-400 transition-colors duration-200">
                        FAQ
                    </a>
                </nav>

                {/* Desktop Actions */}
                <div className="hidden md:flex items-center space-x-3">
                    {/* Theme Toggle */}
                    <ThemeToggle />
                    
                    {/* Login / Signup */}
                    <button className="px-4 py-2 rounded-lg text-sm font-medium text-violet-600 dark:text-violet-400 border border-violet-600 dark:border-violet-400 hover:bg-violet-50 dark:hover:bg-violet-900/20 cursor-pointer transition-colors duration-200"
                        onClick={() => navigate("/Login")}>
                        Login
                    </button>
                    <button className="px-4 py-2 rounded-lg text-sm font-medium bg-violet-600 dark:bg-violet-500 text-white hover:bg-violet-700 dark:hover:bg-violet-600 cursor-pointer transition-colors duration-200"
                        onClick={() => navigate("/Signup-Menu")}>
                        Signup
                    </button>
                </div>

                {/* Mobile Menu Button */}
                <div className="md:hidden flex items-center space-x-3">
                    {/* Theme Toggle for Mobile */}
                    <ThemeToggle />
                    
                    <button onClick={toggleMenu} className="text-violet-600 dark:text-violet-400 cursor-pointer">
                        {isOpen ? <X size={28} /> : <Menu size={28} />}
                    </button>
                </div>
            </div>

            {/* Mobile Dropdown Menu */}
            {isOpen && (
                <div className="md:hidden bg-white/90 dark:bg-gray-900/90 backdrop-blur-md px-4 pb-4 pt-2 shadow-md rounded-b-lg">
                    <nav className="flex flex-col space-y-3 text-sm font-medium text-gray-800 dark:text-gray-100">
                        <a href="#sponsors" onClick={toggleMenu} className="hover:text-violet-600 dark:hover:text-violet-400 transition-colors duration-200">
                            Sponsors
                        </a>
                        <a href="#certifications" onClick={toggleMenu} className="hover:text-violet-600 dark:hover:text-violet-400 transition-colors duration-200">
                            Certifications
                        </a>
                        <a href="#exams" onClick={toggleMenu} className="hover:text-violet-600 dark:hover:text-violet-400 transition-colors duration-200">
                            Exams
                        </a>
                        <a href="#faq" onClick={toggleMenu} className="hover:text-violet-600 dark:hover:text-violet-400 transition-colors duration-200">
                            FAQ
                        </a>
                        <div className="flex space-x-2 pt-2">
                            <button className="flex-1 px-4 py-2 rounded-lg text-sm font-medium text-violet-600 dark:text-violet-400 border border-violet-600 dark:border-violet-400 hover:bg-violet-50 dark:hover:bg-violet-900/20 cursor-pointer transition-colors duration-200"
                                onClick={() => navigate("/Login")}
                            >
                                Login
                            </button>
                            <button className="flex-1 px-4 py-2 rounded-lg text-sm font-medium bg-violet-600 dark:bg-violet-500 text-white hover:bg-violet-700 dark:hover:bg-violet-600 cursor-pointer transition-colors duration-200"
                                onClick={() => navigate("/Signup-Menu")}
                            >
                                Signup
                            </button>
                        </div>
                    </nav>
                </div>
            )}
        </header>
    );
}
