import React, { useState } from 'react';
import template01 from '../../../assets/images/certifications/templates/template-01.png';
import template02 from '../../../assets/images/certifications/templates/template-02.png';
import template03 from '../../../assets/images/certifications/templates/template-03.png';

const certificates = [
  {
    title: 'Web Development',
    desc: 'Professional certificate in modern web development technologies.',
    issued: 'March 2024',
    img: template01,
  },
  {
    title: 'Data Science',
    desc: 'Advanced certification in data analysis and machine learning.',
    issued: 'February 2024',
    img: template02,
  },
  {
    title: 'Digital Marketing',
    desc: 'Comprehensive digital marketing strategy certification.',
    issued: 'January 2024',
    img: template03,
  },
];

function CertificatesShowcase() {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedCert, setSelectedCert] = useState(null);

  const openModal = (cert) => {
    setSelectedCert(cert);
    setModalOpen(true);
  };
  const closeModal = () => setModalOpen(false);

  return (
    <section id="certifications" className="relative py-16 bg-gradient-to-br from-violet-50 via-white to-sky-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 overflow-hidden">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-extrabold text-gray-800 dark:text-gray-100 mb-4 animate-fadeInUp">Certificates Showcase</h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto animate-fadeInUp" style={{animationDelay: '0.1s'}}>
            See examples of the professional certificates our students earn upon completion.
          </p>
          <div className="flex flex-wrap justify-center gap-6 mt-6 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
            <div className="bg-violet-100 dark:bg-violet-900/30 text-violet-700 dark:text-violet-300 px-6 py-2 rounded-full font-semibold text-sm shadow">
              10,000+ Certificates Issued
            </div>
            <div className="bg-sky-100 dark:bg-sky-900/30 text-sky-700 dark:text-sky-300 px-6 py-2 rounded-full font-semibold text-sm shadow">
              Trusted by 100+ Institutions
            </div>
          </div>
        </div>
        {/* Carousel/Slider */}
        <div className="flex flex-col md:flex-row gap-8 justify-center items-center animate-fadeInUp" style={{animationDelay: '0.3s'}}>
          {certificates.map((cert, idx) => (
            <div key={idx} className="bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:scale-105 hover:shadow-2xl transition-all duration-300 cursor-pointer max-w-xs w-full flex flex-col items-center" onClick={() => openModal(cert)}>
              <img src={cert.img} alt={cert.title} className="w-40 h-28 object-contain rounded-lg mb-4 shadow" />
              <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2">{cert.title}</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-2 text-center">{cert.desc}</p>
              <span className="text-xs text-gray-500 dark:text-gray-400">Issued: {cert.issued}</span>
              <button className="mt-4 bg-violet-600 hover:bg-violet-700 text-white px-4 py-2 rounded-lg font-semibold shadow transition-all duration-200" onClick={e => {e.stopPropagation(); openModal(cert);}}>
                View Sample Certificate
              </button>
            </div>
          ))}
        </div>
        {/* Modal */}
        {modalOpen && selectedCert && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeInUp">
            <div className="bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-2xl max-w-lg w-full relative">
              <button className="absolute top-2 right-2 text-gray-500 hover:text-violet-600 text-2xl font-bold" onClick={closeModal}>&times;</button>
              <h3 className="text-2xl font-bold mb-4 text-gray-800 dark:text-gray-100">{selectedCert.title} Certificate</h3>
              <img src={selectedCert.img} alt={selectedCert.title} className="w-full h-48 object-contain rounded-lg mb-4 shadow" />
              <p className="text-gray-600 dark:text-gray-400 mb-2">{selectedCert.desc}</p>
              <span className="text-xs text-gray-500 dark:text-gray-400">Issued: {selectedCert.issued}</span>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}

export default CertificatesShowcase;