import { useNavigate } from "react-router-dom";

export default function Cta() {

  const navigate = useNavigate();

  return (
    <section className="bg-white dark:bg-gray-900 text-gray-800 dark:text-white py-20 px-4 md:px-16">
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-4xl font-bold mb-6 text-violet-600 dark:text-violet-400">
          Ready to Get Certified?
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 mb-10">
          Whether you're a student, teacher, or institution — start your journey today. Join thousands of learners earning valuable certifications and leveling up.
        </p>

        <div className="flex flex-col sm:flex-row justify-center gap-6">
          <button className="bg-violet-500 hover:bg-violet-600 cursor-pointer text-white px-8 py-3 rounded-lg transition-colors duration-200 font-medium"
           onClick={() => {navigate("/Exams")}}
          >
            Explore Exams
          </button>
          <button className="bg-white dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer px-8 py-3 rounded-lg transition-colors duration-200 font-medium"
           onClick={() => {navigate("Signup-Menu")}}
          >
            Get Started Now
          </button>
        </div>
      </div>
    </section>
  );
}
