export default function Sponsorship() {
  return (
    <section id="sponsors" className="bg-gray-900 dark:bg-gray-950 text-white py-20 px-4 md:px-16">
      <div className="max-w-6xl mx-auto text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-violet-400">
          Proudly Sponsored By
        </h2>
        <p className="text-lg text-gray-300 mb-12 max-w-3xl mx-auto">
          Our mission is supported by organizations and companies that believe in making education accessible and credible through certification.
        </p>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-10 items-center justify-center">
          {[
            "https://upload.wikimedia.org/wikipedia/commons/a/ab/Microsoft_Logo.png",
            "https://upload.wikimedia.org/wikipedia/commons/4/44/Google-flutter-logo.svg",
            "https://upload.wikimedia.org/wikipedia/commons/4/4f/Cisco_logo_blue_2016.svg",
            "https://upload.wikimedia.org/wikipedia/commons/0/05/Oracle_logo.svg",
            "https://upload.wikimedia.org/wikipedia/commons/5/51/IBM_logo.svg",
            "https://upload.wikimedia.org/wikipedia/commons/a/a9/Amazon_logo.svg",
            "https://upload.wikimedia.org/wikipedia/commons/e/e0/Intel-logo.svg",
            "https://upload.wikimedia.org/wikipedia/commons/2/24/Linkedin_logo_initials.png",
          ].map((logo, idx) => (
            <div key={idx} className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700">
              <img
                src={logo}
                alt={`Sponsor ${idx + 1}`}
                className="h-12 object-contain mx-auto grayscale hover:grayscale-0 transition-all duration-300"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
