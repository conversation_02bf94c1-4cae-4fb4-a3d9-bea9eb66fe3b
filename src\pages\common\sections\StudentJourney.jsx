export default function StudentJourney() {
  const steps = [
    {
      title: "Register",
      desc: "Create your student account in seconds and choose your role.",
      icon: (
        <svg className="w-8 h-8 text-violet-500" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
        </svg>
      ),
      color: "bg-violet-100 dark:bg-violet-900/20"
    },
    {
      title: "Enroll",
      desc: "Browse available exams and enroll in the one that suits you.",
      icon: (
        <svg className="w-8 h-8 text-sky-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: "bg-sky-100 dark:bg-sky-900/20"
    },
    {
      title: "Take Test & Get Certified",
      desc: "Pass the AI-invigilated test to receive your official certificate.",
      icon: (
        <svg className="w-8 h-8 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
      ),
      color: "bg-yellow-100 dark:bg-yellow-900/20"
    },
  ];

  return (
    <section className="relative bg-gradient-to-br from-violet-50 via-white to-sky-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-20 px-4 md:px-16 overflow-hidden">
      <div className="max-w-6xl mx-auto text-center relative z-10">
        <h2 className="text-3xl md:text-4xl font-extrabold mb-6 text-gray-800 dark:text-gray-100 animate-fadeInUp">Your Journey to Certification</h2>
        <p className="text-lg text-gray-600 dark:text-gray-400 mb-12 max-w-3xl mx-auto animate-fadeInUp" style={{animationDelay: '0.1s'}}>
          We've made it easy for students to join, enroll, and get certified. Just follow these simple steps and start building your future.
        </p>
        {/* Horizontal Timeline/Stepper */}
        <div className="flex flex-col md:flex-row items-center justify-between gap-8 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
          {steps.map((step, idx) => (
            <div key={idx} className="flex flex-col items-center relative flex-1">
              <div className={`w-16 h-16 mb-4 ${step.color} rounded-full flex items-center justify-center shadow-lg z-10 animate-bounce`}>
                {step.icon}
              </div>
              <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 w-full max-w-xs">
                <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2">{step.title}</h3>
                <p className="text-gray-600 dark:text-gray-400">{step.desc}</p>
              </div>
              {/* Connector Line */}
              {idx < steps.length - 1 && (
                <div className="hidden md:block absolute top-8 right-0 w-full h-1 z-0">
                  <div className="h-1 bg-gradient-to-r from-violet-400 via-sky-400 to-yellow-400 w-full"></div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
