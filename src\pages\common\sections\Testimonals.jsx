import React, { useState } from 'react';

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    role: "Web Development Student",
    image: "https://randomuser.me/api/portraits/women/79.jpg",
    feedback:
      "The AI invigilation gave me confidence in the system. I loved how easy it was to learn and earn a certificate that helped me land my first internship!",
  },
  {
    name: "<PERSON>",
    role: "Data Science Enthusiast",
    image: "https://randomuser.me/api/portraits/men/32.jpg",
    feedback:
      "Their certification actually got noticed by my employer. The analytics, video lessons, and exam experience are top-notch.",
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    role: "University Student",
    image: "https://randomuser.me/api/portraits/women/65.jpg",
    feedback:
      "I really appreciate how institutions and students can both benefit from this platform. It's fair, credible, and well-designed.",
  },
];

export default function Testimonials() {
  const [current, setCurrent] = useState(0);
  const next = () => setCurrent((current + 1) % testimonials.length);
  const prev = () => setCurrent((current - 1 + testimonials.length) % testimonials.length);

  return (
    <section className="relative bg-gradient-to-br from-sky-50 via-white to-violet-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-20 px-4 md:px-16 overflow-hidden">
      <div className="max-w-6xl mx-auto text-center relative z-10">
        <h2 className="text-3xl md:text-4xl font-extrabold mb-6 text-gray-800 dark:text-gray-100 animate-fadeInUp">What Our Students Say</h2>
        <p className="text-lg text-gray-600 dark:text-gray-400 mb-12 max-w-3xl mx-auto animate-fadeInUp" style={{animationDelay: '0.1s'}}>
          Hear from students who have earned certificates, built confidence, and stepped into better opportunities through our platform.
        </p>
        {/* Carousel/Slider */}
        <div className="flex items-center justify-center gap-6 animate-fadeInUp" style={{animationDelay: '0.2s'}}>
          <button onClick={prev} className="bg-violet-100 dark:bg-violet-900/30 text-violet-700 dark:text-violet-300 rounded-full w-10 h-10 flex items-center justify-center shadow hover:bg-violet-200 dark:hover:bg-violet-800 transition-all duration-200">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" /></svg>
          </button>
          <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 text-left max-w-md w-full relative">
            <div className="flex items-center gap-4 mb-6">
              <img
                src={testimonials[current].image}
                alt={testimonials[current].name}
                className="w-16 h-16 rounded-full object-cover border-4 border-violet-200 dark:border-violet-700"
              />
              <div>
                <h4 className="font-semibold text-gray-800 dark:text-gray-100 text-lg">{testimonials[current].name}</h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">{testimonials[current].role}</p>
              </div>
            </div>
            <div className="flex items-center mb-4 animate-bounce">
              {[...Array(5)].map((_, i) => (
                <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
            <p className="text-gray-600 dark:text-gray-400 text-base leading-relaxed">"{testimonials[current].feedback}"</p>
          </div>
          <button onClick={next} className="bg-violet-100 dark:bg-violet-900/30 text-violet-700 dark:text-violet-300 rounded-full w-10 h-10 flex items-center justify-center shadow hover:bg-violet-200 dark:hover:bg-violet-800 transition-all duration-200">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" /></svg>
          </button>
        </div>
      </div>
    </section>
  );
}

