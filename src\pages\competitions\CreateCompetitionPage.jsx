import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiArrowLeft,
  FiAward,
  FiCalendar,
  FiUsers,
  FiDollarSign,
  FiClock,
  FiFileText
} from 'react-icons/fi';
import {
  createCompetitionFromExam,
  selectCreateLoading,
  selectCreateError,
  selectCreateSuccess
} from '../../store/slices/CompetitionsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const CreateCompetitionPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [selectedExam, setSelectedExam] = useState('');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    start_date: '',
    end_date: '',
    registration_deadline: '',
    max_participants: 100,
    prize_pool: '',
    difficulty_level: 'intermediate',
    category_id: '',
    rules: '',
    mentor_assignment_deadline: '',
    auto_assign_mentors: true,
    required_mentor_count: 3,
    mentor_expertise_areas: [''],
    scoring_criteria: {
      accuracy_weight: 70,
      speed_weight: 20,
      creativity_weight: 10
    }
  });

  // Mock exams data - in real app, this would come from Redux
  const [availableExams] = useState([
    { id: 1, title: 'Advanced Mathematics Quiz', subject: 'Mathematics', questions: 25 },
    { id: 2, title: 'Physics Fundamentals', subject: 'Physics', questions: 30 },
    { id: 3, title: 'Computer Science Basics', subject: 'Computer Science', questions: 20 },
    { id: 4, title: 'Chemistry Lab Test', subject: 'Chemistry', questions: 15 }
  ]);

  // Redux state
  const createLoading = useSelector(selectCreateLoading);
  const createError = useSelector(selectCreateError);
  const createSuccess = useSelector(selectCreateSuccess);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!selectedExam) {
      alert('Please select an exam to create competition from');
      return;
    }

    try {
      const queryParams = {
        title: formData.title,
        description: formData.description,
        start_date: formData.start_date,
        end_date: formData.end_date,
        registration_deadline: formData.registration_deadline,
        max_participants: parseInt(formData.max_participants),
        difficulty_level: formData.difficulty_level,
        auto_assign_mentors: formData.auto_assign_mentors,
        required_mentor_count: parseInt(formData.required_mentor_count),
        mentor_assignment_deadline: formData.mentor_assignment_deadline
      };

      // Add optional fields
      if (formData.prize_pool) queryParams.prize_pool = parseFloat(formData.prize_pool);
      if (formData.category_id) queryParams.category_id = formData.category_id;
      if (formData.rules) queryParams.rules = formData.rules;
      if (formData.mentor_expertise_areas.filter(area => area.trim()).length > 0) {
        queryParams.mentor_expertise_areas = formData.mentor_expertise_areas.filter(area => area.trim());
      }

      await dispatch(createCompetitionFromExam({
        examId: selectedExam,
        queryParams
      })).unwrap();

      navigate('/teacher/competitions');
    } catch (error) {
      console.error('Failed to create competition:', error);
    }
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : (type === 'number' ? parseFloat(value) : value)
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  // Handle mentor expertise areas
  const handleExpertiseAreaChange = (index, value) => {
    setFormData(prev => ({
      ...prev,
      mentor_expertise_areas: prev.mentor_expertise_areas.map((area, i) => 
        i === index ? value : area
      )
    }));
  };

  const addExpertiseArea = () => {
    setFormData(prev => ({
      ...prev,
      mentor_expertise_areas: [...prev.mentor_expertise_areas, '']
    }));
  };

  const removeExpertiseArea = (index) => {
    setFormData(prev => ({
      ...prev,
      mentor_expertise_areas: prev.mentor_expertise_areas.filter((_, i) => i !== index)
    }));
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate(-1)}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <FiArrowLeft className="h-4 w-4 mr-2" />
          Back to Competitions
        </button>
        <h1 className="text-3xl font-bold text-gray-900">Create Competition</h1>
        <p className="mt-2 text-gray-600">
          Create a new competition from an existing exam
        </p>
      </div>

      {/* Error Message */}
      {createError && (
        <div className="mb-6">
          <ErrorMessage message={createError} />
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Exam Selection */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Select Exam</h2>
          
          <div className="grid grid-cols-1 gap-4">
            {availableExams.map((exam) => (
              <label
                key={exam.id}
                className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedExam === exam.id.toString()
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <input
                  type="radio"
                  name="selectedExam"
                  value={exam.id}
                  checked={selectedExam === exam.id.toString()}
                  onChange={(e) => setSelectedExam(e.target.value)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <div className="ml-3 flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900">{exam.title}</h3>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {exam.subject}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">{exam.questions} questions</p>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Basic Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Competition Details</h2>
          
          <div className="grid grid-cols-1 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Competition Title *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter competition title..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                required
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Describe the competition..."
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Difficulty Level
                </label>
                <select
                  name="difficulty_level"
                  value={formData.difficulty_level}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                  <option value="expert">Expert</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Participants *
                </label>
                <div className="flex">
                  <FiUsers className="h-5 w-5 text-gray-400 mt-2 mr-3" />
                  <input
                    type="number"
                    name="max_participants"
                    value={formData.max_participants}
                    onChange={handleInputChange}
                    required
                    min="1"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Prize Pool (Optional)
              </label>
              <div className="flex">
                <FiDollarSign className="h-5 w-5 text-gray-400 mt-2 mr-3" />
                <input
                  type="number"
                  name="prize_pool"
                  value={formData.prize_pool}
                  onChange={handleInputChange}
                  min="0"
                  step="0.01"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Total prize amount..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Schedule */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Schedule</h2>
          
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date & Time *
              </label>
              <div className="flex">
                <FiCalendar className="h-5 w-5 text-gray-400 mt-2 mr-3" />
                <input
                  type="datetime-local"
                  name="start_date"
                  value={formData.start_date}
                  onChange={handleInputChange}
                  required
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Date & Time *
              </label>
              <input
                type="datetime-local"
                name="end_date"
                value={formData.end_date}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Registration Deadline *
              </label>
              <input
                type="datetime-local"
                name="registration_deadline"
                value={formData.registration_deadline}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Mentor Assignment */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Mentor Assignment</h2>
          
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                id="auto-assign-mentors"
                type="checkbox"
                name="auto_assign_mentors"
                checked={formData.auto_assign_mentors}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="auto-assign-mentors" className="ml-2 block text-sm text-gray-700">
                Automatically assign mentors for answer checking
              </label>
            </div>

            {formData.auto_assign_mentors && (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Required Mentor Count
                    </label>
                    <input
                      type="number"
                      name="required_mentor_count"
                      value={formData.required_mentor_count}
                      onChange={handleInputChange}
                      min="1"
                      max="10"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Mentor Assignment Deadline
                    </label>
                    <input
                      type="datetime-local"
                      name="mentor_assignment_deadline"
                      value={formData.mentor_assignment_deadline}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Required Mentor Expertise Areas
                  </label>
                  {formData.mentor_expertise_areas.map((area, index) => (
                    <div key={index} className="flex items-center space-x-2 mb-2">
                      <input
                        type="text"
                        value={area}
                        onChange={(e) => handleExpertiseAreaChange(index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Expertise area..."
                      />
                      {formData.mentor_expertise_areas.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeExpertiseArea(index)}
                          className="p-2 text-red-600 hover:text-red-800"
                        >
                          Remove
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={addExpertiseArea}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    + Add Expertise Area
                  </button>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Rules */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Competition Rules</h2>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Rules and Guidelines
            </label>
            <textarea
              name="rules"
              value={formData.rules}
              onChange={handleInputChange}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter competition rules and guidelines..."
            />
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate(-1)}
            className="px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={createLoading || !selectedExam}
            className="px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {createLoading ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" className="mr-2" />
                Creating...
              </div>
            ) : (
              'Create Competition'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateCompetitionPage;
