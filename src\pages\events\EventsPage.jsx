import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiSearch,
  FiFilter,
  FiCalendar,
  FiMapPin,
  FiAward,
  FiStar,
  FiPlus
} from 'react-icons/fi';
import {
  fetchPublicEvents,
  fetchFeaturedEvents,
  searchEvents,
  updateFilters,
  resetFilters,
  selectPublicEvents,
  selectPublicEventsLoading,
  selectFeaturedEvents,
  selectFeaturedEventsLoading,
  selectFilters
} from '../../store/slices/EventsSlice';
import EventCard from '../../components/events/EventCard';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const EventsPage = () => {
  const dispatch = useDispatch();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'featured', 'competitions'

  // Redux state
  const publicEvents = useSelector(selectPublicEvents);
  const publicEventsLoading = useSelector(selectPublicEventsLoading);
  const featuredEvents = useSelector(selectFeaturedEvents);
  const featuredEventsLoading = useSelector(selectFeaturedEventsLoading);
  const filters = useSelector(selectFilters);

  // Load initial data
  useEffect(() => {
    dispatch(fetchPublicEvents());
    dispatch(fetchFeaturedEvents({ limit: 6 }));
  }, [dispatch]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      dispatch(searchEvents({ q: searchQuery, limit: 20 }));
    } else {
      dispatch(fetchPublicEvents(filters));
    }
  };

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    dispatch(updateFilters(newFilters));
    dispatch(fetchPublicEvents(newFilters));
  };

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    let filterParams = { ...filters };
    
    switch (tab) {
      case 'featured':
        filterParams.is_featured = true;
        filterParams.is_competition = null;
        break;
      case 'competitions':
        filterParams.is_competition = true;
        filterParams.is_featured = null;
        break;
      default:
        filterParams.is_featured = null;
        filterParams.is_competition = null;
    }
    
    dispatch(updateFilters(filterParams));
    dispatch(fetchPublicEvents(filterParams));
  };

  // Clear filters
  const handleClearFilters = () => {
    setSearchQuery('');
    setActiveTab('all');
    dispatch(resetFilters());
    dispatch(fetchPublicEvents());
  };

  const handleViewEventDetails = (event) => {
    // Navigate to event details page
    console.log('View event details:', event);
  };

  const handleRegisterForEvent = (event) => {
    // Handle event registration
    console.log('Register for event:', event);
  };

  const getDisplayEvents = () => {
    switch (activeTab) {
      case 'featured':
        return featuredEvents;
      case 'competitions':
        return publicEvents.filter(event => event.is_competition);
      default:
        return publicEvents;
    }
  };

  const isLoading = publicEventsLoading || featuredEventsLoading;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Events</h1>
            <p className="mt-2 text-gray-600">
              Discover educational events, competitions, and workshops
            </p>
          </div>
          <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <FiPlus className="h-4 w-4 mr-2" />
            Create Event
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </form>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiFilter className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  value={filters.category_id || ''}
                  onChange={(e) => handleFilterChange('category_id', e.target.value || null)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  <option value="academic">Academic</option>
                  <option value="competition">Competition</option>
                  <option value="workshop">Workshop</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <select
                  value={filters.location_id || ''}
                  onChange={(e) => handleFilterChange('location_id', e.target.value || null)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Locations</option>
                  <option value="online">Online</option>
                  <option value="local">Local</option>
                </select>
              </div>

              <div className="flex items-end">
                <button
                  onClick={handleClearFilters}
                  className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'all', name: 'All Events', icon: FiCalendar },
              { id: 'featured', name: 'Featured', icon: FiStar },
              { id: 'competitions', name: 'Competitions', icon: FiAward }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Events Grid */}
      {isLoading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {getDisplayEvents().map((event) => (
            <EventCard
              key={event.id}
              event={event}
              onViewDetails={handleViewEventDetails}
              onRegister={handleRegisterForEvent}
              variant={activeTab === 'featured' ? 'featured' : 'default'}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && getDisplayEvents().length === 0 && (
        <div className="text-center py-12">
          <FiCalendar className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No events found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search or filters to find events.
          </p>
        </div>
      )}
    </div>
  );
};

export default EventsPage;
