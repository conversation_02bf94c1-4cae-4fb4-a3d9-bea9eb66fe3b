import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { 
  FiArrowLeft,
  FiCalendar,
  FiUsers,
  FiDollarSign,
  FiAward,
  FiClock,
  FiFileText
} from 'react-icons/fi';
import {
  createCompetitionFromExam,
  selectCreateLoading,
  selectCreateError,
  selectCreateSuccess
} from '../../store/slices/CompetitionsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import RoleGuard from '../../components/auth/RoleGuard';

const InstituteCreateCompetitionPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [selectedExam, setSelectedExam] = useState('');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    start_date: '',
    end_date: '',
    registration_deadline: '',
    max_participants: 100,
    prize_pool: '',
    difficulty_level: 'intermediate',
    category_id: '',
    rules: '',
    mentor_assignment_deadline: '',
    auto_assign_mentors: true,
    required_mentor_count: 3,
    mentor_expertise_areas: [''],
    scoring_criteria: {
      accuracy_weight: 70,
      speed_weight: 20,
      creativity_weight: 10
    }
  });

  // Mock exams data - in real app, this would come from Redux
  const [availableExams] = useState([
    { id: 1, title: 'Advanced Mathematics Quiz', subject: 'Mathematics', questions: 25 },
    { id: 2, title: 'Physics Fundamentals', subject: 'Physics', questions: 30 },
    { id: 3, title: 'Computer Science Basics', subject: 'Computer Science', questions: 20 },
    { id: 4, title: 'Chemistry Lab Test', subject: 'Chemistry', questions: 15 }
  ]);

  // Redux state
  const createLoading = useSelector(selectCreateLoading);
  const createError = useSelector(selectCreateError);
  const createSuccess = useSelector(selectCreateSuccess);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!selectedExam) {
      alert('Please select an exam to create competition from');
      return;
    }

    try {
      const queryParams = {
        title: formData.title,
        description: formData.description,
        start_date: formData.start_date,
        end_date: formData.end_date,
        registration_deadline: formData.registration_deadline,
        max_participants: parseInt(formData.max_participants),
        difficulty_level: formData.difficulty_level,
        auto_assign_mentors: formData.auto_assign_mentors,
        required_mentor_count: parseInt(formData.required_mentor_count),
        mentor_assignment_deadline: formData.mentor_assignment_deadline
      };

      // Add optional fields
      if (formData.prize_pool) queryParams.prize_pool = parseFloat(formData.prize_pool);
      if (formData.category_id) queryParams.category_id = formData.category_id;
      if (formData.rules) queryParams.rules = formData.rules;
      if (formData.mentor_expertise_areas.filter(area => area.trim()).length > 0) {
        queryParams.mentor_expertise_areas = formData.mentor_expertise_areas.filter(area => area.trim());
      }

      await dispatch(createCompetitionFromExam({
        examId: selectedExam,
        queryParams
      })).unwrap();

      navigate('/institute/competitions');
    } catch (error) {
      console.error('Failed to create competition:', error);
    }
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle mentor expertise areas
  const handleExpertiseAreaChange = (index, value) => {
    const newAreas = [...formData.mentor_expertise_areas];
    newAreas[index] = value;
    setFormData(prev => ({ ...prev, mentor_expertise_areas: newAreas }));
  };

  const addExpertiseArea = () => {
    setFormData(prev => ({
      ...prev,
      mentor_expertise_areas: [...prev.mentor_expertise_areas, '']
    }));
  };

  const removeExpertiseArea = (index) => {
    const newAreas = formData.mentor_expertise_areas.filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, mentor_expertise_areas: newAreas }));
  };

  // Redirect on success
  useEffect(() => {
    if (createSuccess) {
      navigate('/institute/competitions');
    }
  }, [createSuccess, navigate]);

  return (
    <RoleGuard allowedRoles={['institute']}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate(-1)}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <FiArrowLeft className="h-4 w-4 mr-1" />
          Back to Competitions
        </button>
        <h1 className="text-3xl font-bold text-gray-900">Create Competition</h1>
        <p className="mt-2 text-gray-600">
          Create a new competition from an existing exam for your institution
        </p>
      </div>

      {/* Error Message */}
      {createError && (
        <div className="mb-6">
          <ErrorMessage message={createError} />
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Exam Selection */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Select Base Exam</h3>
          <div className="grid grid-cols-1 gap-4">
            {availableExams.map((exam) => (
              <div
                key={exam.id}
                className={`relative rounded-lg border p-4 cursor-pointer transition-colors ${
                  selectedExam === exam.id.toString()
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onClick={() => setSelectedExam(exam.id.toString())}
              >
                <div className="flex items-center">
                  <input
                    type="radio"
                    name="selectedExam"
                    value={exam.id}
                    checked={selectedExam === exam.id.toString()}
                    onChange={(e) => setSelectedExam(e.target.value)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <div className="ml-3">
                    <label className="block text-sm font-medium text-gray-900">
                      {exam.title}
                    </label>
                    <p className="text-sm text-gray-500">
                      {exam.subject} • {exam.questions} questions
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Competition Details */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Competition Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Title */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">
                Competition Title *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            {/* Description */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Start Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Start Date & Time *
              </label>
              <input
                type="datetime-local"
                name="start_date"
                value={formData.start_date}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            {/* End Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                End Date & Time *
              </label>
              <input
                type="datetime-local"
                name="end_date"
                value={formData.end_date}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            {/* Registration Deadline */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Registration Deadline *
              </label>
              <input
                type="datetime-local"
                name="registration_deadline"
                value={formData.registration_deadline}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            {/* Max Participants */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Maximum Participants
              </label>
              <input
                type="number"
                name="max_participants"
                value={formData.max_participants}
                onChange={handleInputChange}
                min="1"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Prize Pool */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Prize Pool ($)
              </label>
              <input
                type="number"
                name="prize_pool"
                value={formData.prize_pool}
                onChange={handleInputChange}
                min="0"
                step="0.01"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Difficulty Level */}
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Difficulty Level
              </label>
              <select
                name="difficulty_level"
                value={formData.difficulty_level}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
                <option value="expert">Expert</option>
              </select>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate(-1)}
            className="px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={createLoading || !selectedExam}
            className="px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {createLoading ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" className="mr-2" />
                Creating...
              </div>
            ) : (
              'Create Competition'
            )}
          </button>
        </div>
      </form>
      </div>
    </RoleGuard>
  );
};

export default InstituteCreateCompetitionPage;
