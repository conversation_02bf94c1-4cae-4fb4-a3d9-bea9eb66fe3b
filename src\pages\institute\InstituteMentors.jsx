import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiUserPlus,
  FiCheck,
  FiX,
  FiEye,
  FiMail,
  FiUser,
  FiStar,
  FiClock,
  FiUsers,
  FiRefreshCw
} from 'react-icons/fi';
import MentorSearchDropdown from '../../components/ui/MentorSearchDropdown';
import {
  fetchMentorApplications,
  fetchInstituteMentors,
  approveMentorApplication,
  rejectMentorApplication,
  inviteMentor,
  selectApplications,
  selectApplicationsLoading,
  selectApplicationsError,
  selectMentors,
  selectMentorsLoading,
  selectMentorsError,
  selectApproveLoading,
  selectApproveSuccess,
  selectRejectLoading,
  selectRejectSuccess,
  selectInviteLoading,
  selectInviteSuccess,
  selectInviteError,
  clearErrors,
  clearSuccessStates
} from '../../store/slices/InstituteMentorsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const InstituteMentors = () => {
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState('applications'); // 'applications', 'invitations', 'mentors'
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [invitationData, setInvitationData] = useState({
    mentorId: '',
    invitationMessage: '',
    offeredHourlyRate: '',
    expectedHoursPerWeek: '',
    subjectsToCover: [],
    startDate: ''
  });
  const [responseData, setResponseData] = useState({
    responseMessage: '',
    finalHourlyRate: '',
    finalHoursPerWeek: '',
    startDate: ''
  });

  // Redux selectors
  const applications = useSelector(selectApplications);
  const applicationsLoading = useSelector(selectApplicationsLoading);
  const applicationsError = useSelector(selectApplicationsError);

  const mentors = useSelector(selectMentors);
  const mentorsLoading = useSelector(selectMentorsLoading);
  const mentorsError = useSelector(selectMentorsError);

  const approveLoading = useSelector(selectApproveLoading);
  const approveSuccess = useSelector(selectApproveSuccess);
  const rejectLoading = useSelector(selectRejectLoading);
  const rejectSuccess = useSelector(selectRejectSuccess);
  const inviteLoading = useSelector(selectInviteLoading);
  const inviteSuccess = useSelector(selectInviteSuccess);
  const inviteError = useSelector(selectInviteError);

  // Load data on component mount
  useEffect(() => {
    dispatch(fetchMentorApplications({ skip: 0, limit: 20 }));
    dispatch(fetchInstituteMentors({ skip: 0, limit: 20 }));
  }, [dispatch]);

  // Debug logging
  useEffect(() => {
    console.log('Applications data:', applications);
    console.log('Mentors data:', mentors);
    console.log('Applications error:', applicationsError);
    console.log('Mentors error:', mentorsError);
  }, [applications, mentors, applicationsError, mentorsError]);

  // Handle success states
  useEffect(() => {
    if (approveSuccess || rejectSuccess || inviteSuccess) {
      dispatch(clearSuccessStates());
      // Refresh data
      dispatch(fetchMentorApplications({ skip: 0, limit: 20 }));
      dispatch(fetchInstituteMentors({ skip: 0, limit: 20 }));
    }
  }, [approveSuccess, rejectSuccess, inviteSuccess, dispatch]);

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      dispatch(clearErrors());
    };
  }, [dispatch]);

  // Helper function to safely extract error messages
  const getErrorMessage = (error) => {
    if (!error) return null;
    if (typeof error === 'string') return error;
    if (typeof error === 'object') {
      return error.detail || error.message || 'An error occurred';
    }
    return 'An error occurred';
  };

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        dispatch(fetchMentorApplications({ skip: 0, limit: 20 })),
        dispatch(fetchInstituteMentors({ skip: 0, limit: 20 }))
      ]);
    } finally {
      setRefreshing(false);
    }
  };

  const handleInviteMentor = async () => {
    if (!invitationData.mentorId || !invitationData.invitationMessage) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      await dispatch(inviteMentor(invitationData)).unwrap();
      setShowInviteModal(false);
      setInvitationData({
        mentorId: '',
        invitationMessage: '',
        offeredHourlyRate: '',
        expectedHoursPerWeek: '',
        subjectsToCover: [],
        startDate: ''
      });
    } catch (error) {
      console.error('Failed to invite mentor:', error);
    }
  };

  const handleApproveApplication = async (application) => {
    const hourlyRate = prompt('Enter hourly rate (optional):') || application.proposedHourlyRate;
    const hoursPerWeek = prompt('Enter hours per week (optional):') || application.availabilityHours;

    try {
      await dispatch(approveMentorApplication({
        applicationId: application.id,
        responseData: {
          responseMessage: "Welcome to our institute!",
          finalHourlyRate: parseFloat(hourlyRate) || application.proposedHourlyRate,
          finalHoursPerWeek: parseInt(hoursPerWeek) || application.availabilityHours,
          startDate: new Date().toISOString()
        }
      })).unwrap();
    } catch (error) {
      console.error('Failed to approve application:', error);
    }
  };

  const handleRejectApplication = async (applicationId) => {
    const reason = prompt('Please provide a reason for rejection (optional):');
    try {
      await dispatch(rejectMentorApplication({
        applicationId,
        responseData: { reason: reason || "Thank you for your application." }
      })).unwrap();
    } catch (error) {
      console.error('Failed to reject application:', error);
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      accepted: { color: 'bg-green-100 text-green-800', label: 'Accepted' },
      rejected: { color: 'bg-red-100 text-red-800', label: 'Rejected' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const renderStars = (rating) => {
    const stars = [];
    const safeRating = rating || 0;
    const fullStars = Math.floor(safeRating);

    for (let i = 0; i < fullStars; i++) {
      stars.push(<FiStar key={i} className="h-4 w-4 text-yellow-400 fill-current" />);
    }

    const emptyStars = 5 - fullStars;
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<FiStar key={`empty-${i}`} className="h-4 w-4 text-gray-300" />);
    }

    return stars;
  };

  const getTabData = () => {
    switch (activeTab) {
      case 'applications':
        return applications.data || [];
      case 'invitations':
        return []; // Will be implemented when invitations API is available
      case 'mentors':
        return mentors.data || [];
      default:
        return [];
    }
  };

  const getTabLoading = () => {
    switch (activeTab) {
      case 'applications':
        return applicationsLoading;
      case 'invitations':
        return false;
      case 'mentors':
        return mentorsLoading;
      default:
        return false;
    }
  };

  const getTabError = () => {
    switch (activeTab) {
      case 'applications':
        return applicationsError;
      case 'invitations':
        return null;
      case 'mentors':
        return mentorsError;
      default:
        return null;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Mentor Management</h1>
            <p className="mt-2 text-gray-600">
              Manage mentor applications, invitations, and associations
            </p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <FiRefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <button
              onClick={() => setShowInviteModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FiUserPlus className="h-4 w-4 mr-2" />
              Invite Mentor
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiMail className="h-8 w-8 text-blue-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Applications</p>
              <p className="text-lg font-semibold text-gray-900">
                {applicationsLoading ? '...' : applications.data?.length || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiUserPlus className="h-8 w-8 text-purple-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Invitations</p>
              <p className="text-lg font-semibold text-gray-900">1</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiUsers className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Active Mentors</p>
              <p className="text-lg font-semibold text-gray-900">
                {mentorsLoading ? '...' : mentors.data?.filter(m => m.associationStatus === 'active').length || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiClock className="h-8 w-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-lg font-semibold text-gray-900">
                {(applications.data?.filter(a => a.status === 'pending').length || 0) + 1}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'applications', name: 'Applications', icon: FiMail },
              { id: 'invitations', name: 'Invitations', icon: FiUserPlus },
              { id: 'mentors', name: 'Active Mentors', icon: FiUsers }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 capitalize">{activeTab}</h2>
        </div>

        {/* Error State */}
        {getTabError() && (
          <div className="p-6">
            <ErrorMessage message={getTabError()} />
          </div>
        )}

        {/* Loading State */}
        {getTabLoading() ? (
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : getTabData().length === 0 ? (
          <div className="text-center py-12">
            <FiUser className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No {activeTab}</h3>
            <p className="mt-1 text-sm text-gray-500">
              {activeTab === 'applications' && 'No mentor applications received yet.'}
              {activeTab === 'invitations' && 'No mentor invitations sent yet.'}
              {activeTab === 'mentors' && 'No active mentors associated yet.'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {getTabData().map((item) => (
              <div key={item.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      {item.mentor?.profile_image_url || item.profile_image_url ? (
                        <img
                          src={item.mentor?.profile_image_url || item.profile_image_url}
                          alt={item.mentor?.full_name || item.full_name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                          <FiUser className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900">
                          {item.mentor?.full_name || item.full_name}
                        </h3>
                        {item.status && getStatusBadge(item.status)}
                      </div>
                      
                      <div className="flex items-center space-x-4 mb-2">
                        <div className="flex items-center">
                          {renderStars(item.mentor?.rating || item.rating || 0)}
                          <span className="ml-1 text-sm text-gray-600">
                            {(item.mentor?.rating || item.rating || 0).toFixed(1)} ({item.mentor?.total_reviews || item.total_reviews || 0} reviews)
                          </span>
                        </div>
                        <span className="text-sm text-gray-500">
                          {item.mentor?.experience_years || item.experience_years || 0} years exp.
                        </span>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 mb-3">
                        {(item.mentor?.expertise_areas || item.expertise_areas)?.map((area, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {area}
                          </span>
                        ))}
                      </div>
                      
                      {item.application_message && (
                        <div className="text-sm text-gray-600">
                          <p className="font-medium">Application Message:</p>
                          <p>{item.application_message}</p>
                        </div>
                      )}
                      
                      {item.invitation_message && (
                        <div className="text-sm text-gray-600">
                          <p className="font-medium">Invitation Message:</p>
                          <p>{item.invitation_message}</p>
                        </div>
                      )}
                      
                      {activeTab === 'mentors' && (
                        <div className="grid grid-cols-2 gap-4 mt-3 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Active Students:</span> {item.active_students}
                          </div>
                          <div>
                            <span className="font-medium">Completed Sessions:</span> {item.completed_sessions}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => console.log('View details:', item)}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <FiEye className="h-4 w-4 mr-2" />
                      View
                    </button>
                    
                    {item.status === 'pending' && activeTab === 'applications' && (
                      <>
                        <button
                          onClick={() => handleApproveApplication(item)}
                          disabled={approveLoading}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                        >
                          <FiCheck className="h-4 w-4 mr-2" />
                          {approveLoading ? 'Approving...' : 'Accept'}
                        </button>
                        <button
                          onClick={() => handleRejectApplication(item.id)}
                          disabled={rejectLoading}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                        >
                          <FiX className="h-4 w-4 mr-2" />
                          {rejectLoading ? 'Rejecting...' : 'Reject'}
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Invite Mentor Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Invite Mentor
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Mentor *
                  </label>
                  <MentorSearchDropdown
                    value={invitationData.mentorId}
                    onChange={(mentorId) => setInvitationData({ ...invitationData, mentorId })}
                    placeholder="Search and select a mentor to invite..."
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Invitation Message *
                  </label>
                  <textarea
                    value={invitationData.invitationMessage}
                    onChange={(e) => setInvitationData({ ...invitationData, invitationMessage: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Why would you like this mentor to join your institute?"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Offered Hourly Rate
                  </label>
                  <input
                    type="number"
                    value={invitationData.offeredHourlyRate}
                    onChange={(e) => setInvitationData({ ...invitationData, offeredHourlyRate: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., 50"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Expected Hours Per Week
                  </label>
                  <input
                    type="number"
                    value={invitationData.expectedHoursPerWeek}
                    onChange={(e) => setInvitationData({ ...invitationData, expectedHoursPerWeek: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., 10"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowInviteModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleInviteMentor}
                  disabled={inviteLoading}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {inviteLoading ? 'Sending...' : 'Send Invitation'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Messages */}
      {(applicationsError || mentorsError || inviteError) && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">
            {getErrorMessage(applicationsError || mentorsError || inviteError)}
          </span>
        </div>
      )}

      {/* Success Messages */}
      {(approveSuccess || rejectSuccess || inviteSuccess) && (
        <div className="fixed bottom-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50">
          <strong className="font-bold">Success! </strong>
          <span className="block sm:inline">
            {approveSuccess && 'Application approved successfully!'}
            {rejectSuccess && 'Application rejected successfully!'}
            {inviteSuccess && 'Mentor invitation sent successfully!'}
          </span>
        </div>
      )}
    </div>
  );
};

export default InstituteMentors;
