import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiFileText,
  FiCheck,
  FiX,
  FiEye,
  FiStar,
  FiClock,
  FiUser,
  FiAward,
  FiSave,
  FiCheckSquare
} from 'react-icons/fi';
import {
  fetchAnswersToCheck,
  submitAnswerScore,
  bulkScoreAnswers,
  selectAnswersToCheck,
  selectAnswersLoading,
  selectAnswersError,
  selectScoreSubmissionLoading,
  selectBulkScoreLoading
} from '../../store/slices/MentorsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const AnswerChecking = () => {
  const dispatch = useDispatch();
  const [selectedAnswers, setSelectedAnswers] = useState([]);
  const [scores, setScores] = useState({});
  const [feedback, setFeedback] = useState({});
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [bulkScoreData, setBulkScoreData] = useState({
    score: '',
    feedback: ''
  });
  const [filters, setFilters] = useState({
    competition_id: '',
    status: 'pending',
    difficulty: ''
  });

  // Redux state
  const answersToCheck = useSelector(selectAnswersToCheck);
  const loading = useSelector(selectAnswersLoading);
  const error = useSelector(selectAnswersError);
  const scoreSubmissionLoading = useSelector(selectScoreSubmissionLoading);
  const bulkScoreLoading = useSelector(selectBulkScoreLoading);

  // Load answers on mount
  useEffect(() => {
    dispatch(fetchAnswersToCheck(filters));
  }, [dispatch, filters]);

  const handleScoreChange = (answerId, score) => {
    setScores(prev => ({
      ...prev,
      [answerId]: score
    }));
  };

  const handleFeedbackChange = (answerId, feedbackText) => {
    setFeedback(prev => ({
      ...prev,
      [answerId]: feedbackText
    }));
  };

  const handleSubmitScore = async (answerId) => {
    const score = scores[answerId];
    const feedbackText = feedback[answerId];

    if (score === undefined || score === '') {
      alert('Please provide a score');
      return;
    }

    try {
      await dispatch(submitAnswerScore({
        answerId,
        mentor_score: parseFloat(score),
        mentor_feedback: feedbackText || ''
      })).unwrap();
      
      // Remove from local state
      setScores(prev => {
        const newScores = { ...prev };
        delete newScores[answerId];
        return newScores;
      });
      setFeedback(prev => {
        const newFeedback = { ...prev };
        delete newFeedback[answerId];
        return newFeedback;
      });
    } catch (error) {
      console.error('Failed to submit score:', error);
    }
  };

  const handleSelectAnswer = (answerId) => {
    setSelectedAnswers(prev => 
      prev.includes(answerId) 
        ? prev.filter(id => id !== answerId)
        : [...prev, answerId]
    );
  };

  const handleSelectAll = () => {
    if (selectedAnswers.length === answersToCheck.length) {
      setSelectedAnswers([]);
    } else {
      setSelectedAnswers(answersToCheck.map(answer => answer.id));
    }
  };

  const handleBulkScore = async () => {
    if (!bulkScoreData.score || selectedAnswers.length === 0) {
      alert('Please select answers and provide a score');
      return;
    }

    const scoresData = {
      scores: selectedAnswers.map(answerId => ({
        answer_id: answerId,
        mentor_score: parseFloat(bulkScoreData.score),
        mentor_feedback: bulkScoreData.feedback || ''
      }))
    };

    try {
      await dispatch(bulkScoreAnswers(scoresData)).unwrap();
      setShowBulkModal(false);
      setSelectedAnswers([]);
      setBulkScoreData({ score: '', feedback: '' });
    } catch (error) {
      console.error('Failed to submit bulk scores:', error);
    }
  };

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    dispatch(fetchAnswersToCheck(newFilters));
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Answer Checking</h1>
            <p className="mt-2 text-gray-600">
              Review and score competition answers
            </p>
          </div>
          {selectedAnswers.length > 0 && (
            <button 
              onClick={() => setShowBulkModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FiCheckSquare className="h-4 w-4 mr-2" />
              Bulk Score ({selectedAnswers.length})
            </button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiFileText className="h-8 w-8 text-blue-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total Answers</p>
              <p className="text-lg font-semibold text-gray-900">{answersToCheck.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiClock className="h-8 w-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-lg font-semibold text-gray-900">
                {answersToCheck.filter(a => a.mentor_score === null).length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiCheck className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Checked</p>
              <p className="text-lg font-semibold text-gray-900">
                {answersToCheck.filter(a => a.mentor_score !== null).length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiStar className="h-8 w-8 text-purple-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Avg Score</p>
              <p className="text-lg font-semibold text-gray-900">
                {answersToCheck.filter(a => a.mentor_score !== null).length > 0
                  ? Math.round(
                      answersToCheck
                        .filter(a => a.mentor_score !== null)
                        .reduce((sum, a) => sum + a.mentor_score, 0) /
                      answersToCheck.filter(a => a.mentor_score !== null).length
                    )
                  : 0}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Competition
            </label>
            <select
              value={filters.competition_id}
              onChange={(e) => handleFilterChange('competition_id', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Competitions</option>
              <option value="1">Math Competition 2024</option>
              <option value="2">Science Quiz</option>
              <option value="3">Programming Challenge</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="checked">Checked</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Difficulty
            </label>
            <select
              value={filters.difficulty}
              onChange={(e) => handleFilterChange('difficulty', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Difficulties</option>
              <option value="easy">Easy</option>
              <option value="medium">Medium</option>
              <option value="hard">Hard</option>
            </select>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6">
          <ErrorMessage message={error} />
        </div>
      )}

      {/* Answers List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      ) : (
        <div className="bg-white shadow-sm rounded-lg border border-gray-200">
          {/* Header with Select All */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Answers to Check</h2>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedAnswers.length === answersToCheck.length && answersToCheck.length > 0}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">Select All</span>
              </label>
            </div>
          </div>
          
          {answersToCheck.length === 0 ? (
            <div className="text-center py-12">
              <FiFileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No answers to check</h3>
              <p className="mt-1 text-sm text-gray-500">
                There are no answers available for checking at the moment.
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {answersToCheck.map((answer) => (
                <div key={answer.id} className="p-6">
                  <div className="flex items-start space-x-4">
                    {/* Checkbox */}
                    <input
                      type="checkbox"
                      checked={selectedAnswers.includes(answer.id)}
                      onChange={() => handleSelectAnswer(answer.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                    />
                    
                    {/* Answer Content */}
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-medium text-gray-900">
                              Question {answer.question_number}
                            </h3>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {answer.competition?.title}
                            </span>
                            {answer.mentor_score !== null && (
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 ${getScoreColor(answer.mentor_score)}`}>
                                Score: {answer.mentor_score}%
                              </span>
                            )}
                          </div>
                          
                          <div className="flex items-center text-sm text-gray-500 mb-3">
                            <FiUser className="h-4 w-4 mr-1" />
                            {answer.student?.full_name}
                            <FiAward className="h-4 w-4 ml-4 mr-1" />
                            {answer.competition?.title}
                            <FiClock className="h-4 w-4 ml-4 mr-1" />
                            {new Date(answer.submitted_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>

                      {/* Question */}
                      <div className="mb-4">
                        <h4 className="font-medium text-gray-900 mb-2">Question:</h4>
                        <p className="text-gray-700 bg-gray-50 p-3 rounded-md">{answer.question_text}</p>
                      </div>

                      {/* Student Answer */}
                      <div className="mb-4">
                        <h4 className="font-medium text-gray-900 mb-2">Student Answer:</h4>
                        <p className="text-gray-700 bg-blue-50 p-3 rounded-md">{answer.answer_text}</p>
                      </div>

                      {/* Correct Answer (if available) */}
                      {answer.correct_answer && (
                        <div className="mb-4">
                          <h4 className="font-medium text-gray-900 mb-2">Correct Answer:</h4>
                          <p className="text-gray-700 bg-green-50 p-3 rounded-md">{answer.correct_answer}</p>
                        </div>
                      )}

                      {/* Scoring Section */}
                      {answer.mentor_score === null ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 p-4 bg-gray-50 rounded-md">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Score (0-100) *
                            </label>
                            <input
                              type="number"
                              min="0"
                              max="100"
                              value={scores[answer.id] || ''}
                              onChange={(e) => handleScoreChange(answer.id, e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Enter score..."
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Feedback (Optional)
                            </label>
                            <textarea
                              value={feedback[answer.id] || ''}
                              onChange={(e) => handleFeedbackChange(answer.id, e.target.value)}
                              rows={2}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Provide feedback..."
                            />
                          </div>
                          
                          <div className="md:col-span-2">
                            <button
                              onClick={() => handleSubmitScore(answer.id)}
                              disabled={scoreSubmissionLoading}
                              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                            >
                              <FiSave className="h-4 w-4 mr-2" />
                              {scoreSubmissionLoading ? 'Submitting...' : 'Submit Score'}
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="mt-4 p-4 bg-green-50 rounded-md">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-green-800">
                                Score: {answer.mentor_score}%
                              </p>
                              {answer.mentor_feedback && (
                                <p className="text-sm text-green-700 mt-1">
                                  Feedback: {answer.mentor_feedback}
                                </p>
                              )}
                            </div>
                            <FiCheck className="h-5 w-5 text-green-600" />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Bulk Score Modal */}
      {showBulkModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Bulk Score Answers
              </h3>
              
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600 mb-2">
                    Scoring {selectedAnswers.length} selected answers
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Score (0-100) *
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={bulkScoreData.score}
                    onChange={(e) => setBulkScoreData({ ...bulkScoreData, score: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter score for all selected answers..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Feedback (Optional)
                  </label>
                  <textarea
                    value={bulkScoreData.feedback}
                    onChange={(e) => setBulkScoreData({ ...bulkScoreData, feedback: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Feedback for all selected answers..."
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowBulkModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleBulkScore}
                  disabled={bulkScoreLoading}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {bulkScoreLoading ? 'Submitting...' : 'Submit Bulk Scores'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnswerChecking;
