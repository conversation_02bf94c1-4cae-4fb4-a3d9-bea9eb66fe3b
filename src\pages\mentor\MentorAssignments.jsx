import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Fi<PERSON>lock, 
  FiDollarSign, 
  FiCheck, 
  FiX,
  FiEye,
  FiCalendar,
  FiUser,
  FiFileText
} from 'react-icons/fi';
import {
  fetchMentorAssignments,
  respondToAssignment,
  selectAssignments,
  selectAssignmentsLoading,
  selectAssignmentsError
} from '../../store/slices/MentorsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const MentorAssignments = () => {
  const dispatch = useDispatch();
  const [selectedAssignment, setSelectedAssignment] = useState(null);
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [responseData, setResponseData] = useState({
    response: '',
    response_notes: '',
    estimated_completion_time: ''
  });

  // Redux state
  const assignments = useSelector(selectAssignments);
  const loading = useSelector(selectAssignmentsLoading);
  const error = useSelector(selectAssignmentsError);

  // Load assignments on mount
  useEffect(() => {
    dispatch(fetchMentorAssignments());
  }, [dispatch]);

  const handleViewDetails = (assignment) => {
    setSelectedAssignment(assignment);
  };

  const handleRespond = (assignment, response) => {
    setSelectedAssignment(assignment);
    setResponseData({ ...responseData, response });
    setShowResponseModal(true);
  };

  const handleSubmitResponse = async () => {
    if (selectedAssignment && responseData.response) {
      try {
        await dispatch(respondToAssignment({
          assignmentId: selectedAssignment.id,
          responseData
        })).unwrap();
        setShowResponseModal(false);
        setSelectedAssignment(null);
        setResponseData({
          response: '',
          response_notes: '',
          estimated_completion_time: ''
        });
        // Refresh assignments
        dispatch(fetchMentorAssignments());
      } catch (error) {
        console.error('Failed to respond to assignment:', error);
      }
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      accepted: { color: 'bg-green-100 text-green-800', label: 'Accepted' },
      rejected: { color: 'bg-red-100 text-red-800', label: 'Rejected' },
      completed: { color: 'bg-blue-100 text-blue-800', label: 'Completed' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">My Assignments</h1>
        <p className="mt-2 text-gray-600">
          Manage your competition checking assignments
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiClock className="h-8 w-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-lg font-semibold text-gray-900">
                {assignments.filter(a => a.status === 'pending').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiCheck className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Accepted</p>
              <p className="text-lg font-semibold text-gray-900">
                {assignments.filter(a => a.status === 'accepted').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiFileText className="h-8 w-8 text-blue-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-lg font-semibold text-gray-900">
                {assignments.filter(a => a.status === 'completed').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiDollarSign className="h-8 w-8 text-purple-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total Earnings</p>
              <p className="text-lg font-semibold text-gray-900">
                ${assignments.reduce((sum, a) => sum + (a.hourly_rate * a.estimated_hours || 0), 0).toFixed(2)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Assignments List */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Assignments</h2>
        </div>
        
        {assignments.length === 0 ? (
          <div className="text-center py-12">
            <FiFileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No assignments</h3>
            <p className="mt-1 text-sm text-gray-500">
              You don't have any assignments yet.
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {assignments.map((assignment) => (
              <div key={assignment.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-medium text-gray-900">
                        {assignment.competition?.title}
                      </h3>
                      {getStatusBadge(assignment.status)}
                    </div>
                    
                    <p className="text-gray-600 mb-3">{assignment.assignment_notes}</p>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <FiCalendar className="h-4 w-4 mr-2" />
                        Assigned: {new Date(assignment.assigned_at).toLocaleDateString()}
                      </div>
                      <div className="flex items-center">
                        <FiClock className="h-4 w-4 mr-2" />
                        Est. Hours: {assignment.estimated_hours}
                      </div>
                      <div className="flex items-center">
                        <FiDollarSign className="h-4 w-4 mr-2" />
                        Rate: ${assignment.hourly_rate}/hr
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => handleViewDetails(assignment)}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <FiEye className="h-4 w-4 mr-2" />
                      View
                    </button>
                    
                    {assignment.status === 'pending' && (
                      <>
                        <button
                          onClick={() => handleRespond(assignment, 'accepted')}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                          <FiCheck className="h-4 w-4 mr-2" />
                          Accept
                        </button>
                        <button
                          onClick={() => handleRespond(assignment, 'rejected')}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          <FiX className="h-4 w-4 mr-2" />
                          Reject
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Response Modal */}
      {showResponseModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Respond to Assignment
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Response Notes
                  </label>
                  <textarea
                    value={responseData.response_notes}
                    onChange={(e) => setResponseData({ ...responseData, response_notes: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Add any notes about your response..."
                  />
                </div>
                
                {responseData.response === 'accepted' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Estimated Completion Time
                    </label>
                    <input
                      type="datetime-local"
                      value={responseData.estimated_completion_time}
                      onChange={(e) => setResponseData({ ...responseData, estimated_completion_time: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                )}
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowResponseModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmitResponse}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Submit Response
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MentorAssignments;
