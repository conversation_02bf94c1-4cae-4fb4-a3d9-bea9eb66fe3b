import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  <PERSON>Home,
  FiCheck,
  FiX,
  FiEye,
  FiCalendar,
  FiUser,
  FiMail,
  FiPlus,
  FiClock
} from 'react-icons/fi';
import {
  applyToInstitute,
  respondToAssociation,
  selectAssociations,
  selectAssociationsLoading,
  selectAssociationsError,
  selectApplyLoading,
  selectApplySuccess
} from '../../store/slices/MentorsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const MentorAssociations = () => {
  const dispatch = useDispatch();
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [selectedAssociation, setSelectedAssociation] = useState(null);
  const [applicationData, setApplicationData] = useState({
    institute_id: '',
    application_message: '',
    portfolio_highlights: '',
    availability_notes: ''
  });
  const [responseData, setResponseData] = useState({
    response: '',
    response_message: ''
  });

  // Mock associations data - in real app, this would come from Redux
  const [associations] = useState([
    {
      id: 1,
      institute: {
        id: 1,
        name: 'Tech University',
        logo_url: null,
        description: 'Leading technology university'
      },
      status: 'pending',
      type: 'application',
      created_at: '2024-01-15T10:00:00Z',
      application_message: 'I would like to join as a mentor for computer science students.',
      response_message: null
    },
    {
      id: 2,
      institute: {
        id: 2,
        name: 'Science Academy',
        logo_url: null,
        description: 'Premier science education institute'
      },
      status: 'accepted',
      type: 'invitation',
      created_at: '2024-01-10T14:30:00Z',
      application_message: 'We would like to invite you to mentor our physics students.',
      response_message: 'Thank you for the invitation. I accept!'
    }
  ]);

  // Mock institutes data
  const [availableInstitutes] = useState([
    { id: 1, name: 'Tech University', type: 'University' },
    { id: 2, name: 'Science Academy', type: 'Academy' },
    { id: 3, name: 'Math Institute', type: 'Institute' },
    { id: 4, name: 'Engineering College', type: 'College' }
  ]);

  // Redux state
  const associationsLoading = useSelector(selectAssociationsLoading);
  const associationsError = useSelector(selectAssociationsError);
  const applyLoading = useSelector(selectApplyLoading);
  const applySuccess = useSelector(selectApplySuccess);

  const handleApplyToInstitute = async () => {
    if (!applicationData.institute_id || !applicationData.application_message) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      await dispatch(applyToInstitute(applicationData)).unwrap();
      setShowApplicationModal(false);
      setApplicationData({
        institute_id: '',
        application_message: '',
        portfolio_highlights: '',
        availability_notes: ''
      });
    } catch (error) {
      console.error('Failed to apply to institute:', error);
    }
  };

  const handleRespondToAssociation = async () => {
    if (!responseData.response || !responseData.response_message) {
      alert('Please provide a response and message');
      return;
    }

    try {
      await dispatch(respondToAssociation({
        associationId: selectedAssociation.id,
        responseData
      })).unwrap();
      setShowResponseModal(false);
      setSelectedAssociation(null);
      setResponseData({
        response: '',
        response_message: ''
      });
    } catch (error) {
      console.error('Failed to respond to association:', error);
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      accepted: { color: 'bg-green-100 text-green-800', label: 'Accepted' },
      rejected: { color: 'bg-red-100 text-red-800', label: 'Rejected' },
      expired: { color: 'bg-gray-100 text-gray-800', label: 'Expired' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const getTypeBadge = (type) => {
    const typeConfig = {
      application: { color: 'bg-blue-100 text-blue-800', label: 'Application' },
      invitation: { color: 'bg-purple-100 text-purple-800', label: 'Invitation' }
    };

    const config = typeConfig[type] || typeConfig.application;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Institute Associations</h1>
            <p className="mt-2 text-gray-600">
              Manage your applications and invitations from educational institutes
            </p>
          </div>
          <button 
            onClick={() => setShowApplicationModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiPlus className="h-4 w-4 mr-2" />
            Apply to Institute
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiHome className="h-8 w-8 text-blue-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total</p>
              <p className="text-lg font-semibold text-gray-900">{associations.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiClock className="h-8 w-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-lg font-semibold text-gray-900">
                {associations.filter(a => a.status === 'pending').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiCheck className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Accepted</p>
              <p className="text-lg font-semibold text-gray-900">
                {associations.filter(a => a.status === 'accepted').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <FiMail className="h-8 w-8 text-purple-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Invitations</p>
              <p className="text-lg font-semibold text-gray-900">
                {associations.filter(a => a.type === 'invitation').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Associations List */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Associations</h2>
        </div>
        
        {associations.length === 0 ? (
          <div className="text-center py-12">
            <FiHome className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No associations</h3>
            <p className="mt-1 text-sm text-gray-500">
              You haven't applied to any institutes yet.
            </p>
            <div className="mt-6">
              <button
                onClick={() => setShowApplicationModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiPlus className="h-4 w-4 mr-2" />
                Apply to First Institute
              </button>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {associations.map((association) => (
              <div key={association.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      {association.institute.logo_url ? (
                        <img
                          src={association.institute.logo_url}
                          alt={association.institute.name}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                          <FiHome className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900">
                          {association.institute.name}
                        </h3>
                        {getStatusBadge(association.status)}
                        {getTypeBadge(association.type)}
                      </div>
                      
                      <p className="text-gray-600 mb-3">{association.institute.description}</p>
                      
                      <div className="text-sm text-gray-500">
                        <div className="flex items-center mb-1">
                          <FiCalendar className="h-4 w-4 mr-2" />
                          {association.type === 'application' ? 'Applied' : 'Invited'} on{' '}
                          {new Date(association.created_at).toLocaleDateString()}
                        </div>
                        
                        <div className="mt-2">
                          <p className="font-medium text-gray-700">
                            {association.type === 'application' ? 'Application Message:' : 'Invitation Message:'}
                          </p>
                          <p className="text-gray-600 mt-1">{association.application_message}</p>
                        </div>
                        
                        {association.response_message && (
                          <div className="mt-2">
                            <p className="font-medium text-gray-700">Response:</p>
                            <p className="text-gray-600 mt-1">{association.response_message}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => console.log('View details:', association)}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <FiEye className="h-4 w-4 mr-2" />
                      View
                    </button>
                    
                    {association.status === 'pending' && association.type === 'invitation' && (
                      <>
                        <button
                          onClick={() => {
                            setSelectedAssociation(association);
                            setResponseData({ ...responseData, response: 'accepted' });
                            setShowResponseModal(true);
                          }}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                          <FiCheck className="h-4 w-4 mr-2" />
                          Accept
                        </button>
                        <button
                          onClick={() => {
                            setSelectedAssociation(association);
                            setResponseData({ ...responseData, response: 'rejected' });
                            setShowResponseModal(true);
                          }}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          <FiX className="h-4 w-4 mr-2" />
                          Decline
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Application Modal */}
      {showApplicationModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Apply to Institute
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Institute *
                  </label>
                  <select
                    value={applicationData.institute_id}
                    onChange={(e) => setApplicationData({ ...applicationData, institute_id: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select institute</option>
                    {availableInstitutes.map((institute) => (
                      <option key={institute.id} value={institute.id}>
                        {institute.name} ({institute.type})
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Application Message *
                  </label>
                  <textarea
                    value={applicationData.application_message}
                    onChange={(e) => setApplicationData({ ...applicationData, application_message: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Why do you want to mentor at this institute?"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Portfolio Highlights
                  </label>
                  <textarea
                    value={applicationData.portfolio_highlights}
                    onChange={(e) => setApplicationData({ ...applicationData, portfolio_highlights: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Highlight your key achievements..."
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowApplicationModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleApplyToInstitute}
                  disabled={applyLoading}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {applyLoading ? 'Applying...' : 'Submit Application'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Response Modal */}
      {showResponseModal && selectedAssociation && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Respond to Invitation
              </h3>
              
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600 mb-2">
                    Responding to invitation from <strong>{selectedAssociation.institute.name}</strong>
                  </p>
                  <p className="text-sm font-medium text-gray-700">
                    Response: <span className={`capitalize ${responseData.response === 'accepted' ? 'text-green-600' : 'text-red-600'}`}>
                      {responseData.response}
                    </span>
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Response Message *
                  </label>
                  <textarea
                    value={responseData.response_message}
                    onChange={(e) => setResponseData({ ...responseData, response_message: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Your response message..."
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowResponseModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRespondToAssociation}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Submit Response
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MentorAssociations;
