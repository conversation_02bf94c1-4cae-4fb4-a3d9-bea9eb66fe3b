import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiTrendingUp,
  FiFileText,
  FiClock,
  FiStar,
  FiDollarSign,
  FiUsers,
  FiAward,
  FiCalendar,
  FiBarChart
} from 'react-icons/fi';
import {
  fetchMentorStatistics,
  selectStatistics,
  selectStatisticsLoading,
  selectStatisticsError
} from '../../store/slices/MentorsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const MentorStatistics = () => {
  const dispatch = useDispatch();
  const [timeRange, setTimeRange] = useState('month'); // 'week', 'month', 'quarter', 'year'
  const [selectedMetric, setSelectedMetric] = useState('answers_checked');

  // Redux state
  const statistics = useSelector(selectStatistics);
  const loading = useSelector(selectStatisticsLoading);
  const error = useSelector(selectStatisticsError);

  // Load statistics on mount and when time range changes
  useEffect(() => {
    dispatch(fetchMentorStatistics({ time_range: timeRange }));
  }, [dispatch, timeRange]);

  // Mock statistics data - in real app, this would come from Redux
  const mockStats = {
    overview: {
      total_answers_checked: 156,
      total_competitions: 8,
      total_earnings: 1240.50,
      average_score_given: 78.5,
      total_students_helped: 45,
      response_time_hours: 2.3
    },
    recent_activity: [
      {
        date: '2024-01-15',
        answers_checked: 12,
        competitions: 2,
        earnings: 95.00,
        avg_score: 82.1
      },
      {
        date: '2024-01-14',
        answers_checked: 8,
        competitions: 1,
        earnings: 60.00,
        avg_score: 75.5
      },
      {
        date: '2024-01-13',
        answers_checked: 15,
        competitions: 3,
        earnings: 112.50,
        avg_score: 79.8
      }
    ],
    competitions: [
      {
        id: 1,
        title: 'Math Competition 2024',
        answers_checked: 45,
        avg_score_given: 81.2,
        earnings: 337.50,
        completion_rate: 100,
        student_feedback: 4.8
      },
      {
        id: 2,
        title: 'Science Quiz Championship',
        answers_checked: 32,
        avg_score_given: 76.8,
        earnings: 240.00,
        completion_rate: 100,
        student_feedback: 4.6
      },
      {
        id: 3,
        title: 'Programming Challenge',
        answers_checked: 28,
        avg_score_given: 84.5,
        earnings: 210.00,
        completion_rate: 90,
        student_feedback: 4.9
      }
    ],
    performance_trends: {
      accuracy_trend: [85, 87, 89, 88, 91, 93, 90],
      speed_trend: [2.5, 2.3, 2.1, 2.4, 2.0, 1.8, 2.3],
      satisfaction_trend: [4.5, 4.6, 4.7, 4.8, 4.6, 4.9, 4.8]
    }
  };

  const stats = statistics || mockStats;

  const getTimeRangeLabel = (range) => {
    const labels = {
      week: 'This Week',
      month: 'This Month',
      quarter: 'This Quarter',
      year: 'This Year'
    };
    return labels[range] || 'This Month';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Mentor Statistics</h1>
            <p className="mt-2 text-gray-600">
              Track your performance and earnings as a mentor
            </p>
          </div>
          
          {/* Time Range Selector */}
          <div>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
              <option value="year">This Year</option>
            </select>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6">
          <ErrorMessage message={error} />
        </div>
      )}

      {loading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      ) : (
        <div className="space-y-8">
          {/* Overview Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center">
                <FiFileText className="h-8 w-8 text-blue-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">Answers Checked</p>
                  <p className="text-lg font-semibold text-gray-900">{stats.overview.total_answers_checked}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center">
                <FiAward className="h-8 w-8 text-purple-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">Competitions</p>
                  <p className="text-lg font-semibold text-gray-900">{stats.overview.total_competitions}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center">
                <FiDollarSign className="h-8 w-8 text-green-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                  <p className="text-lg font-semibold text-gray-900">{formatCurrency(stats.overview.total_earnings)}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center">
                <FiStar className="h-8 w-8 text-yellow-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">Avg Score Given</p>
                  <p className="text-lg font-semibold text-gray-900">{stats.overview.average_score_given}%</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center">
                <FiUsers className="h-8 w-8 text-indigo-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">Students Helped</p>
                  <p className="text-lg font-semibold text-gray-900">{stats.overview.total_students_helped}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center">
                <FiClock className="h-8 w-8 text-red-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">Avg Response</p>
                  <p className="text-lg font-semibold text-gray-900">{stats.overview.response_time_hours}h</p>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
            </div>
            <div className="p-6">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Answers Checked
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Competitions
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Earnings
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Avg Score
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {stats.recent_activity.map((activity, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(activity.date).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {activity.answers_checked}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {activity.competitions}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(activity.earnings)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {activity.avg_score}%
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Competition Performance */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Competition Performance</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 gap-6">
                {stats.competitions.map((competition) => (
                  <div key={competition.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">{competition.title}</h3>
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                          <span>{competition.answers_checked} answers checked</span>
                          <span>•</span>
                          <span>{competition.completion_rate}% completion rate</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-semibold text-green-600">{formatCurrency(competition.earnings)}</p>
                        <p className="text-sm text-gray-500">earned</p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                      <div className="text-center p-3 bg-blue-50 rounded-md">
                        <p className="text-2xl font-bold text-blue-600">{competition.avg_score_given}%</p>
                        <p className="text-sm text-blue-800">Avg Score Given</p>
                      </div>
                      <div className="text-center p-3 bg-yellow-50 rounded-md">
                        <p className="text-2xl font-bold text-yellow-600">{competition.student_feedback}</p>
                        <p className="text-sm text-yellow-800">Student Rating</p>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-md">
                        <p className="text-2xl font-bold text-green-600">{competition.completion_rate}%</p>
                        <p className="text-sm text-green-800">Completion Rate</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Performance Trends */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Accuracy Trend</h3>
                <FiTrendingUp className="h-5 w-5 text-green-500" />
              </div>
              <div className="space-y-2">
                <p className="text-3xl font-bold text-gray-900">90%</p>
                <p className="text-sm text-green-600">+2% from last period</p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '90%' }}></div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Response Speed</h3>
                <FiClock className="h-5 w-5 text-blue-500" />
              </div>
              <div className="space-y-2">
                <p className="text-3xl font-bold text-gray-900">2.3h</p>
                <p className="text-sm text-blue-600">-0.2h improvement</p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '77%' }}></div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Student Satisfaction</h3>
                <FiStar className="h-5 w-5 text-yellow-500" />
              </div>
              <div className="space-y-2">
                <p className="text-3xl font-bold text-gray-900">4.8</p>
                <p className="text-sm text-yellow-600">+0.1 from last period</p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '96%' }}></div>
                </div>
              </div>
            </div>
          </div>

          {/* Achievements */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Recent Achievements</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="flex items-center p-4 bg-yellow-50 rounded-lg">
                  <FiAward className="h-8 w-8 text-yellow-500 mr-3" />
                  <div>
                    <p className="font-medium text-yellow-800">Top Performer</p>
                    <p className="text-sm text-yellow-600">This month</p>
                  </div>
                </div>
                
                <div className="flex items-center p-4 bg-green-50 rounded-lg">
                  <FiStar className="h-8 w-8 text-green-500 mr-3" />
                  <div>
                    <p className="font-medium text-green-800">5-Star Rating</p>
                    <p className="text-sm text-green-600">Student feedback</p>
                  </div>
                </div>
                
                <div className="flex items-center p-4 bg-blue-50 rounded-lg">
                  <FiClock className="h-8 w-8 text-blue-500 mr-3" />
                  <div>
                    <p className="font-medium text-blue-800">Fast Responder</p>
                    <p className="text-sm text-blue-600">Under 2 hours</p>
                  </div>
                </div>
                
                <div className="flex items-center p-4 bg-purple-50 rounded-lg">
                  <FiUsers className="h-8 w-8 text-purple-500 mr-3" />
                  <div>
                    <p className="font-medium text-purple-800">Mentor of Choice</p>
                    <p className="text-sm text-purple-600">High demand</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MentorStatistics;
