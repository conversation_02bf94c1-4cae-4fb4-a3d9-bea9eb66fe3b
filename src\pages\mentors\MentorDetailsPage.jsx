import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { 
  FiArrowLeft,
  FiStar,
  FiMapPin,
  FiClock,
  FiDollarSign,
  FiAward,
  FiCheckCircle,
  FiMessageCircle,
  FiGlobe,
  FiExternalLink,
  FiCalendar,
  FiUser
} from 'react-icons/fi';
import {
  fetchMentorDetails,
  selectCurrentMentor,
  selectMentorDetailsLoading,
  selectMentorDetailsError
} from '../../store/slices/MentorsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const MentorDetailsPage = () => {
  const { mentorId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState('overview');
  const [showContactModal, setShowContactModal] = useState(false);

  // Redux state
  const mentor = useSelector(selectCurrentMentor);
  const loading = useSelector(selectMentorDetailsLoading);
  const error = useSelector(selectMentorDetailsError);

  // Load mentor details on mount
  useEffect(() => {
    if (mentorId) {
      dispatch(fetchMentorDetails(mentorId));
    }
  }, [dispatch, mentorId]);

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <FiStar key={i} className="h-4 w-4 text-yellow-400 fill-current" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <FiStar key="half" className="h-4 w-4 text-yellow-400 fill-current opacity-50" />
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <FiStar key={`empty-${i}`} className="h-4 w-4 text-gray-300" />
      );
    }

    return stars;
  };

  const handleContact = () => {
    setShowContactModal(true);
  };

  const handleBookSession = () => {
    // Navigate to booking page or open booking modal
    console.log('Book session with mentor:', mentor);
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  if (!mentor) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Mentor not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The mentor you're looking for doesn't exist or is no longer available.
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Back Button */}
      <button
        onClick={() => navigate(-1)}
        className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-6"
      >
        <FiArrowLeft className="h-4 w-4 mr-2" />
        Back to Mentors
      </button>

      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
        <div className="p-6">
          <div className="flex items-start space-x-6">
            {/* Profile Image */}
            <div className="relative">
              <img
                src={mentor.profile_image_url || '/default-avatar.png'}
                alt={mentor.full_name}
                className="w-24 h-24 rounded-full object-cover"
              />
              {mentor.is_verified && (
                <div className="absolute -top-1 -right-1">
                  <FiCheckCircle className="h-8 w-8 text-green-500 bg-white rounded-full" />
                </div>
              )}
            </div>

            {/* Basic Info */}
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">{mentor.full_name}</h1>
                  <p className="text-gray-600 mt-1">@{mentor.username}</p>
                </div>
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  mentor.is_available 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    mentor.is_available ? 'bg-green-400' : 'bg-gray-400'
                  }`} />
                  {mentor.is_available ? 'Available' : 'Busy'}
                </div>
              </div>

              {/* Rating */}
              <div className="flex items-center space-x-4 mt-3">
                <div className="flex items-center">
                  {renderStars(mentor.rating)}
                  <span className="ml-2 text-sm text-gray-600">
                    {mentor.rating.toFixed(1)} ({mentor.total_reviews} reviews)
                  </span>
                </div>
                {mentor.is_verified && (
                  <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <FiAward className="h-3 w-3 mr-1" />
                    Verified
                  </div>
                )}
              </div>

              {/* Quick Stats */}
              <div className="flex items-center space-x-6 mt-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <FiMapPin className="h-4 w-4 mr-1" />
                  {mentor.country}
                </div>
                <div className="flex items-center">
                  <FiClock className="h-4 w-4 mr-1" />
                  {mentor.experience_years} years exp.
                </div>
                <div className="flex items-center">
                  <FiDollarSign className="h-4 w-4 mr-1" />
                  ${mentor.hourly_rate}/hr
                </div>
                {mentor.languages && (
                  <div className="flex items-center">
                    <FiGlobe className="h-4 w-4 mr-1" />
                    {mentor.languages.join(', ')}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-3 mt-6">
                {mentor.is_available && (
                  <button
                    onClick={handleBookSession}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <FiCalendar className="h-4 w-4 mr-2" />
                    Book Session
                  </button>
                )}
                <button
                  onClick={handleContact}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <FiMessageCircle className="h-4 w-4 mr-2" />
                  Contact
                </button>
                {mentor.portfolio_url && (
                  <a
                    href={mentor.portfolio_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <FiExternalLink className="h-4 w-4 mr-2" />
                    Portfolio
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', name: 'Overview' },
              { id: 'expertise', name: 'Expertise' },
              { id: 'reviews', name: 'Reviews' },
              { id: 'availability', name: 'Availability' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {activeTab === 'overview' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">About</h2>
              <p className="text-gray-600 leading-relaxed">{mentor.bio}</p>
              
              {mentor.teaching_style && (
                <div className="mt-6">
                  <h3 className="text-md font-medium text-gray-900 mb-2">Teaching Style</h3>
                  <p className="text-gray-600">{mentor.teaching_style}</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'expertise' && (
            <div className="space-y-6">
              {/* Expertise Areas */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Expertise Areas</h2>
                <div className="flex flex-wrap gap-2">
                  {mentor.expertise_areas?.map((area, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                    >
                      {area}
                    </span>
                  ))}
                </div>
              </div>

              {/* Qualifications */}
              {mentor.qualifications && mentor.qualifications.length > 0 && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Qualifications</h2>
                  <ul className="space-y-2">
                    {mentor.qualifications.map((qualification, index) => (
                      <li key={index} className="flex items-center text-gray-600">
                        <FiAward className="h-4 w-4 mr-2 text-green-500" />
                        {qualification}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Specializations */}
              {mentor.specializations && mentor.specializations.length > 0 && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Specializations</h2>
                  <div className="flex flex-wrap gap-2">
                    {mentor.specializations.map((spec, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800"
                      >
                        {spec}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'reviews' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Reviews</h2>
              <div className="text-center py-8">
                <FiUser className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No reviews yet</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Be the first to leave a review for this mentor.
                </p>
              </div>
            </div>
          )}

          {activeTab === 'availability' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Availability</h2>
              {mentor.availability_hours ? (
                <div className="space-y-3">
                  {Object.entries(mentor.availability_hours).map(([day, hours]) => (
                    <div key={day} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                      <span className="font-medium text-gray-900 capitalize">{day}</span>
                      {hours.available ? (
                        <span className="text-green-600">
                          {hours.start} - {hours.end}
                        </span>
                      ) : (
                        <span className="text-gray-500">Not available</span>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">Availability information not provided.</p>
              )}
              <div className="mt-4 text-sm text-gray-500">
                <p>Timezone: {mentor.timezone || 'Not specified'}</p>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Response Rate</span>
                <span className="font-medium text-gray-900">95%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Response Time</span>
                <span className="font-medium text-gray-900">< 2 hours</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Sessions Completed</span>
                <span className="font-medium text-gray-900">150+</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Student Satisfaction</span>
                <span className="font-medium text-gray-900">98%</span>
              </div>
            </div>
          </div>

          {/* Contact Info */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact</h3>
            <div className="space-y-3">
              {mentor.linkedin_url && (
                <a
                  href={mentor.linkedin_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center text-blue-600 hover:text-blue-800"
                >
                  <FiExternalLink className="h-4 w-4 mr-2" />
                  LinkedIn Profile
                </a>
              )}
              {mentor.portfolio_url && (
                <a
                  href={mentor.portfolio_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center text-blue-600 hover:text-blue-800"
                >
                  <FiExternalLink className="h-4 w-4 mr-2" />
                  Portfolio
                </a>
              )}
            </div>
          </div>

          {/* Similar Mentors */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Similar Mentors</h3>
            <div className="text-center py-4">
              <p className="text-sm text-gray-500">
                Discover other mentors with similar expertise
              </p>
              <button className="mt-2 text-sm text-blue-600 hover:text-blue-800">
                View Similar Mentors
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Modal */}
      {showContactModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Contact {mentor.full_name}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subject
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="What would you like to discuss?"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Message
                  </label>
                  <textarea
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Tell the mentor about your goals and what you're looking for..."
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowContactModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setShowContactModal(false)}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Send Message
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MentorDetailsPage;
