import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { 
  FiArrowLeft,
  FiUser,
  FiGlobe,
  FiDollarSign,
  FiClock,
  FiAward,
  FiFileText,
  FiPlus,
  FiX
} from 'react-icons/fi';
import {
  registerAsMentor,
  selectRegistrationLoading,
  selectRegistrationError,
  selectRegistrationSuccess
} from '../../store/slices/MentorsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';

const MentorRegistrationPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    bio: '',
    expertise_areas: [''],
    experience_years: 1,
    hourly_rate: 25,
    preferred_subjects: [''],
    languages: ['English'],
    qualifications: [''],
    portfolio_url: '',
    linkedin_url: '',
    availability_hours: {
      monday: { start: '09:00', end: '17:00', available: true },
      tuesday: { start: '09:00', end: '17:00', available: true },
      wednesday: { start: '09:00', end: '17:00', available: true },
      thursday: { start: '09:00', end: '17:00', available: true },
      friday: { start: '09:00', end: '17:00', available: true },
      saturday: { start: '09:00', end: '17:00', available: false },
      sunday: { start: '09:00', end: '17:00', available: false }
    },
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    country: '',
    teaching_style: '',
    specializations: ['']
  });

  // Redux state
  const registrationLoading = useSelector(selectRegistrationLoading);
  const registrationError = useSelector(selectRegistrationError);
  const registrationSuccess = useSelector(selectRegistrationSuccess);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      // Clean up the data before submission
      const mentorData = {
        ...formData,
        experience_years: parseInt(formData.experience_years),
        hourly_rate: parseFloat(formData.hourly_rate),
        expertise_areas: formData.expertise_areas.filter(area => area.trim()),
        preferred_subjects: formData.preferred_subjects.filter(subject => subject.trim()),
        languages: formData.languages.filter(lang => lang.trim()),
        qualifications: formData.qualifications.filter(qual => qual.trim()),
        specializations: formData.specializations.filter(spec => spec.trim())
      };

      // Remove empty fields
      if (!mentorData.portfolio_url) delete mentorData.portfolio_url;
      if (!mentorData.linkedin_url) delete mentorData.linkedin_url;
      if (!mentorData.teaching_style) delete mentorData.teaching_style;

      await dispatch(registerAsMentor(mentorData)).unwrap();
      navigate('/mentor/dashboard');
    } catch (error) {
      console.error('Failed to register as mentor:', error);
    }
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle array field changes
  const handleArrayFieldChange = (field, index, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  // Add array field
  const addArrayField = (field) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  // Remove array field
  const removeArrayField = (field, index) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  // Handle availability changes
  const handleAvailabilityChange = (day, field, value) => {
    setFormData(prev => ({
      ...prev,
      availability_hours: {
        ...prev.availability_hours,
        [day]: {
          ...prev.availability_hours[day],
          [field]: value
        }
      }
    }));
  };

  const expertiseOptions = [
    'Mathematics', 'Physics', 'Computer Science', 'Chemistry', 'Biology',
    'English', 'History', 'Economics', 'Psychology', 'Engineering',
    'Art', 'Music', 'Philosophy', 'Statistics', 'Data Science'
  ];

  const languageOptions = [
    'English', 'Spanish', 'French', 'German', 'Chinese', 'Japanese',
    'Arabic', 'Portuguese', 'Russian', 'Italian', 'Dutch', 'Korean'
  ];

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate(-1)}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <FiArrowLeft className="h-4 w-4 mr-2" />
          Back
        </button>
        <h1 className="text-3xl font-bold text-gray-900">Become a Mentor</h1>
        <p className="mt-2 text-gray-600">
          Join our community of educators and help students achieve their goals
        </p>
      </div>

      {/* Error Message */}
      {registrationError && (
        <div className="mb-6">
          <ErrorMessage message={registrationError} />
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Personal Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h2>
          
          <div className="grid grid-cols-1 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Bio *
              </label>
              <textarea
                name="bio"
                value={formData.bio}
                onChange={handleInputChange}
                required
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Tell us about yourself, your background, and teaching philosophy..."
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Experience Years *
                </label>
                <div className="flex">
                  <FiClock className="h-5 w-5 text-gray-400 mt-2 mr-3" />
                  <input
                    type="number"
                    name="experience_years"
                    value={formData.experience_years}
                    onChange={handleInputChange}
                    required
                    min="0"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Hourly Rate (USD) *
                </label>
                <div className="flex">
                  <FiDollarSign className="h-5 w-5 text-gray-400 mt-2 mr-3" />
                  <input
                    type="number"
                    name="hourly_rate"
                    value={formData.hourly_rate}
                    onChange={handleInputChange}
                    required
                    min="1"
                    step="0.01"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Country *
              </label>
              <div className="flex">
                <FiGlobe className="h-5 w-5 text-gray-400 mt-2 mr-3" />
                <input
                  type="text"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  required
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Your country..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Expertise & Skills */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Expertise & Skills</h2>
          
          <div className="space-y-6">
            {/* Expertise Areas */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Expertise Areas *
              </label>
              {formData.expertise_areas.map((area, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <select
                    value={area}
                    onChange={(e) => handleArrayFieldChange('expertise_areas', index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select expertise area</option>
                    {expertiseOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  {formData.expertise_areas.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeArrayField('expertise_areas', index)}
                      className="p-2 text-red-600 hover:text-red-800"
                    >
                      <FiX className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={() => addArrayField('expertise_areas')}
                className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
              >
                <FiPlus className="h-4 w-4 mr-1" />
                Add Expertise Area
              </button>
            </div>

            {/* Languages */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Languages *
              </label>
              {formData.languages.map((language, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <select
                    value={language}
                    onChange={(e) => handleArrayFieldChange('languages', index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select language</option>
                    {languageOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  {formData.languages.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeArrayField('languages', index)}
                      className="p-2 text-red-600 hover:text-red-800"
                    >
                      <FiX className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={() => addArrayField('languages')}
                className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
              >
                <FiPlus className="h-4 w-4 mr-1" />
                Add Language
              </button>
            </div>

            {/* Qualifications */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Qualifications
              </label>
              {formData.qualifications.map((qualification, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={qualification}
                    onChange={(e) => handleArrayFieldChange('qualifications', index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Degree, certification, or qualification..."
                  />
                  {formData.qualifications.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeArrayField('qualifications', index)}
                      className="p-2 text-red-600 hover:text-red-800"
                    >
                      <FiX className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={() => addArrayField('qualifications')}
                className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
              >
                <FiPlus className="h-4 w-4 mr-1" />
                Add Qualification
              </button>
            </div>
          </div>
        </div>

        {/* Professional Links */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Professional Links</h2>
          
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Portfolio URL
              </label>
              <input
                type="url"
                name="portfolio_url"
                value={formData.portfolio_url}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="https://your-portfolio.com"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                LinkedIn URL
              </label>
              <input
                type="url"
                name="linkedin_url"
                value={formData.linkedin_url}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="https://linkedin.com/in/yourprofile"
              />
            </div>
          </div>
        </div>

        {/* Availability */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Availability</h2>
          
          <div className="space-y-4">
            {Object.entries(formData.availability_hours).map(([day, hours]) => (
              <div key={day} className="flex items-center space-x-4">
                <div className="w-20">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={hours.available}
                      onChange={(e) => handleAvailabilityChange(day, 'available', e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700 capitalize">{day}</span>
                  </label>
                </div>
                
                {hours.available && (
                  <>
                    <div>
                      <input
                        type="time"
                        value={hours.start}
                        onChange={(e) => handleAvailabilityChange(day, 'start', e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <span className="text-gray-500">to</span>
                    <div>
                      <input
                        type="time"
                        value={hours.end}
                        onChange={(e) => handleAvailabilityChange(day, 'end', e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate(-1)}
            className="px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={registrationLoading}
            className="px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {registrationLoading ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" className="mr-2" />
                Registering...
              </div>
            ) : (
              'Register as Mentor'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default MentorRegistrationPage;
