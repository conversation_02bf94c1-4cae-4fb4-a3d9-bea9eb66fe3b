import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useThemeProvider } from '../../providers/ThemeContext';
import { 
  FiCheckCircle, 
  FiClock, 
  FiUser, 
  FiCalendar, 
  FiArrowRight, 
  FiLoader,
  FiAlertCircle,
  FiRefreshCw
} from 'react-icons/fi';
import {
  checkExamWithAI,
  waitForAIResults,
  resetChecking,
  selectAIChecking,
  selectCheckingStatus,
  selectProgress
} from '../../store/slices/exam/aiCheckingSlice';
import SubmissionStatusTracker from '../../components/exam/student/SubmissionStatusTracker';

function ExamSubmissionSuccess() {
  const { examId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();

  // Redux state
  const aiChecking = useSelector(selectAIChecking);
  const checkingStatus = useSelector(selectCheckingStatus);
  const progress = useSelector(selectProgress);

  // Local state
  const [retryCount, setRetryCount] = useState(0);

  // Get submission data from navigation state
  const submissionData = location.state || {};
  const {
    examTitle = 'Exam',
    submissionTime = new Date().toISOString(),
    submissionResult = {},
    autoSubmitted = false,
    answers = {},
    autoAITriggered = false // Flag indicating AI was auto-triggered during submission
  } = submissionData;

  // Theme classes
  const themeBg = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const themeText = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  // Format submission time
  const formatSubmissionTime = (timeString) => {
    try {
      const date = new Date(timeString);
      return date.toLocaleString();
    } catch (error) {
      return 'Just now';
    }
  };

  // Get attempt ID from submission result (for reference)
  const attemptId = submissionResult?.attempt_id || submissionResult?.id;

  // Start AI checking process (using simplified API)
  const startAIChecking = async () => {
    if (!examId) {
      console.warn('No exam ID available for AI checking');
      return;
    }

    try {
      console.log('🤖 Starting AI checking for exam:', examId);

      // For students, we don't need to trigger AI checking - just wait for results
      // The AI checking should be triggered automatically after submission
      // We just need to poll for results
      await dispatch(waitForAIResults({
        examId,
        studentId: null, // null for student endpoint
        maxWaitTime: 90000,
        pollInterval: 3000
      })).unwrap();

      console.log('✅ AI checking completed successfully');

    } catch (error) {
      console.error('❌ AI checking failed:', error);
    }
  };

  // Retry AI checking
  const retryAIChecking = () => {
    setRetryCount(prev => prev + 1);
    dispatch(resetChecking());
    startAIChecking();
  };

  // Start AI checking on component mount
  useEffect(() => {
    if (examId && checkingStatus === 'idle') {
      if (autoAITriggered) {
        // AI was already triggered during submission, just wait for results
        console.log('🤖 AI checking was auto-triggered during submission, waiting for results...');
        const timer = setTimeout(() => {
          // Start waiting for results without triggering again
          dispatch(waitForAIResults({
            examId,
            studentId: null,
            maxWaitTime: 90000,
            pollInterval: 3000
          }));
        }, 1000);
        return () => clearTimeout(timer);
      } else {
        // AI was not triggered, start it manually
        console.log('🤖 AI checking was not auto-triggered, starting manually...');
        const timer = setTimeout(() => {
          startAIChecking();
        }, 2000);
        return () => clearTimeout(timer);
      }
    }
  }, [examId, checkingStatus, autoAITriggered, dispatch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      dispatch(resetChecking());
    };
  }, [dispatch]);

  // Navigate to results
  const handleViewResults = () => {
    const aiResults = aiChecking.results[examId];
    navigate(`/student/exam-results/${examId}`, {
      state: {
        ...submissionData,
        aiResults,
        attemptId,
        examId
      }
    });
  };

  // Navigate to exams list
  const handleBackToExams = () => {
    navigate('/student/exams');
  };



  return (
    <div className={`min-h-screen ${themeBg} ${themeText} p-4 sm:p-8`}>
      <div className="max-w-2xl mx-auto">
        {/* Success Header */}
        <div className={`${cardBg} rounded-xl p-8 mb-6 text-center border ${borderColor}`}>
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <FiCheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
          
          <h1 className="text-2xl font-bold mb-2">
            Exam Submitted Successfully!
          </h1>
          
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {autoSubmitted 
              ? 'Your exam was automatically submitted when time expired.'
              : 'Your exam has been submitted for evaluation.'
            }
          </p>

          <div className="text-sm text-gray-500 dark:text-gray-500">
            Submission ID: {attemptId || 'N/A'}
          </div>
        </div>

        {/* Submission Details */}
        <div className={`${cardBg} rounded-xl p-6 mb-6 border ${borderColor}`}>
          <h2 className="text-lg font-semibold mb-4">Submission Details</h2>
          
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <FiUser className="w-5 h-5 text-gray-500" />
              <span className="font-medium">Exam:</span>
              <span className="text-gray-600 dark:text-gray-400">{examTitle}</span>
            </div>
            
            <div className="flex items-center gap-3">
              <FiCalendar className="w-5 h-5 text-gray-500" />
              <span className="font-medium">Submitted:</span>
              <span className="text-gray-600 dark:text-gray-400">
                {formatSubmissionTime(submissionTime)}
              </span>
            </div>
            
            <div className="flex items-center gap-3">
              <FiClock className="w-5 h-5 text-gray-500" />
              <span className="font-medium">Questions Answered:</span>
              <span className="text-gray-600 dark:text-gray-400">
                {Object.keys(answers).length} questions
              </span>
            </div>
          </div>
        </div>

        {/* Submission Status Tracker */}
        <div className={`${cardBg} rounded-xl p-6 mb-6 border ${borderColor}`}>
          <SubmissionStatusTracker
            examId={examId}
            attemptId={attemptId}
            className="w-full"
          />

          {/* Retry Button for Errors */}
          {aiChecking.error && retryCount < 3 && (
            <div className="mt-4 text-center">
              <button
                onClick={retryAIChecking}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2 mx-auto"
              >
                <FiRefreshCw className="w-4 h-4" />
                Retry AI Evaluation
              </button>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <button
            onClick={handleBackToExams}
            className="flex-1 px-6 py-3 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors text-center"
          >
            Back to Exams
          </button>
          
          <button
            onClick={handleViewResults}
            disabled={checkingStatus === 'checking' || checkingStatus === 'waiting'}
            className="flex-1 px-6 py-3 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            <span>View Results</span>
            <FiArrowRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}

export default ExamSubmissionSuccess;
