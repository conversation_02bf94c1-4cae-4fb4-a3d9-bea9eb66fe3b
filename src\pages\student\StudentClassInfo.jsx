import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchClassroomForStudent } from "../../store/slices/ClassroomSlice";
import { fetchAnnouncementsForStudent } from "../../store/slices/AnnouncementSlice";

function StudentClassInfo({ classroomId, onClose }) {
  const dispatch = useDispatch();
  const { classroom, loading, error } = useSelector((state) => state.classroom);
  const {
    announcements,
    loading: announcementsLoading,
    error: announcementsError,
  } = useSelector((state) => state.announcements);

  useEffect(() => {
    if (classroomId) {
      dispatch(fetchClassroomForStudent(classroomId));
      dispatch(fetchAnnouncementsForStudent({ classroom_id: classroomId, skip: 0, limit: 100 }));
    }
  }, [dispatch, classroomId]);

  if (loading) return <p className="text-center mt-10 text-gray-500 dark:text-gray-400">Loading...</p>;
  if (error) return <p className="text-center mt-10 text-red-500 dark:text-red-400">Error: {typeof error === 'object' ? error.detail || JSON.stringify(error) : error}</p>;
  if (!classroom) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/30 backdrop-blur-sm z-0" onClick={onClose} />
      <div className="relative z-10 w-full max-w-3xl mx-auto rounded-2xl shadow-lg p-6 sm:p-10 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 overflow-y-auto max-h-[90vh]">
        {/* Close button */}
        <button
          className="absolute top-4 right-4 text-gray-500 dark:text-gray-300 hover:text-violet-600 dark:hover:text-violet-400 text-2xl font-bold focus:outline-none"
          onClick={onClose}
          aria-label="Close"
        >
          &times;
        </button>
        {/* Class Info */}
        <div className="mb-6">
          <h2 className="text-2xl sm:text-3xl font-extrabold text-violet-700 dark:text-violet-400 mb-2 break-words">{classroom.name}</h2>
          <p className="text-gray-700 dark:text-gray-200 mb-2 break-words">{classroom.description}</p>
          <div className="text-sm text-gray-400 dark:text-gray-500">Subject: {classroom.subject?.name || "-"}</div>
        </div>
        {/* Announcements */}
        <div className="mb-8">
          <h3 className="text-xl font-bold text-violet-700 dark:text-violet-400 mb-3">Announcements</h3>
          {announcementsLoading ? (
            <div className="text-center text-gray-400 dark:text-gray-500">Loading...</div>
          ) : announcementsError ? (
            <div className="text-center text-red-500 dark:text-red-400">{typeof announcementsError === 'string' ? announcementsError : 'Failed to load announcements.'}</div>
          ) : announcements.length === 0 ? (
            <p className="text-gray-400 dark:text-gray-500 italic">No announcements yet.</p>
          ) : (
            <div className="space-y-4">
              {announcements.map((a) => (
                <div key={a.id} className="bg-violet-50 dark:bg-violet-900/30 rounded-lg p-4 shadow-sm">
                  <div className="text-lg font-semibold text-violet-900 dark:text-violet-100">{a.title}</div>
                  <div className="text-gray-700 dark:text-gray-200 mt-1 whitespace-pre-line">{a.content}</div>
                  <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">{new Date(a.created_at).toLocaleString()}</div>
                </div>
              ))}
            </div>
          )}
        </div>
        {/* Students */}
        <div>
          <h3 className="text-xl font-bold text-violet-700 dark:text-violet-400 mb-3">Classmates</h3>
          {classroom.students?.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {classroom.students.map((student) => (
                <div key={student.id} className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow flex items-center gap-4">
                  {student.profile_picture ? (
                    <img src={student.profile_picture} alt={student.username} className="w-12 h-12 rounded-full object-cover border border-gray-200 dark:border-gray-700" />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-violet-100 dark:bg-violet-700 flex items-center justify-center text-xl font-bold text-violet-600 dark:text-violet-200 border border-gray-200 dark:border-gray-700">
                      {student.username ? student.username.charAt(0).toUpperCase() : '?'}
                    </div>
                  )}
                  <div>
                    <div className="text-md font-semibold text-gray-900 dark:text-gray-100">{student.username}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{student.email}</div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-400 dark:text-gray-500 italic">No classmates enrolled yet.</p>
          )}
        </div>
      </div>
    </div>
  );
}

export default StudentClassInfo; 