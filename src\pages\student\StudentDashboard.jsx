import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getStudentUpcomingExams } from '../../store/slices/ExamSlice';
import { fetchMyClassrooms } from '../../store/slices/ClassroomSlice';
import { fetchCurrentUser } from '../../store/slices/userSlice';
import {
  fetchStudentDashboard,
  fetchStudentDashboardSummary,
  fetchStudentQuickActions,
  fetchStudentPerformance,
  fetchStudentSchedule
} from '../../store/slices/StudentDashboardSlice';
import { useStudentDashboard } from '../../hooks/useStudentDashboard';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  FiCalendar,
  FiClock,
  FiBookOpen,
  FiTrendingUp,
  FiUsers,
  FiPlay,
  FiEye,
  FiArrowRight
} from 'react-icons/fi';

function StudentDashboard() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();

  const { exams, loading: examsLoading } = useSelector((state) => state.exams);
  const { myClassrooms, loading: classroomsLoading } = useSelector((state) => state.classroom);
  const { currentUser } = useSelector((state) => state.users);

  // Use custom hook for student dashboard data
  const {
    summary,
    quickActions,
    performance,
    studyMetrics,
    student,
    assignments,
    recentActivity,
    refreshData
  } = useStudentDashboard({
    autoFetch: false, // We'll manually fetch when user is available
    fetchAll: true
  });

  const [stats, setStats] = useState({
    totalClasses: 0,
    upcomingExams: 0,
    completedExams: 0,
    averageScore: 0
  });

  // Theme classes
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";

  useEffect(() => {
    if (!currentUser) {
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, currentUser]);

  useEffect(() => {
    if (currentUser?.id) {
      // Fetch new student dashboard data
      dispatch(fetchStudentDashboard()).catch(error => {
        console.error('Failed to fetch student dashboard:', error);
      });
      dispatch(fetchStudentDashboardSummary()).catch(error => {
        console.error('Failed to fetch dashboard summary:', error);
      });
      dispatch(fetchStudentQuickActions()).catch(error => {
        console.error('Failed to fetch quick actions:', error);
      });
      dispatch(fetchStudentPerformance()).catch(error => {
        console.error('Failed to fetch performance:', error);
      });
      dispatch(fetchStudentSchedule()).catch(error => {
        console.error('Failed to fetch schedule:', error);
      });

      // Keep existing calls for backward compatibility
      dispatch(getStudentUpcomingExams()).catch(error => {
        console.error('Failed to fetch student exams:', error);
      });
      dispatch(fetchMyClassrooms()).catch(error => {
        console.error('Failed to fetch my classrooms:', error);
      });
    }
  }, [dispatch, currentUser]);

  useEffect(() => {
    // Debug logging
    console.log('StudentDashboard - exams:', exams, 'type:', typeof exams, 'isArray:', Array.isArray(exams));
    console.log('StudentDashboard - myClassrooms:', myClassrooms, 'type:', typeof myClassrooms, 'isArray:', Array.isArray(myClassrooms));
    console.log('StudentDashboard - summary:', summary);
    console.log('StudentDashboard - performance:', performance);

    // Use summary data from new API if available, otherwise fallback to old calculation
    if (summary && summary.total_classes !== undefined) {
      setStats({
        totalClasses: summary.total_classes,
        upcomingExams: summary.upcoming_exams,
        completedExams: 0, // Not directly available in summary, could calculate from exams
        averageScore: performance?.overall_grade || summary.overall_grade || 0
      });
    } else {
      // Fallback to old calculation method
      const upcomingExams = Array.isArray(exams) ? exams.filter(exam => {
        const now = new Date();
        const startTime = new Date(exam.start_time);
        return startTime > now;
      }).length : 0;

      const completedExams = Array.isArray(exams) ? exams.filter(exam => exam.completed).length : 0;

      // Handle different possible data structures for myClassrooms
      let classroomsCount = 0;
      if (Array.isArray(myClassrooms)) {
        classroomsCount = myClassrooms.length;
      } else if (myClassrooms && Array.isArray(myClassrooms.data)) {
        classroomsCount = myClassrooms.data.length;
      } else if (myClassrooms && Array.isArray(myClassrooms.results)) {
        classroomsCount = myClassrooms.results.length;
      }

      setStats({
        totalClasses: classroomsCount,
        upcomingExams,
        completedExams,
        averageScore: performance?.overall_grade || 78 // Use performance data or fallback
      });
    }
  }, [exams, myClassrooms, summary, performance]);

  const getExamStatus = (exam) => {
    // Use backend-provided status if available, otherwise fallback to client-side calculation
    if (exam.status) {
      // Map backend status to frontend status for consistency
      const statusMap = {
        'assigned': 'upcoming',
        'upcoming': 'upcoming',
        'ongoing': 'active',
        'started': 'active', // Student has begun the exam
        'submitted': 'completed',
        'disqualified': 'disqualified',
        'ended': 'missed'
      };
      return statusMap[exam.status] || exam.status;
    }

    // Fallback to client-side calculation for backward compatibility
    const now = new Date();
    const startTime = new Date(exam.start_time);
    const endTime = new Date(startTime.getTime() + exam.total_duration * 60000);

    if (exam.completed) return "completed";
    if (now > endTime) return "missed";
    if (now >= startTime && now <= endTime) return "active";
    return "upcoming";
  };

  const upcomingExams = Array.isArray(exams) ? exams.filter(exam => {
    const status = getExamStatus(exam);
    return status === "upcoming" || status === "active";
  }).slice(0, 3) : [];

  // Handle different possible data structures for myClassrooms
  let classroomsArray = [];
  if (Array.isArray(myClassrooms)) {
    classroomsArray = myClassrooms;
  } else if (myClassrooms && Array.isArray(myClassrooms.data)) {
    classroomsArray = myClassrooms.data;
  } else if (myClassrooms && Array.isArray(myClassrooms.results)) {
    classroomsArray = myClassrooms.results;
  }

  const recentClasses = classroomsArray.slice(0, 4);

  return (
    <div className="p-4 sm:p-8">
      {/* Dashboard Header */}
      <div className="sm:flex sm:justify-between sm:items-center mb-8">
        <div className="mb-4 sm:mb-0">
          <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">
            Welcome back, {student?.username || currentUser?.username || 'Student'}!
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Here's what's happening with your studies today
          </p>
          {summary?.last_updated && (
            <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
              Last updated: {new Date(summary.last_updated).toLocaleString()}
            </p>
          )}
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => navigate('/student/exams')}
            className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors flex items-center gap-2"
          >
            <FiBookOpen className="w-4 h-4" />
            View All Exams
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${textSecondary}`}>Total Classes</p>
              <p className={`text-2xl font-bold ${textPrimary}`}>{stats.totalClasses}</p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <FiUsers className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${textSecondary}`}>Upcoming Exams</p>
              <p className={`text-2xl font-bold ${textPrimary}`}>{stats.upcomingExams}</p>
            </div>
            <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
              <FiClock className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${textSecondary}`}>Completed Exams</p>
              <p className={`text-2xl font-bold ${textPrimary}`}>{stats.completedExams}</p>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <FiBookOpen className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm ${textSecondary}`}>Average Score</p>
              <p className={`text-2xl font-bold ${textPrimary}`}>{stats.averageScore}%</p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <FiTrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Additional Stats from New API */}
      {summary && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          <div className={`${cardBg} rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="text-center">
              <p className={`text-xs ${textSecondary}`}>Pending Assignments</p>
              <p className={`text-lg font-bold ${textPrimary}`}>{summary.pending_assignments}</p>
            </div>
          </div>

          <div className={`${cardBg} rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="text-center">
              <p className={`text-xs ${textSecondary}`}>Unread Notifications</p>
              <p className={`text-lg font-bold ${textPrimary}`}>{summary.unread_notifications}</p>
            </div>
          </div>

          <div className={`${cardBg} rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="text-center">
              <p className={`text-xs ${textSecondary}`}>Total Points</p>
              <p className={`text-lg font-bold ${textPrimary}`}>{summary.total_points}</p>
            </div>
          </div>

          <div className={`${cardBg} rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="text-center">
              <p className={`text-xs ${textSecondary}`}>Current Level</p>
              <p className={`text-lg font-bold ${textPrimary}`}>{summary.current_level}</p>
            </div>
          </div>

          <div className={`${cardBg} rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="text-center">
              <p className={`text-xs ${textSecondary}`}>Quick Actions</p>
              <p className={`text-lg font-bold ${textPrimary}`}>{summary.quick_actions_count}</p>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      {quickActions?.length > 0 && (
        <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-8`}>
          <h2 className={`text-lg font-semibold ${textPrimary} mb-4`}>Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {quickActions.slice(0, 6).map((action) => (
              <div key={action.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <h3 className={`font-medium ${textPrimary} mb-2`}>{action.title}</h3>
                <p className={`text-sm ${textSecondary} mb-3`}>{action.description}</p>
                <div className="flex items-center justify-between">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    action.priority === 'high'
                      ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                      : action.priority === 'medium'
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                      : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  }`}>
                    {action.priority}
                  </span>
                  {action.due_date && (
                    <span className={`text-xs ${textSecondary}`}>
                      Due: {new Date(action.due_date).toLocaleDateString()}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Upcoming Exams */}
        <div className="lg:col-span-2">
          <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="flex items-center justify-between mb-6">
              <h2 className={`text-lg font-semibold ${textPrimary}`}>Upcoming Exams</h2>
              <button
                onClick={() => navigate('/student/exams')}
                className="text-violet-600 hover:text-violet-700 text-sm flex items-center gap-1"
              >
                View All <FiArrowRight className="w-4 h-4" />
              </button>
            </div>

            {examsLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : upcomingExams.length > 0 ? (
              <div className="space-y-4">
                {upcomingExams.map((exam) => {
                  const status = getExamStatus(exam);
                  const startTime = new Date(exam.start_time);

                  return (
                    <div key={exam.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className={`font-medium ${textPrimary}`}>{exam.title}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          status === 'active'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                            : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                        }`}>
                          {status === 'active' ? 'Active Now' : 'Upcoming'}
                        </span>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                        <span className="flex items-center gap-1">
                          <FiCalendar className="w-4 h-4" />
                          {startTime.toLocaleDateString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <FiClock className="w-4 h-4" />
                          {exam.total_duration} min
                        </span>
                      </div>
                      <div className="flex justify-end">
                        {status === 'active' ? (
                          <button
                            onClick={() => navigate(`/student/take-exam/${exam.id}`)}
                            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2 text-sm"
                          >
                            <FiPlay className="w-4 h-4" />
                            Take Now
                          </button>
                        ) : (
                          <button
                            onClick={() => navigate(`/student/exams`)}
                            className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors flex items-center gap-2 text-sm"
                          >
                            <FiEye className="w-4 h-4" />
                            View Details
                          </button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className={`${textSecondary}`}>No upcoming exams</p>
              </div>
            )}
          </div>
        </div>

        {/* My Classes */}
        <div>
          <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
            <div className="flex items-center justify-between mb-6">
              <h2 className={`text-lg font-semibold ${textPrimary}`}>My Classes</h2>
              <button
                onClick={() => navigate('/student/classes')}
                className="text-violet-600 hover:text-violet-700 text-sm flex items-center gap-1"
              >
                View All <FiArrowRight className="w-4 h-4" />
              </button>
            </div>

            {classroomsLoading ? (
              <div className="space-y-3">
                {[1, 2, 3, 4].map(i => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full mb-2"></div>
                    <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            ) : recentClasses.length > 0 ? (
              <div className="space-y-3">
                {recentClasses.map((classroom) => (
                  <div key={classroom.id} className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-sm transition-shadow cursor-pointer">
                    <h3 className={`font-medium ${textPrimary} mb-1`}>{classroom.name}</h3>
                    <p className={`text-sm ${textSecondary}`}>{classroom.subject?.name || 'No subject'}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiUsers className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className={`${textSecondary}`}>No classes enrolled</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Assignments and Recent Activity from New API */}
      {(assignments?.length > 0 || recentActivity?.length > 0) && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
          {/* Assignments */}
          {assignments?.length > 0 && (
            <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-lg font-semibold ${textPrimary}`}>Recent Assignments</h2>
                <span className={`text-sm ${textSecondary}`}>{assignments.length} total</span>
              </div>

              <div className="space-y-4">
                {assignments.slice(0, 3).map((assignment) => (
                  <div key={assignment.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className={`font-medium ${textPrimary}`}>{assignment.name}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        assignment.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                          : assignment.status === 'completed'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
                      }`}>
                        {assignment.status}
                      </span>
                    </div>
                    <p className={`text-sm ${textSecondary} mb-2`}>{assignment.description}</p>
                    {assignment.deadline && (
                      <p className={`text-xs ${textSecondary}`}>
                        Due: {new Date(assignment.deadline).toLocaleDateString()}
                      </p>
                    )}
                    {assignment.subject && (
                      <p className={`text-xs ${textSecondary}`}>
                        Subject: {assignment.subject.name}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recent Activity */}
          {recentActivity?.length > 0 && (
            <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700`}>
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-lg font-semibold ${textPrimary}`}>Recent Activity</h2>
                <span className={`text-sm ${textSecondary}`}>{recentActivity.length} activities</span>
              </div>

              <div className="space-y-4">
                {recentActivity.slice(0, 5).map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="w-2 h-2 bg-violet-600 rounded-full mt-2 flex-shrink-0"></div>
                    <div className="flex-1">
                      <h4 className={`font-medium ${textPrimary} text-sm`}>{activity.title}</h4>
                      <p className={`text-xs ${textSecondary} mb-1`}>{activity.description}</p>
                      <p className={`text-xs ${textSecondary}`}>
                        {new Date(activity.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Performance Metrics */}
      {studyMetrics && (
        <div className={`${cardBg} rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 mt-8`}>
          <h2 className={`text-lg font-semibold ${textPrimary} mb-6`}>Study Metrics</h2>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className={`text-2xl font-bold ${textPrimary}`}>{studyMetrics.tasks_completed}</p>
              <p className={`text-sm ${textSecondary}`}>Tasks Completed</p>
            </div>
            <div className="text-center">
              <p className={`text-2xl font-bold ${textPrimary}`}>{studyMetrics.exams_taken}</p>
              <p className={`text-sm ${textSecondary}`}>Exams Taken</p>
            </div>
            <div className="text-center">
              <p className={`text-2xl font-bold ${textPrimary}`}>{studyMetrics.average_grade.toFixed(1)}%</p>
              <p className={`text-sm ${textSecondary}`}>Average Grade</p>
            </div>
            <div className="text-center">
              <p className={`text-2xl font-bold ${textPrimary}`}>{studyMetrics.badges_earned?.length || 0}</p>
              <p className={`text-sm ${textSecondary}`}>Badges Earned</p>
            </div>
          </div>

          {studyMetrics.badges_earned?.length > 0 && (
            <div className="mt-4">
              <p className={`text-sm ${textSecondary} mb-2`}>Recent Badges:</p>
              <div className="flex flex-wrap gap-2">
                {studyMetrics.badges_earned.slice(0, 5).map((badge, index) => (
                  <span key={index} className="px-2 py-1 bg-violet-100 text-violet-800 dark:bg-violet-900/30 dark:text-violet-400 rounded-full text-xs">
                    {badge}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default StudentDashboard;