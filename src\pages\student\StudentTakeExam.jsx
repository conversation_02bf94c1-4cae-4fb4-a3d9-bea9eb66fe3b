import { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { getStudentExam } from "../../store/slices/ExamSlice";
import {
  submitExamSession,
  setConnectionStatus,
  setSessionStatus,
  setSessionId,
  updateAnswer,
  setRemainingTime,
  decrementTime,
  addStrike,
  updateHeartbeat,
  clearSession
} from "../../store/slices/exam/examSessionSlice";
import { checkExamWithAI } from "../../store/slices/exam/aiCheckingSlice";
import { useThemeProvider } from "../../providers/ThemeContext";
import useTimezone from "../../hooks/useTimezone";

import ExamSecurityService from "../../services/exam/security/ExamSecurityService";
import ExamSessionWebSocketService from "../../services/exam/websocket/ExamSessionWebSocketService";
import ExamSessionManager from "../../services/exam/session/ExamSessionManager";

// Enhanced answer management (removed unused import)
import {
  FiClock,
  FiAlertTriangle,
  FiCheck,
  FiArrowLeft,
  FiArrowRight,
  FiSend,
  FiBookOpen,
  FiUser
} from "react-icons/fi";

function StudentTakeExam() {
  const { examId: paramExamId } = useParams();
  const location = useLocation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();
  const { timezoneData, formatDateTime } = useTimezone();

  // Extract exam ID from URL path since useParams might not work with DashboardLayout routing
  const pathParts = location.pathname.split('/');


  // Look for exam ID in the URL pattern: /student/take-exam/{examId}
  let examId = paramExamId;
  if (!examId && pathParts[1] === 'student' && pathParts[2] === 'take-exam' && pathParts[3]) {
    examId = pathParts[3];
  }



  // Redux selectors
  const { currentExam, loading: examLoading, error: examError } = useSelector((state) => {

    return state.exams;
  });

  const examSession = useSelector((state) => {

    return state.examSession;
  });

  const { token } = useSelector((state) => state.login);

  // Local state
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Theme classes - moved here to be available for early returns
  const themeBg = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const themeText = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const cardBg = currentTheme === "dark" ? "bg-gray-800" : "bg-white";

  // Load exam data first - this MUST run before any conditional returns
  useEffect(() => {


    if (examId) {
      // Check for existing session in localStorage
      const existingSessionKey = `exam_session_${examId}`;
      const existingSession = localStorage.getItem(existingSessionKey);

      if (existingSession) {
        try {
          const sessionData = JSON.parse(existingSession);


          // Only restore if session is recent (within last hour)
          const sessionAge = Date.now() - new Date(sessionData.created_at).getTime();
          const oneHour = 60 * 60 * 1000;

          if (sessionAge < oneHour && sessionData.session_id) {

            dispatch(setSessionId(sessionData.session_id));
          } else {

            localStorage.removeItem(existingSessionKey);
          }
        } catch (error) {

          localStorage.removeItem(existingSessionKey);
        }
      }

      // Load exam data if not already loaded
      if (!currentExam) {

        dispatch(getStudentExam(examId));
      }
    }
  }, [dispatch, examId, currentExam]);

  // Setup WebSocket event listeners
  useEffect(() => {
    const handleConnectionEstablished = () => {
      dispatch(setConnectionStatus('connected'));
    };

    const handleHeartbeatResponse = () => {
      dispatch(updateHeartbeat());
    };

    const handleSessionResume = (data) => {

      dispatch(setRemainingTime(data.remaining_time_seconds));
      // Update answers if provided
      if (data.current_answers) {
        Object.entries(data.current_answers).forEach(([questionId, answer]) => {
          dispatch(updateAnswer({ questionId, answer }));
        });
      }
    };

    const handleError = () => {
      dispatch(setConnectionStatus('disconnected'));
    };

    const handleDisconnected = () => {

      dispatch(setConnectionStatus('disconnected'));
    };

    // Add event listeners
    ExamSessionWebSocketService.on('connection_established', handleConnectionEstablished);
    ExamSessionWebSocketService.on('heartbeat_response', handleHeartbeatResponse);
    ExamSessionWebSocketService.on('session_resume', handleSessionResume);
    ExamSessionWebSocketService.on('error', handleError);
    ExamSessionWebSocketService.on('disconnected', handleDisconnected);

    // Enhanced cleanup on unmount
    return () => {


      // Remove WebSocket event listeners
      ExamSessionWebSocketService.off('connection_established', handleConnectionEstablished);
      ExamSessionWebSocketService.off('heartbeat_response', handleHeartbeatResponse);
      ExamSessionWebSocketService.off('session_resume', handleSessionResume);
      ExamSessionWebSocketService.off('error', handleError);
      ExamSessionWebSocketService.off('disconnected', handleDisconnected);

      // Disconnect WebSocket
      ExamSessionWebSocketService.disconnect();

      // Deactivate security measures
      ExamSecurityService.deactivate();

      // Only clear session data if exam was submitted or terminated
      if (examSession.status === 'submitted' || examSession.status === 'terminated') {

        dispatch(clearSession());

        // Clean up localStorage session data
        if (examId) {
          localStorage.removeItem(`exam_session_${examId}`);
        }
      } else if (examSession.status === 'active') {

        // Keep session data for potential recovery
      } else {

        dispatch(clearSession());
      }
    };
  }, [dispatch]);

  // Timer countdown using Redux state
  useEffect(() => {
    if (examSession.status === 'active' && examSession.remainingTimeSeconds > 0) {
      const timer = setInterval(() => {
        dispatch(decrementTime());

        // Auto-submit when time runs out
        if (examSession.remainingTimeSeconds <= 1) {
          handleAutoSubmit();
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [examSession.status, examSession.remainingTimeSeconds, dispatch]);

  // Auto-submit when strikes reach 3
  useEffect(() => {
    if (examSession.autoSubmitTriggered && examSession.strikes >= 3) {

      handleAutoSubmit();
    }
  }, [examSession.autoSubmitTriggered, examSession.strikes]);

  // Track fullscreen status
  useEffect(() => {
    const updateFullscreenStatus = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    // Set initial state
    updateFullscreenStatus();

    // Add event listeners for fullscreen changes
    document.addEventListener('fullscreenchange', updateFullscreenStatus);
    document.addEventListener('webkitfullscreenchange', updateFullscreenStatus);
    document.addEventListener('msfullscreenchange', updateFullscreenStatus);

    return () => {
      document.removeEventListener('fullscreenchange', updateFullscreenStatus);
      document.removeEventListener('webkitfullscreenchange', updateFullscreenStatus);
      document.removeEventListener('msfullscreenchange', updateFullscreenStatus);
    };
  }, []);

  // Define handleSubmitExam as useCallback to avoid dependency issues
  const handleSubmitExam = useCallback(async (autoSubmit = false) => {
    try {


      // 🚨 CRITICAL: Validate session ID before submission
      if (!examSession.sessionId) {


        // Try to recover session from localStorage
        const existingSessionKey = `exam_session_${examId}`;
        const existingSession = localStorage.getItem(existingSessionKey);

        if (existingSession) {
          try {
            const sessionData = JSON.parse(existingSession);
            if (sessionData.session_id) {
              dispatch(setSessionId(sessionData.session_id));

              // Wait a moment for state to update
              await new Promise(resolve => setTimeout(resolve, 100));

              // Check if session ID is now available
              const updatedSession = examSession.sessionId || sessionData.session_id;
              if (updatedSession) {

                // Continue with submission using recovered session ID
              } else {
                throw new Error('Failed to recover session ID. Please restart the exam.');
              }
            } else {
              throw new Error('No valid session ID in localStorage. Please restart the exam.');
            }
          } catch (parseError) {

            localStorage.removeItem(existingSessionKey);
            throw new Error('Invalid session data. Please restart the exam.');
          }
        } else {
          throw new Error('No valid session ID available. Please restart the exam.');
        }
      }

      // Ensure we have a valid examId for AI checking
      if (!examId || examId === 'undefined' || examId === 'null') {

        throw new Error('Invalid exam ID. Cannot submit exam.');
      }



      // Submit exam session via Redux with complete exam data according to API specification
      const result = await dispatch(submitExamSession({
        sessionId: examSession.sessionId,
        exam: currentExam,
        questions: currentExam?.questions || [],
        studentAnswers: examSession.currentAnswers || {},
        isAutoSubmit: autoSubmit // Use auto-submit API for disqualification scenarios
      })).unwrap();



      // Deactivate security measures
      ExamSecurityService.deactivate();

      // Disconnect WebSocket
      ExamSessionWebSocketService.disconnect();

      // 🤖 AUTOMATICALLY TRIGGER AI CHECKING AFTER SUCCESSFUL SUBMISSION

      try {
        // Validate examId before AI checking
        if (examId && examId !== 'undefined' && examId !== 'null') {
          await dispatch(checkExamWithAI({
            examId: examId,
            studentId: null // null for student endpoint
          })).unwrap();

        } else {

        }
      } catch (aiError) {

        // Don't block navigation if AI checking fails
      }

      // Navigate to submission success page
      navigate(`/student/exam-submitted/${examId}`, {
        state: {
          answers: examSession.currentAnswers,
          autoSubmitted: autoSubmit,
          examTitle: currentExam?.title || 'Exam',
          submissionResult: result,
          submissionTime: new Date().toISOString(),
          autoAITriggered: true, // Flag to indicate AI was auto-triggered
          sessionId: examSession.sessionId // Include session ID for reference
        }
      });
    } catch (error) {


      // Provide more specific error messages
      let errorMessage = 'Failed to submit exam';
      const errorMsg = error?.message || error?.toString() || '';

      if (errorMsg.includes('session')) {
        errorMessage = 'Session error occurred during submission. Please try again.';
      } else if (errorMsg.includes('network') || errorMsg.includes('fetch')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (errorMsg) {
        errorMessage = `Submission failed: ${errorMsg}`;
      }

      alert(errorMessage);
    }
  }, [dispatch, examSession.sessionId, examSession.currentAnswers, examSession.status, navigate, examId, currentExam?.title]);

  const handleAutoSubmit = useCallback(() => {
    // Auto-submit when time runs out
    handleSubmitExam(true);
  }, [handleSubmitExam]);

  // Enhanced debug information and session tracking
  useEffect(() => {





    // Check for session consistency
    if (examSession.status === 'active' && !examSession.sessionId) {


      // Try to recover from localStorage
      const existingSessionKey = `exam_session_${examId}`;
      const existingSession = localStorage.getItem(existingSessionKey);

      if (existingSession) {
        try {
          const sessionData = JSON.parse(existingSession);
          if (sessionData.session_id) {

            dispatch(setSessionId(sessionData.session_id));
          }
        } catch (error) {

        }
      }
    }
  }, [
    examSession.connectionStatus,
    examSession.sessionId,
    examSession.status,
    examSession.currentAnswers,
    examSession.remainingTimeSeconds,
    examSession.strikes,
    examSession.submitting,
    examSession.autoSubmitTriggered,
    examId,
    currentExam,
    dispatch
  ]);

  const formatTime = (seconds) => {
    // Handle invalid or undefined values
    if (!seconds || isNaN(seconds) || seconds < 0) {
      return "0:00";
    }

    const totalSeconds = Math.floor(seconds);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartExam = async () => {
    if (!currentExam || !currentExam.questions || currentExam.questions.length === 0) {
      alert('No questions available for this exam.');
      return;
    }

    // Check if exam has start time
    if (!currentExam.start_time) {
      alert('Exam start time is not set');
      return;
    }

    // Check if exam is within the allowed time window
    const now = new Date();
    // Backend sends UTC time, convert to local for comparison
    const utcStartTime = currentExam.start_time.endsWith('Z') ? currentExam.start_time : currentExam.start_time + 'Z';
    const startTime = new Date(utcStartTime);
    const endTime = new Date(startTime.getTime() + currentExam.total_duration * 60000);

    if (now < startTime) {
      const localStartTime = formatDateTime(currentExam.start_time, true);
      alert(`This exam has not started yet. Start time: ${localStartTime}`);
      return;
    }

    if (now > endTime) {
      alert('This exam has already ended.');
      return;
    }

    try {


      // Set status to starting
      dispatch(setSessionStatus('starting'));

      // Get user data and token
      const token = localStorage.getItem('token');


      let sessionData = null;
      try {
        // Use ExamSessionManager to start the session properly
        sessionData = await ExamSessionManager.startSession(examId, token);

        // Validate session data
        if (!sessionData || !sessionData.session_id) {
          throw new Error('Invalid session data received from server');
        }

        // Store session ID in Redux
        dispatch(setSessionId(sessionData.session_id));


      } catch (apiError) {


        // Don't create fallback sessions - fail properly
        throw new Error(`Failed to start exam session: ${apiError.message}`);
      }

      // Validate session was created successfully
      const finalSessionId = sessionData?.session_id;
      if (!finalSessionId) {
        throw new Error('Failed to create exam session. Please try again.');
      }



      // Store session ID in localStorage for recovery
      const sessionKey = `exam_session_${examId}`;
      const sessionStorageData = {
        session_id: finalSessionId,
        exam_id: examId,
        created_at: new Date().toISOString(),
        status: 'active'
      };
      localStorage.setItem(sessionKey, JSON.stringify(sessionStorageData));


      // Verify session ID is properly set in Redux


      // SIMPLIFIED APPROACH: Skip WebSocket for now and start exam directly
      dispatch(setConnectionStatus('connected')); // Set as connected immediately

      // Initialize exam session state
      dispatch(setRemainingTime(currentExam.total_duration * 60)); // Convert minutes to seconds

      // Set exam as active - use the correct action to set status to 'active'
      dispatch(setSessionStatus('active'));



      // Activate security measures with fullscreen

      ExamSecurityService.activate({
        onViolation: (violation) => {


          // Show user-friendly violation message
          if (violation.type === 'fullscreen_exit') {
            alert('⚠️ Please stay in fullscreen mode during the exam. The exam will automatically return to fullscreen.');
          } else if (violation.type === 'tab_switch') {
            alert('⚠️ Tab switching is not allowed during the exam. This has been recorded as a violation.');
          } else if (violation.type === 'fullscreen_denied') {
            alert('⚠️ Fullscreen mode is required for this exam. Please allow fullscreen access and try again.');
          }

          // Add strike to Redux state
          dispatch(addStrike());
        }
      });



    } catch (error) {

      dispatch(setConnectionStatus('error'));

      // Provide detailed error information for debugging


      alert(`Failed to start exam: ${error.message || error}. Please check your internet connection and try again.`);
    }
  };

  const handleAnswerChange = (questionId, answer) => {
    if (!questionId) {

      return;
    }

    // Update Redux state
    dispatch(updateAnswer({ questionId, answer }));

    // Send answer to server via WebSocket
    ExamSessionWebSocketService.saveAnswer(questionId, answer);


  };

  // Handle disqualification auto-submit
  const handleDisqualificationSubmit = useCallback(async (reason = 'Disqualified for cheating') => {
    try {


      // Force submit with empty answers using auto-submit API
      await handleSubmitExam(true); // true = autoSubmit mode



      // Show disqualification message
      alert(`Exam has been automatically submitted due to: ${reason}`);

    } catch (error) {

      alert('Failed to submit exam after disqualification. Please contact support.');
    }
  }, [handleSubmitExam]);

  // Debug function to test API endpoints
  const testAPIEndpoints = async () => {

    const token = localStorage.getItem('token');

    // Test 1: CORRECT session endpoint
    try {
      const response1 = await fetch(`https://edufair.duckdns.org/api/exams/session/exam-session/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ exam_id: examId })
      });
    } catch (error) {
    }

    // Test 2: Alternative endpoint (for comparison)
    try {
      const response2 = await fetch(`https://edufair.duckdns.org/api/exams/${examId}/start-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ exam_id: examId })
      });
    } catch (error) {
    }

    // Test 3: Simple exam fetch
    try {
      const response3 = await fetch(`https://edufair.duckdns.org/api/exams/student/${examId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
    } catch (error) {
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < currentExam.questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleQuestionNavigation = (index) => {
    setCurrentQuestionIndex(index);
  };



  const getAnsweredCount = () => {
    return Object.keys(examSession.currentAnswers).length;
  };

  const isQuestionAnswered = (questionId) => {
    return examSession.currentAnswers.hasOwnProperty(questionId);
  };

  // Conditional rendering AFTER all hooks
  if (examLoading) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
          <p>Loading exam...</p>
        </div>
      </div>
    );
  }

  if (examError) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className={`${cardBg} rounded-xl p-8 max-w-md mx-auto text-center shadow-lg`}>
          <FiAlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Exam</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {examError}
          </p>
          <button
            onClick={() => navigate('/student/exams')}
            className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
          >
            Back to Exams
          </button>
        </div>
      </div>
    );
  }

  if (!currentExam) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className={`${cardBg} rounded-xl p-8 max-w-md mx-auto text-center shadow-lg`}>
          <FiBookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Loading Exam...</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Please wait while we load your exam details.
          </p>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            <p>Exam ID: {examId}</p>
            <p>Loading: {examLoading ? 'Yes' : 'No'}</p>
            <p>Error: {examError ? 'Yes' : 'No'}</p>
          </div>
          {examError && (
            <div className="mt-4 p-3 bg-red-100 dark:bg-red-900/30 rounded-lg">
              <p className="text-red-600 dark:text-red-400 text-sm">
                Error: {typeof examError === 'string' ? examError : JSON.stringify(examError)}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Check if exam has questions
  if (!currentExam.questions || currentExam.questions.length === 0) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className={`${cardBg} rounded-xl p-8 max-w-md mx-auto text-center shadow-lg`}>
          <FiAlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">No Questions Available</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            This exam doesn't have any questions yet.
          </p>
          <button
            onClick={() => navigate('/student/exams')}
            className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
          >
            Back to Exams
          </button>
        </div>
      </div>
    );
  }

  // Pre-exam screen
  if (examSession.status === 'idle' || examSession.status === 'starting') {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center p-4`}>
        <div className={`${cardBg} rounded-xl p-8 max-w-2xl mx-auto shadow-lg`}>
          <div className="text-center mb-8">
            <FiBookOpen className="w-16 h-16 text-violet-600 mx-auto mb-4" />
            <h1 className="text-2xl font-bold mb-2">{currentExam.title}</h1>
            <p className="text-gray-600 dark:text-gray-400 mb-3">{currentExam.description}</p>

            {/* Show connection status when starting exam */}
            {examSession.status === 'starting' && (
              <div className="mb-6 p-4 border rounded-lg bg-blue-50 dark:bg-blue-900/20">
                <div className="flex items-center justify-center gap-3 mb-3">
                  <div className="w-4 h-4 rounded-full bg-blue-500 animate-pulse"></div>
                  <span className="font-medium text-blue-700 dark:text-blue-300">
                    🔄 Starting Exam Session...
                  </span>
                </div>
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Please wait while we establish a secure connection for your exam.
                </p>
              </div>
            )}

            <div className="text-sm text-gray-500 dark:text-gray-400">
              <p><strong>Start Time:</strong> {currentExam.start_time ? formatDateTime(currentExam.start_time, true) : 'Not set'}</p>
              {timezoneData && timezoneData.detected && (
                <p><strong>Your Location:</strong> {timezoneData.city}, {timezoneData.country}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <FiClock className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <div className="font-semibold">{currentExam.total_duration} minutes</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Duration</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <FiBookOpen className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <div className="font-semibold">{currentExam.questions?.length || 0} questions</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Questions</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <FiUser className="w-6 h-6 text-purple-600 mx-auto mb-2" />
              <div className="font-semibold">{currentExam.total_marks} marks</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Marks</div>
            </div>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-4">
            <div className="flex items-start gap-3">
              <FiAlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Exam Instructions:</h3>
                <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                  <li>• <strong>Fullscreen mode will be automatically activated</strong> when you start the exam</li>
                  <li>• Once you start the exam, the timer will begin counting down</li>
                  <li>• You can navigate between questions using the navigation panel</li>
                  <li>• Make sure to save your answers before the time runs out</li>
                  <li>• The exam will auto-submit when time expires</li>
                  <li>• <strong>Do not exit fullscreen or switch tabs</strong> during the exam - this will be recorded as a violation</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-8">
            <div className="flex items-start gap-3">
              <FiAlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-red-800 dark:text-red-200 mb-2">🔒 Security Requirements:</h3>
                <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                  <li>• <strong>Fullscreen mode will be enforced</strong> - Do not exit fullscreen during the exam</li>
                  <li>• <strong>Tab switching is prohibited</strong> - Switching tabs will be detected and reported</li>
                  <li>• <strong>Copy/paste is disabled</strong> - All copy, paste, and cut operations are blocked</li>
                  <li>• <strong>Right-click is disabled</strong> - Context menus and developer tools are blocked</li>
                  <li>• <strong>Screenshots are prevented</strong> - Screen capture attempts will be detected</li>
                  <li>• <strong>Browser tools are blocked</strong> - Developer tools, inspect element, etc. are disabled</li>
                  <li>• ⚠️ <strong>Violations will be recorded</strong> and may result in exam termination</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="flex gap-4 justify-center">
            <button
              onClick={() => navigate('/student/exams')}
              disabled={examSession.status === 'starting'}
              className="px-6 py-3 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors flex items-center gap-2 disabled:opacity-50"
            >
              <FiArrowLeft className="w-4 h-4" />
              Back to Exams
            </button>

            {/* Debug buttons - remove in production */}
            <button
              onClick={testAPIEndpoints}
              className="px-4 py-3 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors font-semibold flex items-center gap-2"
            >
              🧪 Test APIs
            </button>

            <button
              onClick={() => handleDisqualificationSubmit('Testing auto-submit')}
              className="px-4 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors font-semibold flex items-center gap-2"
            >
              🚨 Test Auto-Submit
            </button>

            <button
              onClick={handleStartExam}
              disabled={examSession.status === 'starting'}
              className="px-8 py-3 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors font-semibold flex items-center gap-2 disabled:opacity-50"
            >
              {examSession.status === 'starting' ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Starting Exam...
                </>
              ) : (
                'Start Exam'
              )}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Check if exam has questions
  if (!currentExam.questions || currentExam.questions.length === 0) {
    return (
      <div className={`min-h-screen ${themeBg} ${themeText} flex items-center justify-center`}>
        <div className={`${cardBg} rounded-xl p-8 max-w-md mx-auto text-center shadow-lg`}>
          <FiAlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">No Questions Available</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            This exam does not have any questions yet.
          </p>
          <button
            onClick={() => navigate('/student/exams')}
            className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
          >
            Back to Exams
          </button>
        </div>
      </div>
    );
  }

  const currentQuestion = currentExam.questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / currentExam.questions.length) * 100;

  return (
    <div className={`min-h-screen ${themeBg} ${themeText}`}>
      {/* Header with timer and progress */}
      <div className={`${cardBg} shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10`}>
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-semibold">{currentExam.title}</h1>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Question {currentQuestionIndex + 1} of {currentExam.questions.length}
              </div>

              {/* Connection & Security Status */}
              <div className="flex items-center gap-4 mt-1">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    examSession.connectionStatus === 'connected' ? 'bg-green-500' :
                    examSession.connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' :
                    'bg-red-500'
                  }`}></div>
                  <span className="text-xs text-gray-500">
                    {examSession.connectionStatus === 'connected' ? 'Connected' :
                     examSession.connectionStatus === 'connecting' ? 'Connecting...' :
                     examSession.connectionStatus === 'reconnecting' ? 'Reconnecting...' :
                     'Disconnected'}
                  </span>
                  {/* Debug: Show actual status value */}
                  <span className="text-xs text-gray-400 ml-1">
                    ({examSession.connectionStatus})
                  </span>
                  {/* Enhanced session status display */}
                  <span className={`text-xs ml-2 px-2 py-1 rounded ${
                    examSession.sessionId
                      ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                  }`}>
                    Session: {examSession.sessionId ?
                      `${examSession.sessionId.substring(0, 8)}...` :
                      'No Session ID!'
                    }
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                  <span className="text-xs text-gray-500">🔒 Secure Mode</span>
                </div>

                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    isFullscreen ? 'bg-green-500' : 'bg-orange-500'
                  }`}></div>
                  <span className="text-xs text-gray-500">
                    📺 {isFullscreen ? 'Fullscreen' : 'Windowed'}
                  </span>
                </div>

                {examSession.strikes > 0 && (
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${
                      examSession.strikes >= 3 ? 'bg-red-500 animate-pulse' : 'bg-red-500'
                    }`}></div>
                    <span className={`text-xs font-medium ${
                      examSession.strikes >= 3 ? 'text-red-600 animate-pulse' : 'text-red-500'
                    }`}>
                      ⚠️ Strikes: {examSession.strikes}/3
                      {examSession.strikes >= 3 && (
                        <span className="ml-1 font-bold">- AUTO-SUBMITTING!</span>
                      )}
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* Session Status Indicator */}
              <div className="text-right">
                <div className={`text-xs px-2 py-1 rounded-full ${
                  examSession.sessionId && examSession.status === 'active'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {examSession.sessionId && examSession.status === 'active'
                    ? '🟢 Session Active'
                    : '🔴 No Session'
                  }
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  {examSession.sessionId ? `ID: ${examSession.sessionId.substring(0, 8)}...` : 'Not Started'}
                </div>
              </div>

              <div className="text-right">
                <div className={`text-lg font-mono font-bold ${examSession.remainingTimeSeconds < 300 ? 'text-red-600' : 'text-green-600'}`}>
                  {formatTime(examSession.remainingTimeSeconds)}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">Time Remaining</div>
              </div>

              <button
                onClick={() => {
                  // Validate session before allowing submission
                  if (!examSession.sessionId) {
                    alert('No active exam session found. Please restart the exam.');
                    return;
                  }
                  if (examSession.status !== 'active') {
                    alert('Exam session is not active. Please restart the exam.');
                    return;
                  }
                  setShowSubmitConfirm(true);
                }}
                disabled={examSession.submitting || !examSession.sessionId || examSession.status !== 'active'}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center gap-2"
              >
                {examSession.submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <FiSend className="w-4 h-4" />
                    Submit
                  </>
                )}
              </button>

              {/* Submit Button Status Message */}
              {(!examSession.sessionId || examSession.status !== 'active') && (
                <div className="text-xs text-red-600 dark:text-red-400 ml-2">
                  {!examSession.sessionId
                    ? 'No session - Please start exam first'
                    : 'Session not active - Please restart exam'
                  }
                </div>
              )}
            </div>
          </div>
          
          {/* Progress bar */}
          <div className="mt-4">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-violet-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-4 flex gap-6">
        {/* Question Navigation Sidebar */}
        <div className={`${cardBg} rounded-xl p-4 w-64 h-fit sticky top-24 shadow-sm`}>
          <h3 className="font-semibold mb-4">Questions</h3>
          <div className="grid grid-cols-5 gap-2">
            {currentExam.questions.map((_, index) => (
              <button
                key={index}
                onClick={() => handleQuestionNavigation(index)}
                className={`w-10 h-10 rounded-lg text-sm font-medium transition-colors ${
                  index === currentQuestionIndex
                    ? 'bg-violet-600 text-white'
                    : isQuestionAnswered(currentExam.questions[index].id)
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                    : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {index + 1}
              </button>
            ))}
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Answered: {getAnsweredCount()} / {currentExam.questions.length}
            </div>
          </div>
        </div>

        {/* Main Question Area */}
        <div className="flex-1">
          <div className={`${cardBg} rounded-xl p-8 shadow-sm`}>
            {currentQuestion && (
              <>
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-semibold">
                      Question {currentQuestionIndex + 1}
                    </h2>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {currentQuestion.marks} mark{currentQuestion.marks !== 1 ? 's' : ''}
                    </span>
                  </div>
                  <p className="text-gray-800 dark:text-gray-200 leading-relaxed">
                    {currentQuestion.text}
                  </p>
                </div>

                {/* Answer Options */}
                <div className="mb-8">
                  {currentQuestion.Type === 'MCQS' ? (
                    <div className="space-y-3">
                      {currentQuestion.options.map((option, index) => (
                        <label
                          key={index}
                          className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-colors ${
                            examSession.currentAnswers[currentQuestion.id] === option.option_text
                              ? 'border-violet-600 bg-violet-50 dark:bg-violet-900/20'
                              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                          }`}
                        >
                          <input
                            type="radio"
                            name={`question-${currentQuestion.id}`}
                            value={option.option_text}
                            checked={examSession.currentAnswers[currentQuestion.id] === option.option_text}
                            onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                            className="w-4 h-4 text-violet-600 mr-3"
                          />
                          <span className="flex-1">{option.option_text}</span>
                        </label>
                      ))}
                    </div>
                  ) : (
                    <textarea
                      value={examSession.currentAnswers[currentQuestion.id] || ''}
                      onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                      placeholder="Type your answer here..."
                      rows={6}
                      className="w-full p-4 border-2 border-gray-200 dark:border-gray-700 rounded-lg focus:border-violet-600 focus:outline-none bg-white dark:bg-gray-800 resize-none"
                    />
                  )}
                </div>

                {/* Navigation Buttons */}
                <div className="flex justify-between">
                  <button
                    onClick={handlePreviousQuestion}
                    disabled={currentQuestionIndex === 0}
                    className="px-6 py-3 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    <FiArrowLeft className="w-4 h-4" />
                    Previous
                  </button>
                  
                  <button
                    onClick={handleNextQuestion}
                    disabled={currentQuestionIndex === currentExam.questions.length - 1}
                    className="px-6 py-3 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    Next
                    <FiArrowRight className="w-4 h-4" />
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Submit Confirmation Modal */}
      {showSubmitConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className={`${cardBg} rounded-xl p-6 max-w-md w-full`}>
            <h3 className="text-lg font-semibold mb-4">Submit Exam?</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Are you sure you want to submit your exam? You have answered {getAnsweredCount()} out of {currentExam.questions.length} questions.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowSubmitConfirm(false)}
                className="flex-1 px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => handleSubmitExam()}
                disabled={examSession.submitting}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
              >
                {examSession.submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <FiCheck className="w-4 h-4" />
                    Submit
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default StudentTakeExam;
