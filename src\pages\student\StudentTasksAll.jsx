import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  fetchTasksByStudent,
  selectTasks,
  selectTasksLoading,
  selectTasksError,
  clearTaskState
} from '../../store/slices/TaskSlice';
import {
  FiSearch,
  FiCalendar,
  FiClock,
  FiCheckCircle,
  FiAlertCircle,
  FiEye,
  FiLoader,
  FiBook,
  FiSend,
  FiEdit3,
  FiStar,
  FiTrendingUp,
  FiArrowLeft,
  FiFilter,
  FiGrid,
  FiList,
  FiMoreVertical,
  FiDownload,
  FiFileText,
  FiUser
} from 'react-icons/fi';

/**
 * StudentTasksAll Page
 * Google Classroom-like view for all student tasks with modern UI/UX
 */
const StudentTasksAll = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();

  // Redux state
  const tasks = useSelector(selectTasks);
  const loading = useSelector(selectTasksLoading);
  const error = useSelector(selectTasksError);

  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [subjectFilter, setSubjectFilter] = useState('all');
  const [sortBy, setSortBy] = useState('deadline');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [selectedTasks, setSelectedTasks] = useState([]);

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300';

  // Load student tasks on mount
  useEffect(() => {
    dispatch(fetchTasksByStudent());

    return () => {
      dispatch(clearTaskState());
    };
  }, [dispatch]);

  // Filter and sort tasks
  const filteredTasks = useMemo(() => {
    let filtered = tasks.filter(task => {
      const matchesSearch = task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (task.description && task.description.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesStatus = statusFilter === 'all' || 
                           (task.status && task.status.toLowerCase() === statusFilter.toLowerCase());
      
      const matchesSubject = subjectFilter === 'all' || 
                            (task.subject && 
                             (typeof task.subject === 'string' 
                               ? task.subject === subjectFilter 
                               : task.subject.name === subjectFilter));
      
      return matchesSearch && matchesStatus && matchesSubject;
    });

    // Sort tasks
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'deadline':
          if (!a.deadline && !b.deadline) return 0;
          if (!a.deadline) return 1;
          if (!b.deadline) return -1;
          return new Date(a.deadline) - new Date(b.deadline);
        case 'name':
          return a.name.localeCompare(b.name);
        case 'subject':
          const aSubject = typeof a.subject === 'string' ? a.subject : (a.subject?.name || '');
          const bSubject = typeof b.subject === 'string' ? b.subject : (b.subject?.name || '');
          return aSubject.localeCompare(bSubject);
        case 'created':
          return new Date(b.created_at) - new Date(a.created_at);
        default:
          return 0;
      }
    });

    return filtered;
  }, [tasks, searchTerm, statusFilter, subjectFilter, sortBy]);

  // Get unique subjects for filter
  const subjects = useMemo(() => {
    const uniqueSubjects = [...new Set(tasks.map(t => {
      if (typeof t.subject === 'string') {
        return t.subject;
      } else if (t.subject && typeof t.subject === 'object' && t.subject.name) {
        return t.subject.name;
      }
      return null;
    }).filter(Boolean))];
    return uniqueSubjects.sort();
  }, [tasks]);

  // Handle task actions
  const handleViewTask = (taskId) => {
    navigate(`/student/task/${taskId}`);
  };

  const handleBackToTasks = () => {
    navigate('/student/tasks');
  };

  // Check if task is overdue
  const isOverdue = (task) => {
    return task.deadline && 
           new Date(task.deadline) < new Date() && 
           task.status === 'pending';
  };

  // Get task priority
  const getTaskPriority = (task) => {
    if (isOverdue(task)) return 'high';
    if (task.deadline) {
      const daysUntilDeadline = Math.ceil((new Date(task.deadline) - new Date()) / (1000 * 60 * 60 * 24));
      if (daysUntilDeadline <= 1) return 'high';
      if (daysUntilDeadline <= 3) return 'medium';
    }
    return 'low';
  };

  // Status badge component
  const StatusBadge = ({ status, isOverdue }) => {
    const statusConfig = {
      not_submitted: {
        color: isOverdue
          ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border border-red-200 dark:border-red-800'
          : 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400 border border-orange-200 dark:border-orange-800',
        icon: isOverdue ? FiAlertCircle : FiClock,
        text: isOverdue ? 'Overdue' : 'Missing'
      },
      pending: {
        color: isOverdue
          ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border border-red-200 dark:border-red-800'
          : 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400 border border-orange-200 dark:border-orange-800',
        icon: isOverdue ? FiAlertCircle : FiClock,
        text: isOverdue ? 'Overdue' : 'Missing'
      },
      in_progress: {
        color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border border-blue-200 dark:border-blue-800',
        icon: FiEdit3,
        text: 'Draft'
      },
      submitted: {
        color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border border-green-200 dark:border-green-800',
        icon: FiCheckCircle,
        text: 'Turned in'
      },
      graded: {
        color: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 border border-purple-200 dark:border-purple-800',
        icon: FiStar,
        text: 'Graded'
      }
    };

    const config = statusConfig[status] || statusConfig.not_submitted;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3" />
        {config.text}
      </span>
    );
  };

  // Priority indicator component
  const PriorityIndicator = ({ priority }) => {
    const priorityConfig = {
      high: { color: 'bg-red-500', label: 'High Priority' },
      medium: { color: 'bg-yellow-500', label: 'Medium Priority' },
      low: { color: 'bg-green-500', label: 'Low Priority' }
    };

    const config = priorityConfig[priority] || priorityConfig.low;

    return (
      <div className={`w-1 h-full ${config.color} rounded-full`} title={config.label} />
    );
  };

  // Task Card Component for Grid View
  const TaskCard = ({ task, onView }) => {
    const priority = getTaskPriority(task);
    const taskIsOverdue = isOverdue(task);

    return (
      <div
        className={`${bgPrimary} rounded-lg border ${borderColor} hover:shadow-lg transition-all duration-200 cursor-pointer group relative overflow-hidden`}
        onClick={() => onView(task.id)}
      >
        <PriorityIndicator priority={priority} />

        <div className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1 min-w-0">
              <h3 className={`font-semibold ${textPrimary} mb-1 line-clamp-2 group-hover:text-blue-600 transition-colors`}>
                {task.name}
              </h3>
              {task.subject && (
                <div className="flex items-center gap-1 mb-2">
                  <FiBook className={`w-4 h-4 ${textSecondary}`} />
                  <span className={`text-sm ${textSecondary}`}>
                    {typeof task.subject === 'string' ? task.subject : task.subject.name}
                  </span>
                </div>
              )}
            </div>
            <StatusBadge status={task.status} isOverdue={taskIsOverdue} />
          </div>

          {/* Description */}
          {task.description && (
            <p className={`text-sm ${textSecondary} mb-4 line-clamp-3`}>
              {task.description}
            </p>
          )}

          {/* Deadline */}
          {task.deadline && (
            <div className="flex items-center gap-2 mb-4">
              <FiCalendar className={`w-4 h-4 ${taskIsOverdue ? 'text-red-500' : textSecondary}`} />
              <span className={`text-sm ${taskIsOverdue ? 'text-red-500 font-medium' : textSecondary}`}>
                Due: {new Date(task.deadline).toLocaleDateString()} at{' '}
                {new Date(task.deadline).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </span>
            </div>
          )}

          {/* Topics/Chapters */}
          {(task.chapters?.length > 0 || task.topics?.length > 0) && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-1">
                {task.chapters?.slice(0, 2).map((chapter, index) => (
                  <span key={index} className={`px-2 py-1 text-xs rounded-full ${bgSecondary} ${textSecondary}`}>
                    {typeof chapter === 'string' ? chapter : (chapter?.name || 'Unnamed Chapter')}
                  </span>
                ))}
                {task.topics?.slice(0, 2).map((topic, index) => (
                  <span key={index} className={`px-2 py-1 text-xs rounded-full ${bgSecondary} ${textSecondary}`}>
                    {typeof topic === 'string' ? topic : (topic?.name || 'Unnamed Topic')}
                  </span>
                ))}
                {(task.chapters?.length > 2 || task.topics?.length > 2) && (
                  <span className={`px-2 py-1 text-xs rounded-full ${bgSecondary} ${textSecondary}`}>
                    +{(task.chapters?.length || 0) + (task.topics?.length || 0) - 2} more
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              <FiClock className={`w-4 h-4 ${textSecondary}`} />
              <span className={`text-xs ${textSecondary}`}>
                Created {new Date(task.created_at).toLocaleDateString()}
              </span>
            </div>

            <button className="text-blue-600 hover:text-blue-700 text-sm flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <FiEye className="w-4 h-4" />
              View
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Task List Item Component for List View
  const TaskListItem = ({ task, onView }) => {
    const priority = getTaskPriority(task);
    const taskIsOverdue = isOverdue(task);

    return (
      <div
        className={`${bgPrimary} rounded-lg border ${borderColor} hover:shadow-md transition-all duration-200 cursor-pointer group`}
        onClick={() => onView(task.id)}
      >
        <div className="flex items-center p-6">
          <PriorityIndicator priority={priority} />

          <div className="flex-1 min-w-0 ml-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h3 className={`font-semibold ${textPrimary} mb-1 group-hover:text-blue-600 transition-colors`}>
                  {task.name}
                </h3>

                <div className="flex items-center gap-4 mb-2">
                  {task.subject && (
                    <div className="flex items-center gap-1">
                      <FiBook className={`w-4 h-4 ${textSecondary}`} />
                      <span className={`text-sm ${textSecondary}`}>
                        {typeof task.subject === 'string' ? task.subject : task.subject.name}
                      </span>
                    </div>
                  )}

                  {task.deadline && (
                    <div className="flex items-center gap-1">
                      <FiCalendar className={`w-4 h-4 ${taskIsOverdue ? 'text-red-500' : textSecondary}`} />
                      <span className={`text-sm ${taskIsOverdue ? 'text-red-500 font-medium' : textSecondary}`}>
                        Due: {new Date(task.deadline).toLocaleDateString()}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center gap-1">
                    <FiClock className={`w-4 h-4 ${textSecondary}`} />
                    <span className={`text-sm ${textSecondary}`}>
                      Created {new Date(task.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                {task.description && (
                  <p className={`text-sm ${textSecondary} line-clamp-2 mb-2`}>
                    {task.description}
                  </p>
                )}

                {/* Topics/Chapters */}
                {(task.chapters?.length > 0 || task.topics?.length > 0) && (
                  <div className="flex flex-wrap gap-1">
                    {task.chapters?.slice(0, 3).map((chapter, index) => (
                      <span key={index} className={`px-2 py-1 text-xs rounded-full ${bgSecondary} ${textSecondary}`}>
                        {typeof chapter === 'string' ? chapter : (chapter?.name || 'Unnamed Chapter')}
                      </span>
                    ))}
                    {task.topics?.slice(0, 3).map((topic, index) => (
                      <span key={index} className={`px-2 py-1 text-xs rounded-full ${bgSecondary} ${textSecondary}`}>
                        {typeof topic === 'string' ? topic : (topic?.name || 'Unnamed Topic')}
                      </span>
                    ))}
                    {(task.chapters?.length > 3 || task.topics?.length > 3) && (
                      <span className={`px-2 py-1 text-xs rounded-full ${bgSecondary} ${textSecondary}`}>
                        +{(task.chapters?.length || 0) + (task.topics?.length || 0) - 3} more
                      </span>
                    )}
                  </div>
                )}
              </div>

              <div className="flex items-center gap-4 ml-4">
                <StatusBadge status={task.status} isOverdue={taskIsOverdue} />

                <button className="text-blue-600 hover:text-blue-700 text-sm flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <FiEye className="w-4 h-4" />
                  View
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <FiLoader className={`w-8 h-8 animate-spin mx-auto mb-4 ${textSecondary}`} />
          <p className={textSecondary}>Loading your tasks...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <FiAlertCircle className="w-8 h-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 dark:text-red-400 mb-4">Failed to load tasks</p>
          <button
            onClick={() => dispatch(fetchTasksByStudent())}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <button
            onClick={handleBackToTasks}
            className={`p-2 rounded-lg ${bgSecondary} hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors`}
          >
            <FiArrowLeft className={`w-5 h-5 ${textPrimary}`} />
          </button>
          <div>
            <h1 className={`text-2xl font-bold ${textPrimary}`}>All Tasks</h1>
            <p className={`text-sm ${textSecondary}`}>
              Complete overview of all your assigned tasks
            </p>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className={`${bgPrimary} rounded-lg border ${borderColor} p-4`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${textSecondary}`}>Total</p>
                <p className={`text-xl font-bold ${textPrimary}`}>{tasks.length}</p>
              </div>
              <FiBook className={`w-6 h-6 text-blue-500`} />
            </div>
          </div>
          <div className={`${bgPrimary} rounded-lg border ${borderColor} p-4`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${textSecondary}`}>Pending</p>
                <p className={`text-xl font-bold ${textPrimary}`}>
                  {tasks.filter(t => t.status === 'pending').length}
                </p>
              </div>
              <FiClock className={`w-6 h-6 text-yellow-500`} />
            </div>
          </div>
          <div className={`${bgPrimary} rounded-lg border ${borderColor} p-4`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${textSecondary}`}>Submitted</p>
                <p className={`text-xl font-bold ${textPrimary}`}>
                  {tasks.filter(t => t.status === 'submitted').length}
                </p>
              </div>
              <FiSend className={`w-6 h-6 text-blue-500`} />
            </div>
          </div>
          <div className={`${bgPrimary} rounded-lg border ${borderColor} p-4`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${textSecondary}`}>Overdue</p>
                <p className={`text-xl font-bold ${textPrimary}`}>
                  {tasks.filter(t => isOverdue(t)).length}
                </p>
              </div>
              <FiAlertCircle className={`w-6 h-6 text-red-500`} />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className={`${bgPrimary} rounded-lg border ${borderColor} p-6 mb-6`}>
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <FiSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${textSecondary}`} />
              <input
                type="text"
                placeholder="Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 border ${borderColor} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${bgPrimary} ${textPrimary}`}
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className={`px-3 py-2 border ${borderColor} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${bgPrimary} ${textPrimary}`}
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="submitted">Submitted</option>
              <option value="graded">Graded</option>
            </select>

            {/* Subject Filter */}
            <select
              value={subjectFilter}
              onChange={(e) => setSubjectFilter(e.target.value)}
              className={`px-3 py-2 border ${borderColor} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${bgPrimary} ${textPrimary}`}
            >
              <option value="all">All Subjects</option>
              {subjects.map(subject => (
                <option key={subject} value={subject}>{subject}</option>
              ))}
            </select>

            {/* Sort By */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className={`px-3 py-2 border ${borderColor} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${bgPrimary} ${textPrimary}`}
            >
              <option value="deadline">Sort by Deadline</option>
              <option value="name">Sort by Name</option>
              <option value="subject">Sort by Subject</option>
              <option value="created">Sort by Created</option>
            </select>

            {/* View Mode Toggle */}
            <div className="flex rounded-lg border border-gray-300 dark:border-gray-600 overflow-hidden">
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-2 ${viewMode === 'grid' ? 'bg-blue-600 text-white' : `${bgSecondary} ${textSecondary}`} transition-colors`}
              >
                <FiGrid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 ${viewMode === 'list' ? 'bg-blue-600 text-white' : `${bgSecondary} ${textSecondary}`} transition-colors`}
              >
                <FiList className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tasks Display */}
      {filteredTasks.length === 0 ? (
        <div className={`${bgPrimary} rounded-lg border ${borderColor} p-12 text-center`}>
          <FiBook className={`w-12 h-12 mx-auto mb-4 ${textSecondary} opacity-50`} />
          <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>No tasks found</h3>
          <p className={`${textSecondary}`}>
            {searchTerm || statusFilter !== 'all' || subjectFilter !== 'all'
              ? 'Try adjusting your filters to see more tasks.'
              : 'You don\'t have any tasks assigned yet.'}
          </p>
        </div>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTasks.map((task) => (
            <TaskCard key={task.id} task={task} onView={handleViewTask} />
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredTasks.map((task) => (
            <TaskListItem key={task.id} task={task} onView={handleViewTask} />
          ))}
        </div>
      )}
    </div>
  );
};

export default StudentTasksAll;
