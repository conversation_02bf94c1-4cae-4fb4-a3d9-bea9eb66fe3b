import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchClassroomById } from "../../store/slices/ClassroomSlice";
import {
  FiHome,
  FiUsers,
  FiClock,
  FiSettings,
  FiShare2,
  FiPlus,
  FiMessageSquare,
  FiActivity,
  FiUserMinus,
  FiUserX,
  FiUserPlus,
  FiUser,
  FiX,
  FiSend
} from "react-icons/fi";
import RequestForm from "./RequestForm";
import CreateTask from "./CreateTask";
import {
  fetchAnnouncements,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement
} from "../../store/slices/AnnouncementSlice";

// Import new components
import InfoTab from "../../features/classroom/teacher/InfoTab";
import UserList from "../../components/ui/UserList";

function ClassInfo({ classroomId: propClassroomId }) {
  const classroomId = propClassroomId;
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState("overview");
  const [showRequestForm, setShowRequestForm] = useState(false);
  const [requestFormFetched, setRequestFormFetched] = useState(false);
  const [showCreateTask, setShowCreateForm] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showAnnouncementForm, setShowAnnouncementForm] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState(null);
  const [announcementForm, setAnnouncementForm] = useState({
    title: '',
    content: ''
  });

  // Redux state with proper data mapping
  const { classroom, loading, error } = useSelector((state) => state.classroom);
  const { announcements, loading: announcementsLoading } = useSelector((state) => state.announcements);

  // Correctly map API response data
  const classroomData = {
    id: classroom?.id || classroomId,
    name: classroom?.name || 'Untitled Classroom',
    description: classroom?.description || '',
    teacherId: classroom?.teacher_id || '',
    // Students array from API
    students: classroom?.students || [],
    studentCount: classroom?.students?.length || 0,
    // Join requests from API
    joinRequests: classroom?.class_requests || [],
    joinRequestCount: classroom?.class_requests?.length || 0,
    // Additional computed data
    isActive: true, // Assume active if we can fetch it
    createdAt: classroom?.created_at,
    updatedAt: classroom?.updated_at,
    // Stats that might come from other endpoints
    assignmentCount: classroom?.assignment_count || 0,
    examCount: classroom?.exam_count || 0,
    announcementCount: announcements?.length || 0
  };

  // Extract student details for display
  const studentsList = classroomData.students.map(student => ({
    id: student.id,
    username: student.username,
    email: student.email,
    mobile: student.mobile,
    country: student.country,
    profilePicture: student.profile_picture,
    isEmailVerified: student.is_email_verified,
    isMobileVerified: student.is_mobile_verified
  }));

  // Extract join request details
  const joinRequestsList = classroomData.joinRequests.map(request => ({
    id: request.id,
    studentId: request.student_id,
    classroomId: request.classroom_id,
    student: {
      id: request.student_user.id,
      username: request.student_user.username,
      email: request.student_user.email,
      mobile: request.student_user.mobile,
      country: request.student_user.country,
      profilePicture: request.student_user.profile_picture,
      isEmailVerified: request.student_user.is_email_verified,
      isMobileVerified: request.student_user.is_mobile_verified
    }
  }));

  // Modern tab configuration with corrected logic
  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: FiHome,
      description: 'Class overview and announcements'
    },
    {
      id: 'students',
      label: 'Students',
      icon: FiUsers,
      description: 'Manage enrolled students',
      badge: classroomData.studentCount
    },
    {
      id: 'invite',
      label: 'Invite Students',
      icon: FiPlus,
      description: 'Send join requests to students',
      badge: classroomData.joinRequestCount > 0 ? classroomData.joinRequestCount : null
    },
  ];

  // Fetch classroom and announcements
  useEffect(() => {
    if (classroomId) {
      dispatch(fetchClassroomById(classroomId));
    }
  }, [dispatch, classroomId]);

  useEffect(() => {
    if (classroomId && (activeTab === "overview" || activeTab === "info")) {
      dispatch(fetchAnnouncements({ classroom_id: classroomId, skip: 0, limit: 100 }));
    }
  }, [dispatch, classroomId, activeTab]);

  // Enhanced handlers - Google Classroom style
  const handleCreateAnnouncement = () => {
    setEditingAnnouncement(null);
    setAnnouncementForm({ title: '', content: '' });
    setShowAnnouncementForm(true);
  };

  const handleEditAnnouncement = (announcement) => {
    setEditingAnnouncement(announcement);
    setAnnouncementForm({ title: announcement.title, content: announcement.content });
    setShowAnnouncementForm(true);
  };

  const handleCancelAnnouncement = () => {
    setShowAnnouncementForm(false);
    setAnnouncementForm({ title: '', content: '' });
    setEditingAnnouncement(null);
  };

  const handleSubmitAnnouncement = async (e) => {
    e.preventDefault();
    if (!announcementForm.content.trim()) {
      alert('Please enter announcement content');
      return;
    }

    try {
      if (editingAnnouncement) {
        await dispatch(updateAnnouncement({
          id: editingAnnouncement.id,
          data: announcementForm
        })).unwrap();
      } else {
        await dispatch(createAnnouncement({
          data: announcementForm,
          classroom_id: classroomId
        })).unwrap();
      }
      setShowAnnouncementForm(false);
      setAnnouncementForm({ title: '', content: '' });
      setEditingAnnouncement(null);
    } catch (error) {
      console.error('Failed to save announcement:', error);
      alert('Failed to save announcement. Please try again.');
    }
  };

  const handleDeleteAnnouncement = async (announcement) => {
    if (window.confirm('Are you sure you want to delete this announcement?')) {
      try {
        await dispatch(deleteAnnouncement(announcement.id)).unwrap();
        // No need to fetch announcements again - Redux state is updated automatically
      } catch (error) {
        console.error('Failed to delete announcement:', error);
        alert('Failed to delete announcement. Please try again.');
      }
    }
  };

  const handleCreateTask = () => {
    setShowCreateForm(true);
  };

  const handleShareClass = () => {
    // Copy classroom link to clipboard
    const classLink = `${window.location.origin}/join/${classroomId}`;
    navigator.clipboard.writeText(classLink);
    // Show success toast
  };

  // User action handlers
  const handleFollowUser = (user) => {
    console.log("Follow user:", user);
    // Implement follow functionality
  };

  const handleVisitProfile = (user) => {
    console.log("Visit profile:", user);
    // Navigate to user profile
    // navigate(`/profile/${user.id}`);
  };



  // Enhanced loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="animate-pulse">
            {/* Header skeleton */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-8 mb-8">
              <div className="flex items-center space-x-6">
                <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-2xl"></div>
                <div className="flex-1">
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-3"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
              </div>
            </div>
            {/* Content skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-white dark:bg-gray-800 rounded-xl p-6">
                  <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Enhanced error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 text-center">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Failed to load classroom</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {typeof error === 'object' ? error.detail || 'An unexpected error occurred' : error}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!classroom) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Classroom not found</h3>
          <p className="text-gray-600 dark:text-gray-400">The classroom you're looking for doesn't exist or you don't have access to it.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      {/* Modern Header with Gradient */}
      <div className="relative bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 dark:from-blue-800 dark:via-blue-900 dark:to-indigo-900">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-black/10 opacity-50"></div>

        <div className="relative max-w-6xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            {/* Enhanced Class Info */}
            <div className="flex items-center space-x-6">
              <div className="relative">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center border border-white/30 shadow-lg">
                  <span className="text-2xl font-bold text-white">
                    {classroomData.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-400 rounded-full border-2 border-white flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              </div>
              <div className="space-y-1">
                <h1 className="text-3xl font-bold text-white tracking-tight">
                  {classroomData.name}
                </h1>
                <p className="text-blue-100 text-lg font-medium">
                  {classroomData.description || 'Test Class'}
                </p>
                <div className="flex items-center space-x-4 text-blue-200 text-sm">
                  <span className="flex items-center space-x-1">
                    <FiUsers className="w-4 h-4" />
                    <span>{classroomData.studentCount} students</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <FiMessageSquare className="w-4 h-4" />
                    <span>{classroomData.announcementCount} announcements</span>
                  </span>
                </div>
              </div>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="flex items-center space-x-3">
              <button
                onClick={handleShareClass}
                className="group flex items-center space-x-2 px-4 py-2.5 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white rounded-xl border border-white/20 transition-all duration-200 hover:scale-105"
              >
                <FiShare2 className="w-5 h-5 group-hover:rotate-12 transition-transform duration-200" />
                <span className="hidden sm:inline font-medium">Share</span>
              </button>
              <button className="group flex items-center space-x-2 px-4 py-2.5 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white rounded-xl border border-white/20 transition-all duration-200 hover:scale-105">
                <FiSettings className="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" />
                <span className="hidden sm:inline font-medium">Settings</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Navigation */}
      <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-b border-gray-200/50 dark:border-gray-700/50 sticky top-0 z-30">
        <div className="max-w-6xl mx-auto px-6">
          <nav className="flex space-x-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`group relative flex items-center space-x-2 px-4 py-4 text-sm font-medium transition-all duration-200 rounded-t-xl ${
                    isActive
                      ? 'text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 shadow-sm border-t-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                  }`}
                >
                  <Icon className={`w-4 h-4 transition-transform duration-200 ${isActive ? 'scale-110' : 'group-hover:scale-105'}`} />
                  <span className="relative">
                    {tab.label}
                    {isActive && (
                      <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-blue-500 rounded-full"></div>
                    )}
                  </span>
                  {tab.badge && tab.badge > 0 && (
                    <span className={`ml-1 px-2 py-0.5 text-xs rounded-full font-semibold transition-colors duration-200 ${
                      isActive
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {tab.badge}
                    </span>
                  )}
                  {!isActive && (
                    <div className="absolute inset-x-0 bottom-0 h-0.5 bg-gray-200 dark:bg-gray-700 scale-x-0 group-hover:scale-x-100 transition-transform duration-200 origin-left"></div>
                  )}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-6 py-8">
        {/* Overview Tab */}
        {activeTab === "overview" && (
          <div className="space-y-8">
            {/* Enhanced Quick Actions */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">Quick Actions</h2>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">Manage your classroom efficiently</p>
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={handleCreateTask}
                    className="group flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                  >
                    <FiPlus className="w-4 h-4 group-hover:rotate-90 transition-transform duration-200" />
                    <span>Create Task</span>
                  </button>

                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-4 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                      <FiUsers className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{classroomData.studentCount}</p>
                      <p className="text-blue-700 dark:text-blue-300 text-sm font-medium">Students</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 rounded-xl p-4 border border-emerald-200 dark:border-emerald-800">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-emerald-600 rounded-lg flex items-center justify-center">
                      <FiMessageSquare className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-emerald-900 dark:text-emerald-100">{classroomData.announcementCount}</p>
                      <p className="text-emerald-700 dark:text-emerald-300 text-sm font-medium">Announcements</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-4 border border-purple-200 dark:border-purple-800">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                      <FiActivity className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">{classroomData.joinRequestCount}</p>
                      <p className="text-purple-700 dark:text-purple-300 text-sm font-medium">Pending Requests</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {showCreateTask && <CreateTask onClose={() => setShowCreateForm(false)} />}

            {/* Announcements - Google Classroom Style */}
            <div className="space-y-6">
              {/* Google Classroom Style Announcement Creator */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                {!showAnnouncementForm ? (
                  /* Collapsed State - Google Classroom Style */
                  <div
                    onClick={handleCreateAnnouncement}
                    className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border-b border-gray-200 dark:border-gray-700"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                        <FiUser className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="text-gray-500 dark:text-gray-400 text-sm bg-gray-100 dark:bg-gray-700 rounded-full px-4 py-2 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                          Share something with your class...
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  /* Expanded State - Google Classroom Style */
                  <form onSubmit={handleSubmitAnnouncement} className="p-4 space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <FiUser className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1 space-y-3">
                        {/* Title Input (Optional) */}
                        <input
                          type="text"
                          value={announcementForm.title}
                          onChange={(e) => setAnnouncementForm({ ...announcementForm, title: e.target.value })}
                          placeholder="Title (optional)"
                          className="w-full px-0 py-2 text-lg font-medium border-0 border-b border-gray-200 dark:border-gray-600 bg-transparent focus:outline-none focus:border-blue-500 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                        />

                        {/* Content Textarea */}
                        <textarea
                          value={announcementForm.content}
                          onChange={(e) => setAnnouncementForm({ ...announcementForm, content: e.target.value })}
                          placeholder="Share something with your class..."
                          rows={4}
                          className="w-full px-0 py-2 border-0 bg-transparent resize-none focus:outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                          required
                          autoFocus
                        />
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                      <button
                        type="button"
                        onClick={handleCancelAnnouncement}
                        className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={!announcementForm.content.trim()}
                        className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center space-x-2"
                      >
                        <FiSend className="w-4 h-4" />
                        <span>{editingAnnouncement ? 'Update' : 'Post'}</span>
                      </button>
                    </div>
                  </form>
                )}
              </div>

              {/* Announcements List */}
              <div className="space-y-4">
                {announcementsLoading ? (
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-8 border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <span className="ml-3 text-gray-600 dark:text-gray-400">Loading announcements...</span>
                    </div>
                  </div>
                ) : announcements && announcements.length > 0 ? (
                  announcements.map((announcement) => (
                    <div key={announcement.id} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                      <div className="p-6">
                        <div className="flex items-start space-x-4">
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                              <FiUser className="w-5 h-5 text-white" />
                            </div>
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div>
                                <h3 className="font-medium text-gray-900 dark:text-gray-100">
                                  {announcement.teacher?.username || 'Teacher'}
                                </h3>
                                <div className="flex items-center space-x-2 mt-1">
                                  <FiClock className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                                  <span className="text-sm text-gray-500 dark:text-gray-400">
                                    {new Date(announcement.created_at).toLocaleDateString('en-US', {
                                      year: 'numeric',
                                      month: 'short',
                                      day: 'numeric',
                                      hour: '2-digit',
                                      minute: '2-digit'
                                    })}
                                  </span>
                                </div>
                              </div>

                              <div className="flex items-center space-x-2">
                                <button
                                  onClick={() => handleEditAnnouncement(announcement)}
                                  className="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
                                >
                                  Edit
                                </button>
                                <button
                                  onClick={() => handleDeleteAnnouncement(announcement)}
                                  className="text-sm text-red-600 hover:text-red-700 transition-colors"
                                >
                                  Delete
                                </button>
                              </div>
                            </div>

                            <div className="mt-4">
                              {announcement.title && (
                                <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                                  {announcement.title}
                                </h4>
                              )}
                              <div className="text-gray-700 dark:text-gray-300 prose prose-sm max-w-none">
                                {announcement.content}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-12 border border-gray-200 dark:border-gray-700 text-center">
                    <FiMessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No announcements yet</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      Create your first announcement to share updates with your students.
                    </p>
                    <button
                      onClick={handleCreateAnnouncement}
                      className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <FiPlus className="w-4 h-4" />
                      <span>Create Announcement</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Students Tab */}
        {activeTab === "students" && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Students ({classroomData.studentCount})
              </h2>
              <button
                onClick={() => setShowInviteModal(true)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
              >
                Invite Student
              </button>
            </div>

            <UserList
              users={studentsList}
              onFollow={handleFollowUser}
              onVisitProfile={handleVisitProfile}
              showEmail={true}
              showPhone={false}
              showLocation={true}
              searchable={true}
              searchPlaceholder="Search students..."
              emptyTitle="No students enrolled"
              emptyDescription="Students will appear here once they join your classroom."
              customActions={[
                {
                  id: 'remove',
                  label: 'Remove from Class',
                  icon: FiUserMinus,
                  onClick: (student) => console.log('Remove student:', student)
                }
              ]}
            />
          </div>
        )}

        {/* Invite Students Tab */}
        {activeTab === "invite" && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Invite Students to Join
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Send join requests to students who aren't in your classroom yet
                </p>
              </div>
              <button
                onClick={() => setShowRequestForm(true)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
              >
                Send Invites
              </button>
            </div>

            {/* Show students who have received invites but haven't joined yet */}
            {classroomData.joinRequestCount > 0 ? (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">
                  Students with Pending Invites ({classroomData.joinRequestCount})
                </h3>
                <UserList
                  users={joinRequestsList.map(request => request.student)}
                  onFollow={handleFollowUser}
                  onVisitProfile={handleVisitProfile}
                  showEmail={true}
                  showPhone={false}
                  showLocation={true}
                  searchable={true}
                  searchPlaceholder="Search invited students..."
                  emptyTitle="No pending invites"
                  emptyDescription="Students you invite will appear here until they accept or decline."
                  emptyIcon={FiClock}
                  customActions={[
                    {
                      id: 'resend',
                      label: 'Resend Invite',
                      icon: FiPlus,
                      onClick: (student) => console.log('Resend invite to:', student)
                    },
                    {
                      id: 'cancel',
                      label: 'Cancel Invite',
                      icon: FiUserX,
                      onClick: (student) => console.log('Cancel invite to:', student)
                    }
                  ]}
                />
              </div>
            ) : (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-12 text-center">
                <FiUsers className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No pending invites</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Start building your class by sending join requests to students.
                </p>
                <button
                  onClick={() => setShowRequestForm(true)}
                  className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  Send Your First Invite
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Request Form Modal */}
      <RequestForm
        show={showRequestForm}
        onClose={() => { setShowRequestForm(false); setRequestFormFetched(false); }}
        classID={classroomId}
        fetched={requestFormFetched}
        setFetched={setRequestFormFetched}
      />

      {/* Invite Student Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Invite Student to Join
                </h3>
                <button
                  onClick={() => setShowInviteModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Share Classroom Code
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={classroomData.id}
                      readOnly
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                    />
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(classroomData.id);
                        // You can add a toast notification here
                      }}
                      className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
                    >
                      Copy
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Students can use this code to request to join your classroom
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Share Join Link
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={`${window.location.origin}/join/${classroomData.id}`}
                      readOnly
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                    />
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(`${window.location.origin}/join/${classroomData.id}`);
                        // You can add a toast notification here
                      }}
                      className="px-3 py-2 bg-emerald-600 hover:bg-emerald-700 text-white text-sm rounded-lg transition-colors"
                    >
                      Copy
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Direct link for students to join your classroom
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowInviteModal(false)}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}


    </div>
  );
}

export default ClassInfo;
