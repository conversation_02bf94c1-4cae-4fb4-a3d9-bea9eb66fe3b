import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  fetchTaskForEdit,
  updateTaskForTeacher,
  selectCurrentTask,
  selectTasksLoading,
  selectTasksError,
  clearTaskState
} from '../../store/slices/TaskSlice';
import { fetchAllOwnClasses } from '../../store/slices/ClassroomSlice';
import { fetchAllStudents } from '../../store/slices/userSlice';
import { DateTimeInput } from '../../components/ui/FormComponents';
import SearchableDropdown from '../../components/common/SearchableDropdown';
import { useDropdownData, useAssignmentState } from '../../hooks/useDropdownData';
import {
  FiArrowLeft,
  FiSave,
  FiLoader,
  FiCalendar,
  FiUsers,
  FiUser,
  FiX,
  FiPlus,
  Fi<PERSON>lertCircle,
  FiCheckCircle
} from 'react-icons/fi';

const EditTask = ({ taskId }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();

  // Redux state
  const task = useSelector(selectCurrentTask);
  const loading = useSelector(selectTasksLoading);
  const error = useSelector(selectTasksError);

  // Local state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [deadline, setDeadline] = useState('');
  const [acceptAfterDeadline, setAcceptAfterDeadline] = useState(false);

  // Custom hooks for dropdown data and assignment state
  const classroomDropdown = useDropdownData('classrooms');
  const studentDropdown = useDropdownData('students');

  const {
    selectedClassrooms,
    selectedStudents,
    formData,
    handleClassroomChange,
    handleStudentChange,
    setFormData
  } = useAssignmentState(
    task?.classrooms || [], // Use classrooms array from task details API
    task?.students || [],   // Use students array from task details API
    task
  );

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-900' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-700' : 'border-gray-300';

  // Load task data
  useEffect(() => {
    if (taskId) {
      dispatch(clearTaskState()); // Clear any previous state
      dispatch(fetchTaskForEdit(taskId));
    }

    return () => {
      dispatch(clearTaskState());
    };
  }, [dispatch, taskId]);

  // Initialize form when task loads
  useEffect(() => {
    if (task) {


      // Handle deadline initialization safely
      let initialDeadline = '';
      if (task.deadline) {
        try {
          const deadlineDate = new Date(task.deadline);
          if (!isNaN(deadlineDate.getTime())) {
            initialDeadline = deadlineDate.toISOString().slice(0, 16);
          }
        } catch (error) {
          console.error('Error parsing task deadline:', task.deadline, error);
        }
      }


      setDeadline(initialDeadline);
      setAcceptAfterDeadline(task.accept_after_deadline || false);
    }
  }, [task]);





  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSuccessMessage('');
    setErrorMessage('');



    try {
      // Validate and format deadline
      let formattedDeadline = null;
      if (deadline && typeof deadline === 'string' && deadline.trim()) {
        const deadlineDate = new Date(deadline);
        if (!isNaN(deadlineDate.getTime())) {
          formattedDeadline = deadlineDate.toISOString();
        } else {
          setErrorMessage('Invalid deadline format. Please select a valid date and time.');
          setIsSubmitting(false);
          return;
        }
      }

      const updateData = {
        deadline: formattedDeadline,
        accept_after_deadline: acceptAfterDeadline,
        add_classroom_ids: formData.add_classroom_ids,
        remove_classroom_ids: formData.remove_classroom_ids,
        add_student_ids: formData.add_student_ids,
        remove_student_ids: formData.remove_student_ids
      };



      await dispatch(updateTaskForTeacher({
        task_id: taskId,
        data: updateData
      })).unwrap();

      setSuccessMessage('Task updated successfully!');

      // Navigate back to task detail after a short delay
      setTimeout(() => {
        navigate(`/teacher/task/${taskId}`);
      }, 1500);

    } catch (error) {
      console.error('Failed to update task:', error);
      const errorMsg = error?.detail || error?.message || 'Failed to update task. Please try again.';
      setErrorMessage(errorMsg);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (loading && !task) {
    return (
      <div className={`min-h-screen ${bgPrimary} flex items-center justify-center`}>
        <div className="text-center">
          <FiLoader className={`w-8 h-8 animate-spin mx-auto mb-4 ${textSecondary}`} />
          <p className={`${textSecondary}`}>Loading task details...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`min-h-screen ${bgPrimary} flex items-center justify-center`}>
        <div className="text-center">
          <FiAlertCircle className="w-8 h-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">Failed to load task details</p>
          <button
            onClick={() => navigate('/teacher/tasks')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Tasks
          </button>
        </div>
      </div>
    );
  }

  if (!task) {
    return null;
  }

  return (
    <main className={`min-h-screen ${bgPrimary}`}>
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <header className="flex items-center gap-4 mb-6">
          <button
            onClick={() => navigate(`/teacher/task/${taskId}`)}
            className={`p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${textSecondary}`}
          >
            <FiArrowLeft className="w-5 h-5" />
          </button>
          <hgroup>
            <h1 className={`text-2xl font-bold ${textPrimary}`}>Edit Task</h1>
            <p className={`${textSecondary}`}>{task.name}</p>
          </hgroup>
        </header>

        {/* Success Message */}
        {successMessage && (
          <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
              <FiCheckCircle className="w-5 h-5" />
              <span>{successMessage}</span>
            </div>
          </div>
        )}

        {/* Error Message */}
        {errorMessage && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
              <FiAlertCircle className="w-5 h-5" />
              <span>{errorMessage}</span>
            </div>
          </div>
        )}

        {/* Edit Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Task Information Card */}
          <div className={`${bgSecondary} rounded-lg border ${borderColor} p-6`}>
            <h2 className={`text-lg font-semibold ${textPrimary} mb-4`}>Task Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className={`block text-sm font-medium ${textPrimary} mb-1`}>
                  Task Name
                </label>
                <input
                  type="text"
                  value={task.name}
                  disabled
                  className={`w-full px-3 py-2 border ${borderColor} rounded-lg bg-gray-100 dark:bg-gray-700 ${textSecondary} cursor-not-allowed`}
                />
              </div>
              
              <div>
                <label className={`block text-sm font-medium ${textPrimary} mb-1`}>
                  Subject
                </label>
                <input
                  type="text"
                  value={task.subject?.name || 'No subject'}
                  disabled
                  className={`w-full px-3 py-2 border ${borderColor} rounded-lg bg-gray-100 dark:bg-gray-700 ${textSecondary} cursor-not-allowed`}
                />
              </div>
            </div>

            <div>
              <label className={`block text-sm font-medium ${textPrimary} mb-1`}>
                Description
              </label>
              <textarea
                value={task.description || ''}
                disabled
                rows={3}
                className={`w-full px-3 py-2 border ${borderColor} rounded-lg bg-gray-100 dark:bg-gray-700 ${textSecondary} cursor-not-allowed resize-none`}
              />
            </div>
          </div>

          {/* Deadline Settings */}
          <div className={`${bgSecondary} rounded-lg border ${borderColor} p-6`}>
            <h2 className={`text-lg font-semibold ${textPrimary} mb-4 flex items-center gap-2`}>
              <FiCalendar className="w-5 h-5" />
              Deadline Settings
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium ${textPrimary} mb-1`}>
                  Deadline
                </label>
                <DateTimeInput
                  value={deadline}
                  onChange={(e) => setDeadline(e.target.value)}
                  className="w-full"
                />
                <p className={`text-xs ${textSecondary} mt-1`}>
                  Leave empty to remove deadline
                </p>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="accept_after_deadline"
                  checked={acceptAfterDeadline}
                  onChange={(e) => setAcceptAfterDeadline(e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label htmlFor="accept_after_deadline" className={`text-sm ${textPrimary}`}>
                  Accept submissions after deadline
                </label>
              </div>
            </div>
          </div>

          {/* Assignment Instructions */}
          <div className={`${bgSecondary} rounded-lg border ${borderColor} p-4 mb-6`}>
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-blue-600 dark:text-blue-400 text-sm font-medium">i</span>
              </div>
              <div>
                <h3 className={`font-medium ${textPrimary} mb-1`}>Assignment Options</h3>
                <p className={`text-sm ${textSecondary}`}>
                  You can assign this task to either <strong>entire classrooms</strong> or <strong>individual students</strong>, but not both.
                  Selecting classrooms will automatically include all students in those classrooms.
                </p>
              </div>
            </div>
          </div>

          {/* Classroom Assignments */}
          <div className={`${bgSecondary} rounded-lg border ${borderColor} p-6 ${
            selectedStudents.length > 0 ? 'opacity-75' : ''
          }`}>
            <h2 className={`text-lg font-semibold ${textPrimary} mb-4 flex items-center gap-2`}>
              <FiUsers className="w-5 h-5" />
              Classroom Assignments
              {selectedClassrooms.length > 0 && (
                <span className="ml-2 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
                  {selectedClassrooms.length} selected
                </span>
              )}
            </h2>

            <SearchableDropdown
              options={classroomDropdown.options}
              selectedValues={selectedClassrooms}
              onSelectionChange={handleClassroomChange}
              onSearch={classroomDropdown.onSearch}
              onLoadMore={classroomDropdown.onLoadMore}
              loading={classroomDropdown.loading}
              hasMore={classroomDropdown.hasMore}
              multiple={true}
              placeholder={selectedStudents.length > 0
                ? "Cannot select classrooms when students are assigned"
                : "Select classrooms to assign..."
              }
              searchPlaceholder="Search classrooms..."
              label="Assign to Classrooms"
              displayKey="name"
              valueKey="id"
              disabled={selectedStudents.length > 0}
            />

            {selectedStudents.length > 0 && (
              <div className={`mt-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800`}>
                <div className="flex items-center justify-between">
                  <p className={`text-sm text-orange-700 dark:text-orange-300 flex items-center gap-2`}>
                    <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                    Individual students are assigned. Remove student assignments to assign classrooms.
                  </p>
                  <button
                    type="button"
                    onClick={() => handleStudentChange([])}
                    className="px-3 py-1 text-xs bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors"
                  >
                    Clear Students
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Individual Student Assignments */}
          <div className={`${bgSecondary} rounded-lg border ${borderColor} p-6 ${
            selectedClassrooms.length > 0 ? 'opacity-75' : ''
          }`}>
            <h2 className={`text-lg font-semibold ${textPrimary} mb-4 flex items-center gap-2`}>
              <FiUser className="w-5 h-5" />
              Individual Student Assignments
              {selectedStudents.length > 0 && (
                <span className="ml-2 px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 rounded-full text-xs font-medium">
                  {selectedStudents.length} selected
                </span>
              )}
            </h2>


            <SearchableDropdown
              options={studentDropdown.options}
              selectedValues={selectedStudents}
              onSelectionChange={handleStudentChange}
              onSearch={studentDropdown.onSearch}
              onLoadMore={studentDropdown.onLoadMore}
              loading={studentDropdown.loading}
              hasMore={studentDropdown.hasMore}
              multiple={true}
              placeholder={selectedClassrooms.length > 0
                ? "Cannot select students when classrooms are assigned"
                : "Select students to assign..."
              }
              searchPlaceholder="Search students..."
              label="Assign to Individual Students"
              displayKey="name"
              valueKey="id"
              disabled={selectedClassrooms.length > 0}
            />

            {selectedClassrooms.length > 0 && (
              <div className={`mt-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800`}>
                <div className="flex items-center justify-between">
                  <p className={`text-sm text-orange-700 dark:text-orange-300 flex items-center gap-2`}>
                    <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                    Classrooms are assigned. Remove classroom assignments to assign individual students.
                  </p>
                  <button
                    type="button"
                    onClick={() => handleClassroomChange([])}
                    className="px-3 py-1 text-xs bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors"
                  >
                    Clear Classrooms
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex items-center justify-end gap-4 pt-6">
            <button
              type="button"
              onClick={() => navigate(`/teacher/task/${taskId}`)}
              className={`px-6 py-2 border ${borderColor} rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${textPrimary}`}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <FiLoader className="w-4 h-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <FiSave className="w-4 h-4" />
                  Update Task
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </main>
  );
};

export default EditTask;
