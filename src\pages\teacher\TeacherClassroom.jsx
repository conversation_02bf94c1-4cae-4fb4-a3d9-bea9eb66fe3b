import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { fetchClassroomById, fetchStudentsInClassroom } from '../../store/slices/ClassroomSlice';
import {
  fetchAnnouncements,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement
} from '../../store/slices/AnnouncementSlice';
import { useThemeProvider } from '../../providers/ThemeContext';
import UserList from '../../components/ui/UserList';
import {
  FiHome,
  FiUsers,
  FiBookOpen,
  FiFileText,
  FiSettings,
  FiMoreVertical,
  FiCopy,
  FiShare2,
  FiArrowLeft,
  FiPlus,
  FiEdit3,
  FiTrash2,
  FiUser,
  FiMessageSquare,
  FiSend,
  FiX,
  FiCalendar,
  FiClock,
  FiEye,
  FiDownload
} from 'react-icons/fi';

function TeacherClassroom() {
  const { classroomId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentTheme } = useThemeProvider();
  
  const { classroom, students, loading, error } = useSelector((state) => state.classroom);
  const { announcements, loading: announcementsLoading } = useSelector((state) => state.announcements);
  const { currentUser } = useSelector((state) => state.users);
  
  const [activeTab, setActiveTab] = useState('stream');
  const [showClassCode, setShowClassCode] = useState(false);
  const [showAnnouncementForm, setShowAnnouncementForm] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState(null);
  const [announcementForm, setAnnouncementForm] = useState({
    title: '',
    content: ''
  });

  // Theme classes
  const bgPrimary = currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50";
  const bgSecondary = currentTheme === "dark" ? "bg-gray-800" : "bg-white";
  const textPrimary = currentTheme === "dark" ? "text-gray-100" : "text-gray-900";
  const textSecondary = currentTheme === "dark" ? "text-gray-400" : "text-gray-600";
  const borderColor = currentTheme === "dark" ? "border-gray-700" : "border-gray-200";

  useEffect(() => {
    if (classroomId) {
      dispatch(fetchClassroomById(classroomId));
      dispatch(fetchStudentsInClassroom(classroomId));
      dispatch(fetchAnnouncements({ classroom_id: classroomId, skip: 0, limit: 100 }));
    }
  }, [dispatch, classroomId]);

  const tabs = [
    { id: 'stream', label: 'Stream', icon: FiHome },
    { id: 'classwork', label: 'Classwork', icon: FiBookOpen },
    { id: 'people', label: 'People', icon: FiUsers },
    { id: 'grades', label: 'Grades', icon: FiFileText }
  ];

  const copyClassCode = () => {
    if (classroom?.class_code) {
      navigator.clipboard.writeText(classroom.class_code);
    }
  };

  const handleCreateAnnouncement = () => {
    setEditingAnnouncement(null);
    setAnnouncementForm({ title: '', content: '' });
    setShowAnnouncementForm(true);
  };

  const handleEditAnnouncement = (announcement) => {
    setEditingAnnouncement(announcement);
    setAnnouncementForm({ title: announcement.title, content: announcement.content });
    setShowAnnouncementForm(true);
  };

  const handleSubmitAnnouncement = async (e) => {
    e.preventDefault();
    try {
      if (editingAnnouncement) {
        await dispatch(updateAnnouncement({ 
          id: editingAnnouncement.id, 
          data: announcementForm 
        })).unwrap();
      } else {
        await dispatch(createAnnouncement({ 
          data: announcementForm, 
          classroom_id: classroomId 
        })).unwrap();
      }
      setShowAnnouncementForm(false);
      setAnnouncementForm({ title: '', content: '' });
      dispatch(fetchAnnouncements({ classroom_id: classroomId, skip: 0, limit: 100 }));
    } catch (error) {
      console.error('Failed to save announcement:', error);
    }
  };

  const handleDeleteAnnouncement = async (announcementId) => {
    if (window.confirm('Are you sure you want to delete this announcement?')) {
      try {
        await dispatch(deleteAnnouncement(announcementId)).unwrap();
        // No need to fetch announcements again - Redux state is updated automatically
      } catch (error) {
        console.error('Failed to delete announcement:', error);
        alert('Failed to delete announcement. Please try again.');
      }
    }
  };

  if (loading) {
    return (
      <div className={`min-h-screen ${bgPrimary} flex items-center justify-center`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !classroom) {
    return (
      <div className={`min-h-screen ${bgPrimary} flex items-center justify-center`}>
        <div className="text-center">
          <h2 className={`text-xl font-semibold ${textPrimary} mb-2`}>Classroom not found</h2>
          <p className={textSecondary}>The classroom you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/teacher/classes')}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Classes
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${bgPrimary}`}>
      {/* Header */}
      <div className={`${bgSecondary} border-b ${borderColor} sticky top-0 z-40`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/teacher/classes')}
                className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 ${textSecondary} hover:${textPrimary} transition-colors`}
              >
                <FiArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className={`text-lg font-medium ${textPrimary} truncate max-w-md`}>
                  {classroom.name}
                </h1>
                <p className={`text-sm ${textSecondary} truncate max-w-md`}>
                  {classroom.subject?.name}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowClassCode(!showClassCode)}
                className={`px-3 py-1.5 text-sm border ${borderColor} rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${textSecondary}`}
              >
                Class code
              </button>
              <button className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 ${textSecondary} transition-colors`}>
                <FiSettings className="w-5 h-5" />
              </button>
              <button className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 ${textSecondary} transition-colors`}>
                <FiMoreVertical className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Class Code Dropdown */}
      {showClassCode && (
        <div className={`${bgSecondary} border-b ${borderColor} px-4 py-3`}>
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${textSecondary} mb-1`}>Class code</p>
                <p className={`text-lg font-mono ${textPrimary}`}>{classroom.class_code || 'No code available'}</p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={copyClassCode}
                  className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <FiCopy className="w-4 h-4" />
                  <span>Copy</span>
                </button>
                <button className="flex items-center space-x-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <FiShare2 className="w-4 h-4" />
                  <span>Share</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className={`${bgSecondary} border-b ${borderColor}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-600 text-blue-600'
                      : `border-transparent ${textSecondary} hover:${textPrimary} hover:border-gray-300`
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {activeTab === 'stream' && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-3 space-y-6">
              {/* Class Header Card */}
              <div 
                className={`${bgSecondary} rounded-lg overflow-hidden shadow-sm border ${borderColor}`}
                style={{
                  background: `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`,
                }}
              >
                <div className="p-6 text-white">
                  <h2 className="text-2xl font-bold mb-2">{classroom.name}</h2>
                  <p className="text-blue-100 mb-4">{classroom.description}</p>
                  <div className="flex items-center space-x-4 text-sm text-blue-100">
                    <span>{classroom.subject?.name}</span>
                    <span>•</span>
                    <span>{students?.length || 0} students</span>
                  </div>
                </div>
              </div>

              {/* Create Announcement */}
              <div className={`${bgSecondary} rounded-lg border ${borderColor} p-4`}>
                <button
                  onClick={handleCreateAnnouncement}
                  className={`w-full text-left p-4 rounded-lg border-2 border-dashed ${borderColor} hover:border-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors ${textSecondary} hover:${textPrimary}`}
                >
                  <div className="flex items-center space-x-3">
                    <FiPlus className="w-5 h-5" />
                    <span>Share something with your class</span>
                  </div>
                </button>
              </div>

              {/* Announcements */}
              <div className="space-y-4">
                {announcementsLoading ? (
                  <div className={`${bgSecondary} rounded-lg p-6 border ${borderColor}`}>
                    <div className="animate-pulse space-y-3">
                      <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
                    </div>
                  </div>
                ) : announcements && announcements.length > 0 ? (
                  announcements.map((announcement) => (
                    <div key={announcement.id} className={`${bgSecondary} rounded-lg border ${borderColor} overflow-hidden`}>
                      <div className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3 flex-1">
                            <div className="flex-shrink-0">
                              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                                <FiUser className="w-5 h-5 text-white" />
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2 mb-2">
                                <p className={`text-sm font-medium ${textPrimary}`}>
                                  {currentUser?.username || 'You'}
                                </p>
                                <span className={`text-xs ${textSecondary}`}>
                                  {new Date(announcement.created_at).toLocaleDateString()}
                                </span>
                              </div>
                              <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>
                                {announcement.title}
                              </h3>
                              <p className={`${textSecondary} whitespace-pre-line`}>
                                {announcement.content}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            <button
                              onClick={() => handleEditAnnouncement(announcement)}
                              className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 ${textSecondary} hover:${textPrimary} transition-colors`}
                            >
                              <FiEdit3 className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteAnnouncement(announcement.id)}
                              className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-red-500 hover:text-red-600 transition-colors`}
                            >
                              <FiTrash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className={`${bgSecondary} rounded-lg p-12 text-center border ${borderColor}`}>
                    <FiMessageSquare className={`w-12 h-12 ${textSecondary} mx-auto mb-4`} />
                    <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>No announcements yet</h3>
                    <p className={textSecondary}>Share something with your class to get started.</p>
                  </div>
                )}
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Actions */}
              <div className={`${bgSecondary} rounded-lg p-4 border ${borderColor}`}>
                <h3 className={`text-sm font-medium ${textPrimary} mb-3`}>Quick Actions</h3>
                <div className="space-y-2">
                  <button className="w-full text-left p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center space-x-2">
                    <FiPlus className="w-4 h-4 text-blue-600" />
                    <span className={`text-sm ${textPrimary}`}>Create assignment</span>
                  </button>
                  <button className="w-full text-left p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center space-x-2">
                    <FiFileText className="w-4 h-4 text-green-600" />
                    <span className={`text-sm ${textPrimary}`}>Create quiz</span>
                  </button>
                  <button className="w-full text-left p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center space-x-2">
                    <FiUsers className="w-4 h-4 text-purple-600" />
                    <span className={`text-sm ${textPrimary}`}>Invite students</span>
                  </button>
                </div>
              </div>

              {/* Class Statistics */}
              <div className={`${bgSecondary} rounded-lg p-4 border ${borderColor}`}>
                <h3 className={`text-sm font-medium ${textPrimary} mb-3`}>Class Statistics</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className={textSecondary}>Total Students:</span>
                    <span className={textPrimary}>{students?.length || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className={textSecondary}>Announcements:</span>
                    <span className={textPrimary}>{announcements?.length || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className={textSecondary}>Created:</span>
                    <span className={textPrimary}>
                      {new Date(classroom.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'classwork' && (
          <div className="space-y-6">
            <div className={`${bgSecondary} rounded-lg p-6 border ${borderColor}`}>
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-xl font-semibold ${textPrimary}`}>Classwork</h2>
                <div className="flex space-x-2">
                  <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <FiPlus className="w-4 h-4" />
                    <span>Create</span>
                  </button>
                </div>
              </div>

              {/* Filter Tabs */}
              <div className="flex space-x-4 mb-6 border-b border-gray-200 dark:border-gray-700">
                <button className="pb-2 border-b-2 border-blue-600 text-blue-600 font-medium">All</button>
                <button className={`pb-2 ${textSecondary} hover:${textPrimary}`}>Assignments</button>
                <button className={`pb-2 ${textSecondary} hover:${textPrimary}`}>Quizzes</button>
                <button className={`pb-2 ${textSecondary} hover:${textPrimary}`}>Materials</button>
              </div>

              {/* Classwork Items */}
              <div className="space-y-4">
                {/* Sample Assignment */}
                <div className={`border ${borderColor} rounded-lg p-4 hover:shadow-md transition-shadow`}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                          <FiFileText className="w-5 h-5 text-green-600" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className={`font-medium ${textPrimary} mb-1`}>Sample Assignment</h3>
                        <p className={`text-sm ${textSecondary} mb-2`}>
                          Complete the reading assignment and submit your analysis.
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <span>Due: Tomorrow, 11:59 PM</span>
                          <span>100 points</span>
                          <span>5 submissions</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 ${textSecondary}`}>
                        <FiEdit3 className="w-4 h-4" />
                      </button>
                      <button className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 ${textSecondary}`}>
                        <FiEye className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* No Classwork State */}
                <div className="text-center py-12">
                  <FiBookOpen className={`w-12 h-12 ${textSecondary} mx-auto mb-4`} />
                  <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>No classwork yet</h3>
                  <p className={textSecondary}>Create assignments, quizzes, and materials for your students.</p>
                  <button className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Create your first assignment
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'people' && (
          <div className="space-y-6">
            <div className={`${bgSecondary} rounded-lg p-6 border ${borderColor}`}>
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-xl font-semibold ${textPrimary}`}>People</h2>
                <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  <FiUsers className="w-4 h-4" />
                  <span>Invite students</span>
                </button>
              </div>

              {/* Teachers Section */}
              <div className="mb-8">
                <h3 className={`text-lg font-medium ${textPrimary} mb-4 flex items-center`}>
                  <span>Teachers</span>
                  <span className={`ml-2 text-sm ${textSecondary}`}>1</span>
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium">
                          {currentUser?.username?.charAt(0).toUpperCase() || 'T'}
                        </span>
                      </div>
                      <div>
                        <p className={`font-medium ${textPrimary}`}>
                          {currentUser?.username || 'You'} (You)
                        </p>
                        <p className={`text-sm ${textSecondary}`}>
                          {currentUser?.email || '<EMAIL>'}
                        </p>
                      </div>
                    </div>
                    <button className={`p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 ${textSecondary}`}>
                      <FiMoreVertical className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Students Section */}
              <div>
                <h3 className={`text-lg font-medium ${textPrimary} mb-4 flex items-center`}>
                  <span>Students</span>
                  <span className={`ml-2 text-sm ${textSecondary}`}>{students?.length || 0}</span>
                </h3>

                <UserList
                  users={students || []}
                  onFollow={(user) => console.log('Follow student:', user)}
                  onVisitProfile={(user) => console.log('Visit profile:', user)}
                  showEmail={true}
                  showPhone={false}
                  showLocation={false}
                  searchable={false} // Keep it simple in this context
                  emptyTitle="No students yet"
                  emptyDescription="Invite students to join your class."
                  emptyIcon={FiUsers}
                  size="md"
                  className={currentTheme === "dark" ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200"}
                  customActions={[
                    {
                      id: 'message',
                      label: 'Send Message',
                      icon: FiMoreVertical,
                      onClick: (student) => console.log('Message student:', student)
                    }
                  ]}
                />
              </div>
            </div>
          </div>
        )}

        {activeTab === 'grades' && (
          <div className="space-y-6">
            <div className={`${bgSecondary} rounded-lg p-6 border ${borderColor}`}>
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-xl font-semibold ${textPrimary}`}>Grades</h2>
                <div className="flex space-x-2">
                  <button className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <FiDownload className="w-4 h-4 inline mr-2" />
                    Export
                  </button>
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Grade book
                  </button>
                </div>
              </div>

              {/* Grade Overview */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">Class Average</h3>
                  <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">B+</p>
                  <p className="text-sm text-blue-600 dark:text-blue-400">87.3%</p>
                </div>
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-green-800 dark:text-green-200">Assignments</h3>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">12</p>
                  <p className="text-sm text-green-600 dark:text-green-400">Total</p>
                </div>
                <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-purple-800 dark:text-purple-200">Submissions</h3>
                  <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">89%</p>
                  <p className="text-sm text-purple-600 dark:text-purple-400">On time</p>
                </div>
                <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-orange-800 dark:text-orange-200">Pending</h3>
                  <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">3</p>
                  <p className="text-sm text-orange-600 dark:text-orange-400">To grade</p>
                </div>
              </div>

              {/* Recent Submissions */}
              <div>
                <h3 className={`text-lg font-medium ${textPrimary} mb-4`}>Recent Submissions</h3>
                <div className="space-y-3">
                  <div className={`border ${borderColor} rounded-lg p-4 hover:shadow-sm transition-shadow`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className={`font-medium ${textPrimary}`}>Assignment 1 - John Doe</h4>
                        <p className={`text-sm ${textSecondary}`}>Submitted 2 hours ago</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="px-2 py-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 rounded text-xs">
                          Pending
                        </span>
                        <button className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors">
                          Grade
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* No Grades State */}
              <div className="text-center py-12">
                <FiFileText className={`w-12 h-12 ${textSecondary} mx-auto mb-4`} />
                <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>No grades yet</h3>
                <p className={textSecondary}>Create assignments to start grading student work.</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Announcement Modal */}
      {showAnnouncementForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setShowAnnouncementForm(false)} />
          <div className={`relative ${bgSecondary} rounded-lg shadow-xl w-full max-w-md p-6`}>
            <div className="flex items-center justify-between mb-4">
              <h2 className={`text-lg font-semibold ${textPrimary}`}>
                {editingAnnouncement ? 'Edit Announcement' : 'Create Announcement'}
              </h2>
              <button
                onClick={() => setShowAnnouncementForm(false)}
                className={`p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 ${textSecondary}`}
              >
                <FiX className="w-5 h-5" />
              </button>
            </div>
            
            <form onSubmit={handleSubmitAnnouncement} className="space-y-4">
              <div>
                <label className={`block text-sm font-medium ${textPrimary} mb-2`}>
                  Title
                </label>
                <input
                  type="text"
                  value={announcementForm.title}
                  onChange={(e) => setAnnouncementForm({ ...announcementForm, title: e.target.value })}
                  className={`w-full px-3 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgSecondary}`}
                  placeholder="Enter announcement title"
                  required
                />
              </div>
              
              <div>
                <label className={`block text-sm font-medium ${textPrimary} mb-2`}>
                  Content
                </label>
                <textarea
                  value={announcementForm.content}
                  onChange={(e) => setAnnouncementForm({ ...announcementForm, content: e.target.value })}
                  rows={4}
                  className={`w-full px-3 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgSecondary}`}
                  placeholder="Write your announcement..."
                  required
                />
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowAnnouncementForm(false)}
                  className={`px-4 py-2 border ${borderColor} rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${textSecondary}`}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <FiSend className="w-4 h-4" />
                  <span>{editingAnnouncement ? 'Update' : 'Post'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default TeacherClassroom;
