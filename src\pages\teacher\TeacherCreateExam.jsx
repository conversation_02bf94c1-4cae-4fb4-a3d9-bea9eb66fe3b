import React, { useState, useMemo, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { useThemeProvider } from "../../providers/ThemeContext";

import { fetchAllOwnClasses } from "../../store/slices/ClassroomSlice";
import { fetchSubjects } from "../../store/slices/SubjectSlice";
import { fetchChaptersBySubject } from "../../store/slices/ChapterSlice";
import { fetchTopicsByChapter } from "../../store/slices/TopicSlice";
import { fetchSubtopicsByTopic } from "../../store/slices/SubtopicSlice";
import { fetchClasses } from "../../store/slices/ClassesSlice";
import { aiGenerateQuestions } from "../../store/slices/QuestionSlice";
import { createExamWithAssignment, fetchTeacherExamById, setCurrentExam, updateExam } from "../../store/slices/ExamSlice";

// Import new components
import ExamDetailsForm from "../../components/exam/ExamDetailsForm";
import QuestionForm from "../../components/exam/QuestionForm";
import QuestionList from "../../components/exam/QuestionList";
import StepIndicator from "../../components/exam/StepIndicator";
import ProgressBar from "../../components/exam/ProgressBar";
import StudentAssignmentSelector from "../../components/exam/StudentAssignmentSelector";


import {
  FiSave,
  FiArrowRight,
  FiArrowLeft,
  FiCheck
} from "react-icons/fi";

const initialExamState = {
  title: "",
  description: "",
  total_marks: 0,
  total_duration: 0,
  start_time: "",
  question_ids: [],
};

const initialQuestionState = {
  text: "",
  answer: "",
  Type: "MCQS",
  Level: "EASY",
  imageUrl: "",
  marks: 1,
  options: [
    { option_text: "", is_correct: false },
    { option_text: "", is_correct: false },
    { option_text: "", is_correct: false },
    { option_text: "", is_correct: false },
  ],
};

const initialDescriptiveState = {
  text: "",
  answer: "",
  Type: "LONG", // Default to LONG for descriptive questions
  Level: "EASY",
  imageUrl: "",
  marks: 1,
  options: [],
};

export default function TeacherCreateExam() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { examId } = useParams();
  const { currentTheme } = useThemeProvider();

  // Extract exam ID from URL path since useParams might not work with DashboardLayout routing
  const pathParts = location.pathname.split('/');
  console.log('🔍 URL path parts:', pathParts);

  // Look for exam ID in different positions based on URL pattern
  let extractedExamId = null;

  // Pattern: /teacher/exam/{examId}/edit
  if (pathParts[1] === 'teacher' && pathParts[2] === 'exam' && pathParts[4] === 'edit') {
    extractedExamId = pathParts[3];
  }
  // Pattern: /teacher/edit-exam/{examId}
  else if (pathParts[1] === 'teacher' && pathParts[2] === 'edit-exam') {
    extractedExamId = pathParts[3];
  }
  // Fallback: find any part that looks like an ID (contains alphanumeric characters)
  else {
    extractedExamId = pathParts.find(part =>
      part &&
      part.length > 5 &&
      /^[a-zA-Z0-9\-_]+$/.test(part) &&
      part !== 'teacher' &&
      part !== 'exam' &&
      part !== 'edit' &&
      part !== 'create-exam'
    );
  }

  const finalExamId = examId || extractedExamId;
  console.log('🆔 Extracted exam ID:', { examId, extractedExamId, finalExamId });

  // Determine if we're in edit mode
  const isEditMode = Boolean(finalExamId) && location.pathname.includes('/edit');
  console.log('🔧 Edit mode detection:', { finalExamId, pathname: location.pathname, isEditMode });

  const { currentExam: existingExam, loading: examLoading, error: examError } = useSelector((state) => state.exams);

  // Memoize theme classes to prevent unnecessary re-renders
  const themeClasses = useMemo(() => ({
    bg: currentTheme === "dark" ? "bg-gray-900" : "bg-white",
    text: currentTheme === "dark" ? "text-gray-100" : "text-gray-900",
    input: currentTheme === "dark" ? "bg-gray-800 text-gray-100 border-gray-700" : "bg-gray-50 text-gray-900 border-gray-300",
    label: currentTheme === "dark" ? "text-gray-300" : "text-gray-700"
  }), [currentTheme]);

  // Redux state for classrooms, subjects, chapters
  const { classrooms } = useSelector((state) => state.classroom);
  const { subjects } = useSelector((state) => state.subjects);
  const { chaptersBySubject = [], loading: chaptersLoading } = useSelector((state) => state.chapters);
  const { topicsByChapter = [], loading: topicsLoading } = useSelector((state) => state.topics);
  const { subtopicsByTopic = [], loading: subtopicsLoading } = useSelector((state) => state.subtopics);
  const { classes = [], loading: classesLoading } = useSelector((state) => state.classes);


  // Step state - Step 1: Exam Details, Step 2: Add Questions
  const [step, setStep] = useState(1);
  const [questionType, setQuestionType] = useState("MCQS");
  const [exam, setExam] = useState(initialExamState);
  const [questions, setQuestions] = useState([]); // created question objects
  const [questionForm, setQuestionForm] = useState(initialQuestionState);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [progress, setProgress] = useState(0);
  const [classId, setClassId] = useState("");
  const [subjectId, setSubjectId] = useState("");
  const [classNumber, setClassNumber] = useState(""); // Add class number state
  const [classNumberId, setClassNumberId] = useState(""); // Add class number ID state
  const [chapterId, setChapterId] = useState("");
  const [topicId, setTopicId] = useState("");
  const [subtopicId, setSubtopicId] = useState("");
  const [descType, setDescType] = useState("SHORT");
  const [examDetailsValid, setExamDetailsValid] = useState(false);

  // AI Generation options
  const [aiNoOfQuestions, setAiNoOfQuestions] = useState(3);
  const [aiDifficultyMode, setAiDifficultyMode] = useState("mix"); // "mix" or "custom"
  const [aiNoOfEasy, setAiNoOfEasy] = useState(1);
  const [aiNoOfMedium, setAiNoOfMedium] = useState(1);
  const [aiNoOfHard, setAiNoOfHard] = useState(1);

  // Editing state for questions
  const [editingIndex, setEditingIndex] = useState(-1);
  const [editingQuestion, setEditingQuestion] = useState(null);

  // Assignment state
  const [assignmentType, setAssignmentType] = useState("classroom"); // "classroom" or "students"
  const [selectedStudentIds, setSelectedStudentIds] = useState([]);



  // Fetch classrooms and classes on mount
  React.useEffect(() => {
    console.log('🏫 Initial data fetch useEffect triggered');
    console.log('🔄 Fetching classrooms and classes');
    dispatch(fetchAllOwnClasses());
    dispatch(fetchClasses());
  }, [dispatch]);

  // Load existing exam data when in edit mode
  React.useEffect(() => {
    console.log('🔍 Edit mode useEffect triggered:', { isEditMode, finalExamId, hasLocationState: !!location.state?.examData });

    if (isEditMode && finalExamId) {
      // Clear any existing exam data to ensure fresh load
      dispatch(setCurrentExam(null));

      // First, check if exam data was passed through navigation state
      const examFromState = location.state?.examData;
      if (examFromState) {
        console.log('📋 Using exam data from navigation state for editing:', examFromState);

        // Set the exam data in Redux state for consistency
        dispatch(setCurrentExam(examFromState));

        // Directly populate the form with the exam data
        setExam({
          title: examFromState.title || "",
          description: examFromState.description || "",
          total_marks: examFromState.total_marks || 0,
          total_duration: examFromState.total_duration || 0,
          start_time: examFromState.start_time ? new Date(examFromState.start_time).toISOString() : "",
          question_ids: examFromState.question_ids || [],
        });

        // Set questions if they exist
        if (examFromState.questions && examFromState.questions.length > 0) {
          setQuestions(examFromState.questions);
        }

        // Set assignment type and selected students based on exam data
        if (examFromState.assignment_info) {
          const { assignment_type } = examFromState.assignment_info;
          setAssignmentType(assignment_type === 'classroom' ? 'classroom' : 'students');

          // Set selected students for individual assignments
          if (assignment_type === 'individual' && examFromState.assigned_students) {
            const studentIds = examFromState.assigned_students.map(student => student.student_id);
            setSelectedStudentIds(studentIds);
          }
        }

        // Set class number from exam data
        if (examFromState.class_number) {
          setClassNumber(examFromState.class_number);
        }

        // Set subject and class from questions data
        if (examFromState.questions && examFromState.questions.length > 0) {
          const firstQuestion = examFromState.questions[0];
          if (firstQuestion.subject_id) {
            setSubjectId(firstQuestion.subject_id);
          }
          if (firstQuestion.class_id) {
            setClassId(firstQuestion.class_id);
          }
        }

        return;
      }

      // Always fetch fresh data from API if no navigation state data
      console.log('🔄 Fetching exam data via API for exam ID:', finalExamId);
      dispatch(fetchTeacherExamById(finalExamId));
    }
  }, [dispatch, isEditMode, finalExamId, location.state, location.key]);

  // Populate form with existing exam data when loaded (fallback for API data)
  React.useEffect(() => {
    console.log('📦 API data useEffect triggered:', {
      isEditMode,
      hasExistingExam: !!existingExam,
      examId: existingExam?.id,
      finalExamId,
      hasLocationState: !!location.state?.examData
    });

    if (isEditMode && existingExam && existingExam.id === finalExamId && !location.state?.examData) {
      console.log('📋 Populating form with API data:', existingExam);

      setExam({
        title: existingExam.title || "",
        description: existingExam.description || "",
        total_marks: existingExam.total_marks || 0,
        total_duration: existingExam.total_duration || 0,
        start_time: existingExam.start_time ? new Date(existingExam.start_time).toISOString() : "",
        question_ids: existingExam.question_ids || [],
      });

      // Set questions if they exist
      if (existingExam.questions && existingExam.questions.length > 0) {
        console.log('📝 Setting questions from API:', existingExam.questions.length);
        setQuestions(existingExam.questions);
      }

      // Set assignment type and selected students based on exam data
      if (existingExam.assignment_info) {
        const { assignment_type } = existingExam.assignment_info;
        console.log('👥 Setting assignment type:', assignment_type);
        setAssignmentType(assignment_type === 'classroom' ? 'classroom' : 'students');

        // Set selected students for individual assignments
        if (assignment_type === 'individual' && existingExam.assigned_students) {
          const studentIds = existingExam.assigned_students.map(student => student.student_id);
          console.log('🎯 Setting selected students:', studentIds);
          setSelectedStudentIds(studentIds);
        }
      }

      // Set class number from exam data
      if (existingExam.class_number) {
        console.log('🏫 Setting class number:', existingExam.class_number);
        setClassNumber(existingExam.class_number);
      }

      // Set subject and class from questions data
      if (existingExam.questions && existingExam.questions.length > 0) {
        const firstQuestion = existingExam.questions[0];
        if (firstQuestion.subject_id) {
          console.log('📚 Setting subject ID from question:', firstQuestion.subject_id);
          setSubjectId(firstQuestion.subject_id);
        }
        if (firstQuestion.class_id) {
          console.log('🏫 Setting class ID from question:', firstQuestion.class_id);
          setClassId(firstQuestion.class_id);
        }
      }
    }
  }, [isEditMode, existingExam, finalExamId, location.state, location.key]);

  // Set default classroom when loaded
  React.useEffect(() => {
    console.log('🏫 Default classroom useEffect triggered:', { classroomsCount: classrooms?.length, currentClassId: classId });
    if (classrooms && classrooms.length > 0 && !classId) {
      console.log('🎯 Setting default classroom:', classrooms[0].id);
      setClassId(classrooms[0].id);
    }
  }, [classrooms, classId]);

  // Fetch subjects when classroom changes
  React.useEffect(() => {
    console.log('📚 Subjects useEffect triggered:', { classId });
    if (classId) {
      console.log('🔄 Fetching subjects for class:', classId);
      dispatch(fetchSubjects());
    }
  }, [dispatch, classId]);

  // Set default subject when loaded
  React.useEffect(() => {
    console.log('📚 Default subject useEffect triggered:', { subjectsCount: subjects?.length, currentSubjectId: subjectId });
    if (subjects && subjects.length > 0 && !subjectId) {
      console.log('🎯 Setting default subject:', subjects[0].id);
      setSubjectId(subjects[0].id);
    }
  }, [subjects, subjectId]);

  // Fetch chapters when subject changes
  React.useEffect(() => {
    console.log('📖 Chapters useEffect triggered:', { subjectId });
    if (subjectId) {
      console.log('🔄 Fetching chapters for subject:', subjectId);
      dispatch(fetchChaptersBySubject({ subjectId }));
    }
  }, [dispatch, subjectId]);

  // Set default chapter when loaded
  React.useEffect(() => {
    console.log('📖 Default chapter useEffect triggered:', { chaptersCount: chaptersBySubject?.length, currentChapterId: chapterId });
    if (chaptersBySubject && chaptersBySubject.length > 0 && !chapterId) {
      console.log('🎯 Setting default chapter:', chaptersBySubject[0].id);
      setChapterId(chaptersBySubject[0].id);
    }
  }, [chaptersBySubject, chapterId]);

  // Fetch topics when chapter changes
  React.useEffect(() => {
    console.log('📝 Topics useEffect triggered:', { chapterId });
    if (chapterId) {
      console.log('🔄 Fetching topics for chapter:', chapterId);
      dispatch(fetchTopicsByChapter({ chapterId }));
      setTopicId("");
      setSubtopicId("");
    }
  }, [dispatch, chapterId]);

  // Set default topic when loaded
  React.useEffect(() => {
    if (topicsByChapter && topicsByChapter.length > 0 && !topicId) {
      setTopicId(topicsByChapter[0].id);
    }
  }, [topicsByChapter]);

  // Fetch subtopics when topic changes
  React.useEffect(() => {
    if (topicId) {
      dispatch(fetchSubtopicsByTopic({ topicId }));
      setSubtopicId("");
    }
  }, [dispatch, topicId]);

  // Set default subtopic when loaded
  React.useEffect(() => {
    if (subtopicsByTopic && subtopicsByTopic.length > 0 && !subtopicId) {
      setSubtopicId(subtopicsByTopic[0].id);
    }
  }, [subtopicsByTopic]);

  // Step progress - Step 1: Exam Details (50%), Step 2: Add Questions (100%)
  React.useEffect(() => {
    setProgress(step === 1 ? 50 : 100);
  }, [step]);

  // Validate exam details
  React.useEffect(() => {
    console.log('🔍 Validating exam details:', {
      title: `"${exam.title}"`,
      titleValid: !!exam.title.trim(),
      start_time: exam.start_time,
      total_duration: exam.total_duration,
      durationValid: exam.total_duration > 0,
      classId,
      subjectId,
      classNumber,
      assignmentType,
      selectedStudentIds: selectedStudentIds.length
    });

    const isValid = exam.title.trim() &&
                   exam.start_time &&
                   exam.total_duration > 0 &&
                   (assignmentType === 'classroom' ? classId : true) && // Only require classroom for classroom assignment
                   subjectId &&
                   classNumber && // Always require class number
                   (assignmentType === 'classroom' ||
                    (assignmentType === 'students' && selectedStudentIds.length > 0));

    console.log('🔍 Exam details valid:', isValid);
    setExamDetailsValid(isValid);
  }, [exam.title, exam.start_time, exam.total_duration, classId, subjectId, classNumber, assignmentType, selectedStudentIds]);

  // Calculate total marks
  const totalMarks = useMemo(() => {
    return questions.reduce((sum, q) => sum + (q.marks || 0), 0);
  }, [questions]);

  // Event handlers
  const handleExamChange = useCallback((e) => {
    const { name, value } = e.target;
    setExam((prev) => ({ ...prev, [name]: value }));
  }, []);

  const handleClassChange = useCallback((e) => {
    setClassId(e.target.value);
  }, []);

  const handleSubjectChange = useCallback((e) => {
    setSubjectId(e.target.value);
  }, []);

  const handleClassNumberChange = useCallback((e) => {
    const selectedClassNo = e.target.value;
    setClassNumber(selectedClassNo);

    // Find the class ID for the selected ClassNo
    const selectedClass = classes?.find(cls => cls.ClassNo === selectedClassNo);
    setClassNumberId(selectedClass?.id || "");
  }, [classes]);

  const handleQuestionChange = useCallback((e) => {
    const { name, value } = e.target;
    setQuestionForm(prev => ({ ...prev, [name]: value }));
  }, []);

  const handleOptionChange = useCallback((idx, field, value) => {
    setQuestionForm(prev => ({
      ...prev,
      options: prev.options.map((opt, i) =>
        i === idx ? { ...opt, [field]: field === "is_correct" ? value : value } : opt
      ),
    }));
  }, []);
  // Add question handler
  const handleAddQuestion = useCallback(async (e) => {
    e.preventDefault();
    setError("");
    setIsSubmitting(true);

    try {
      // Validate question
      if (!questionForm.text.trim()) throw new Error("Question text is required");
      if (!chapterId) throw new Error("Please select chapter");
      if (!classNumber || !classNumberId) throw new Error("Please select class number");
      // Topic and Subtopic are now optional

      if (questionType === "MCQS") {
        if (!questionForm.options.some((opt) => opt.is_correct)) {
          throw new Error("Select a correct option");
        }
        if (questionForm.options.some((opt) => !opt.option_text.trim())) {
          throw new Error("All options required");
        }
      }

      // Prepare question object
      const questionObj = {
        ...questionForm,
        Type: questionType === "MCQS" ? "MCQS" : descType,
        class_id: classNumberId, // Use class_id with the class number UUID
        subject_id: subjectId,
        chapter_id: chapterId,
        topic_id: topicId || null,
        subtopic_id: subtopicId || null,
      };

      // Add to questions array
      setQuestions(prev => [...prev, questionObj]);

      // Reset form
      setQuestionForm(questionType === "MCQS" ? initialQuestionState : initialDescriptiveState);
      setSuccess("Question added successfully!");

      setTimeout(() => setSuccess(""), 3000);
    } catch (error) {
      setError(error.message);
    } finally {
      setIsSubmitting(false);
    }
  }, [questionForm, questionType, chapterId, topicId, subtopicId, descType, classNumber, classNumberId, subjectId]);

  // AI Generate questions handler
  const handleAIGenerate = useCallback(async () => {
    setError("");
    setSuccess("");

    // Validation
    if (!classNumber) {
      setError("Please select a class number in exam details for AI generation");
      return;
    }
    if (assignmentType === 'classroom' && !classId) {
      setError("Please select classroom for AI generation");
      return;
    }
    if (!subjectId || !chapterId) {
      setError("Please select subject and chapter for AI generation");
      return;
    }
    // Topic and Subtopic are now optional for AI generation

    setIsSubmitting(true);
    try {
      // Get the selected classroom, subject, chapter, topic, and subtopic names
      const selectedClassroom = classrooms.find(c => c.id === classId);
      const selectedSubject = subjects.find(s => s.id === subjectId);
      const selectedChapter = chaptersBySubject.find(c => c.id === chapterId);
      const selectedTopic = topicsByChapter.find(t => t.id === topicId);
      const selectedSubtopic = subtopicsByTopic.find(st => st.id === subtopicId);

      // Use the class number from exam details
      const classLevel = classNumber;

      // Validate required fields
      if (!selectedSubject?.name || !selectedChapter?.name) {
        setError("Missing subject or chapter information. Please refresh and try again.");
        return;
      }

      // Prepare difficulty distribution
      const difficultyPayload = {};
      if (aiDifficultyMode === "custom") {
        // Validate that custom difficulty numbers add up to total questions
        const totalCustom = aiNoOfEasy + aiNoOfMedium + aiNoOfHard;
        if (totalCustom !== aiNoOfQuestions) {
          setError(`Custom difficulty distribution (${totalCustom}) must equal total questions (${aiNoOfQuestions})`);
          return;
        }
        difficultyPayload.no_of_easy = aiNoOfEasy;
        difficultyPayload.no_of_medium = aiNoOfMedium;
        difficultyPayload.no_of_hard = aiNoOfHard;
      }

      console.log('AI Generation Data:', {
        class: classLevel,
        subject: selectedSubject.name,
        chapter: selectedChapter.name,
        no_of_questions: aiNoOfQuestions,
        topic: selectedTopic?.name || "",
        subtopic: selectedSubtopic?.name || "",
        ...difficultyPayload
      });

      const result = await dispatch(aiGenerateQuestions({
        class: classLevel,
        subject: selectedSubject.name,
        chapter: selectedChapter.name,
        no_of_questions: aiNoOfQuestions,
        topic: selectedTopic?.name || "",
        subtopic: selectedSubtopic?.name || "",
        ...difficultyPayload
      })).unwrap();

      // EMERGENCY DEBUG - Check if result is the JSON string we see in console
      if (typeof result === 'string' && result.includes('{"status":"generated"')) {
        console.log('🚨 FOUND THE JSON STRING! Parsing it now...');
        try {
          const parsed = JSON.parse(result);
          console.log('🎉 Successfully parsed:', parsed);
          // Replace result with parsed version
          const questions = parsed.questions || [];
          if (questions.length > 0) {
            const questionsWithRequiredFields = questions.map(question => {
              console.log('🔍 Processing question:', question);
              return {
                text: question.question || question.question_text || question.text || "NO TEXT FOUND",
                Level: (question.difficulty_level || question.difficulty)?.toUpperCase() || "MEDIUM",
                Type: "LONG",
                marks: question.marks || 5,
                answer: question.solution || question.answer_key || question.answer || "",
                solution_steps: question.solution_steps || [],
                class_id: classNumberId,
                subject_id: subjectId,
                chapter_id: chapterId,
                topic_id: topicId || null,
                subtopic_id: subtopicId || null,
                question_id: question.question_id,
                chapter: question.chapter,
                topic: question.topic,
                subtopic: question.subtopic,
              };
            });

            console.log('🎯 Final questions:', questionsWithRequiredFields);
            setQuestions(prev => [...prev, ...questionsWithRequiredFields]);
            setSuccess(`Generated ${questions.length} questions successfully!`);
            setTimeout(() => setSuccess(""), 5000);
            return;
          }
        } catch (e) {
          console.error('❌ Failed to parse JSON string:', e);
        }
      }

      console.log('AI Generation Result:', result);
      console.log('Result type:', typeof result);

      // Handle case where result is still a JSON string
      let parsedResult = result;
      if (typeof result === 'string') {
        try {
          parsedResult = JSON.parse(result);
          console.log('Parsed result:', parsedResult);
        } catch (e) {
          console.error('Failed to parse result:', e);
          setError("Invalid response format from AI service");
          return;
        }
      }

      // Extract questions from response
      const questions = parsedResult.questions || [];
      console.log('Extracted questions:', questions);

      if (questions && questions.length > 0) {
        // Transform AI-generated questions to match expected format
        const questionsWithRequiredFields = questions.map(question => {
          console.log('Processing question:', question);
          return {
            // Map AI response fields to expected format - YOUR RESPONSE HAS "question" FIELD
            text: question.question || question.question_text || question.text || "",
            Level: (question.difficulty_level || question.difficulty)?.toUpperCase() || question.Level || "MEDIUM",
            // Default to LONG for descriptive questions
            Type: "LONG",
            marks: question.marks || 5, // Default marks if not provided
            answer: question.solution || question.answer_key || question.answer || "",
            solution_steps: question.solution_steps || [],
            // Required fields for database
            class_id: classNumberId,
            subject_id: subjectId,
            chapter_id: chapterId,
            topic_id: topicId || null,
            subtopic_id: subtopicId || null,
            // Keep original AI fields for reference
            question_id: question.question_id,
            chapter: question.chapter,
            topic: question.topic,
            subtopic: question.subtopic,
          };
        });

        console.log('Final questions to add:', questionsWithRequiredFields);
        setQuestions(prev => [...prev, ...questionsWithRequiredFields]);
        setSuccess(`Generated ${questions.length} questions successfully!`);
        setTimeout(() => setSuccess(""), 5000);
      } else {
        setError("No questions were generated. Please try again.");
      }
    } catch (error) {
      console.error('AI Generation Error:', error);
      setError(error || "Failed to generate questions. Please check your selections and try again.");
    } finally {
      setIsSubmitting(false);
    }
  }, [dispatch, subjectId, classNumber, classNumberId, chapterId, topicId, subtopicId, classrooms, subjects, chaptersBySubject, topicsByChapter, subtopicsByTopic, aiNoOfQuestions, aiDifficultyMode, aiNoOfEasy, aiNoOfMedium, aiNoOfHard]);



  // Question management handlers
  const handleDeleteQuestion = useCallback((index) => {
    setQuestions(prev => prev.filter((_, i) => i !== index));
  }, []);

  const handleEditQuestion = useCallback((index) => {
    setEditingIndex(index);
    setEditingQuestion({ ...questions[index] });
  }, [questions]);

  const handleEditingQuestionChange = useCallback((field, value) => {
    setEditingQuestion(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleEditingOptionChange = useCallback((optIdx, field, value) => {
    setEditingQuestion(prev => ({
      ...prev,
      options: prev.options.map((opt, i) =>
        i === optIdx ? { ...opt, [field]: field === "is_correct" ? value : value } : opt
      )
    }));
  }, []);

  const handleSaveEdit = useCallback(() => {
    setQuestions(prev => prev.map((q, i) => i === editingIndex ? editingQuestion : q));
    setEditingIndex(-1);
    setEditingQuestion(null);
  }, [editingIndex, editingQuestion]);

  const handleCancelEdit = useCallback(() => {
    setEditingIndex(-1);
    setEditingQuestion(null);
  }, []);

  // Submit exam handler
  const handleSubmitExam = useCallback(async (e) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    setIsSubmitting(true);

    try {
      console.log('🔍 Current exam state before validation:', exam);
      console.log('🔍 Exam title:', `"${exam.title}"`, 'Length:', exam.title?.length);
      console.log('🔍 Exam duration:', exam.total_duration, 'Type:', typeof exam.total_duration);
      console.log('🔍 examDetailsValid:', examDetailsValid);

      // Prevent submission if form is not valid
      if (!examDetailsValid) {
        throw new Error("Please fill in all required exam details before submitting");
      }

      if (!exam.title.trim()) throw new Error("Exam title is required");
      const duration = parseInt(exam.total_duration);
      if (!duration || duration <= 0) throw new Error("Exam duration must be greater than 0 minutes");
      if (questions.length === 0) throw new Error("Add at least one question");
      if (assignmentType === 'classroom' && !classId) throw new Error("Please select classroom");
      if (!subjectId) throw new Error("Please select subject");

      // Validate assignment
      if (assignmentType === 'students' && selectedStudentIds.length === 0) {
        throw new Error("Please select at least one student for the exam assignment");
      }

      // Calculate total marks
      const totalMarks = questions.reduce((sum, q) => sum + (q.marks || 0), 0);

      // Prepare exam data with correct assignment structure
      const examData = {
        // Move exam fields to root level (no nested "exam" object)
        ...exam,
        total_marks: totalMarks,
        total_duration: parseInt(exam.total_duration) || 0, // Ensure it's a number
        questions: questions,
        assignment: {
          classroom_id: assignmentType === 'classroom' ? classId : null,
          student_ids: assignmentType === 'students' ? selectedStudentIds : []
        }
      };

      // Submit exam - either create or update
      if (isEditMode) {
        // Use simple update for basic exam fields only
        // Note: Questions and assignments need to be handled separately
        await dispatch(updateExam({
          id: finalExamId,
          examData: {
            title: exam.title,
            description: exam.description,
            total_duration: exam.total_duration,
            start_time: exam.start_time
          }
        })).unwrap();

        setSuccess("Exam basic information updated successfully!");
        console.log("⚠️ Note: Questions and assignments are not updated in edit mode with the simplified API");

        // Navigate back to exam detail after successful update
        setTimeout(() => {
          navigate(`/teacher/exam/${finalExamId}`);
        }, 1500);
      } else {

        await dispatch(createExamWithAssignment(examData)).unwrap();
        setSuccess("Exam created successfully!");

        // Reset form after success
        setTimeout(() => {
          setExam(initialExamState);
          setQuestions([]);
          setStep(1);
          setAssignmentType("classroom");
          setSelectedStudentIds([]);
          setSuccess("");
        }, 2000);
      }

    } catch (error) {
      setError(error.message || (isEditMode ? "Failed to update exam" : "Failed to create exam"));
    } finally {
      setIsSubmitting(false);
    }
  }, [dispatch, exam, questions, classId, subjectId, assignmentType, selectedStudentIds, isEditMode, finalExamId, navigate]);

  return (
    <div className={`min-h-screen py-8 px-8 ${themeClasses.bg}`}>
      <div className="max-w-6xl mx-auto">
        {/* Header with back button for edit mode */}
        {isEditMode && (
          <div className="flex items-center gap-4 mb-6">
            <button
              onClick={() => navigate(`/teacher/exam/${finalExamId}`)}
              className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
            >
              <FiArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-3xl font-bold text-blue-700 dark:text-blue-400">
              Edit Exam
            </h1>
          </div>
        )}

        {/* Title for create mode */}
        {!isEditMode && (
          <h1 className="text-3xl font-bold mb-6 text-blue-700 dark:text-blue-400 text-center">
            Create New Exam
          </h1>
        )}

        {/* Loading state for edit mode */}
        {isEditMode && examLoading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className={themeClasses.text}>Loading exam data...</p>
            </div>
          </div>
        )}

        {/* Error state for edit mode */}
        {isEditMode && examError && !examLoading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="text-red-500 text-xl mb-4">⚠️</div>
              <p className={`${themeClasses.text} mb-4`}>Failed to load exam data</p>
              <p className="text-red-500 text-sm mb-4">{examError}</p>
              <button
                onClick={() => navigate('/teacher/exams')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                Back to Exams
              </button>
            </div>
          </div>
        )}

        {/* Show main content only when not loading and no error in edit mode */}
        {(!isEditMode || (!examLoading && !examError)) && (
          <>
            {/* Step Indicator */}
            <StepIndicator
              currentStep={step}
              examDetailsValid={examDetailsValid}
              questionsCount={questions.length}
            />

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className={`${themeClasses.bg} rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8`}>
              {step === 1 ? (
                <div>
                  <h2 className="text-2xl font-semibold mb-6 text-gray-900 dark:text-gray-100">
                    Step 1: Exam Details
                  </h2>

                  <ExamDetailsForm
                    exam={exam}
                    onExamChange={handleExamChange}
                    classrooms={classrooms}
                    subjects={subjects}
                    classes={classes}
                    classId={classId}
                    subjectId={subjectId}
                    classNumber={classNumber}
                    assignmentType={assignmentType}
                    onClassChange={handleClassChange}
                    onSubjectChange={handleSubjectChange}
                    onClassNumberChange={handleClassNumberChange}
                    themeClasses={themeClasses}
                    examDetailsValid={examDetailsValid}
                  />

                  {/* Student Assignment Selector */}
                  {classId && (
                    <div className="mt-8">
                      <StudentAssignmentSelector
                        assignmentType={assignmentType}
                        onAssignmentTypeChange={setAssignmentType}
                        selectedStudentIds={selectedStudentIds}
                        onSelectedStudentsChange={setSelectedStudentIds}
                        classId={classId}
                        themeClasses={themeClasses}
                      />
                    </div>
                  )}

                  {/* Navigation */}
                  <div className="flex justify-end mt-8">
                    <button
                      onClick={() => setStep(2)}
                      disabled={!examDetailsValid}
                      className="flex items-center gap-2 px-6 py-3 bg-violet-600 text-white rounded-lg hover:bg-violet-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      Next: Add Questions
                      <FiArrowRight className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                      Step 2: Add Questions
                    </h2>
                    <button
                      onClick={() => setStep(1)}
                      className="flex items-center gap-2 px-4 py-2 text-violet-600 hover:bg-violet-50 dark:hover:bg-violet-900/20 rounded-lg transition-colors"
                    >
                      <FiArrowLeft className="w-4 h-4" />
                      Back to Details
                    </button>
                  </div>

                  <QuestionForm
                    questionType={questionType}
                    setQuestionType={setQuestionType}
                    descType={descType}
                    setDescType={setDescType}
                    questionForm={questionForm}
                    onQuestionChange={handleQuestionChange}
                    onOptionChange={handleOptionChange}
                    onAddQuestion={handleAddQuestion}
                    onAIGenerate={handleAIGenerate}
                    chaptersBySubject={chaptersBySubject}
                    topicsByChapter={topicsByChapter}
                    subtopicsByTopic={subtopicsByTopic}
                    chapterId={chapterId}
                    topicId={topicId}
                    subtopicId={subtopicId}
                    setChapterId={setChapterId}
                    setTopicId={setTopicId}
                    setSubtopicId={setSubtopicId}
                    chaptersLoading={chaptersLoading}
                    topicsLoading={topicsLoading}
                    subtopicsLoading={subtopicsLoading}
                    themeClasses={themeClasses}
                    isSubmitting={isSubmitting}
                    // AI Generation options
                    aiNoOfQuestions={aiNoOfQuestions}
                    setAiNoOfQuestions={setAiNoOfQuestions}
                    aiDifficultyMode={aiDifficultyMode}
                    setAiDifficultyMode={setAiDifficultyMode}
                    aiNoOfEasy={aiNoOfEasy}
                    setAiNoOfEasy={setAiNoOfEasy}
                    aiNoOfMedium={aiNoOfMedium}
                    setAiNoOfMedium={setAiNoOfMedium}
                    aiNoOfHard={aiNoOfHard}
                    setAiNoOfHard={setAiNoOfHard}
                    // Manual question creation fields
                    classes={classes}
                    classNumber={classNumber}
                    classNumberId={classNumberId}
                    onClassNumberChange={handleClassNumberChange}
                  />



                  {/* Questions List */}
                  <div className="mt-8">
                    <QuestionList
                      questions={questions}
                      onDeleteQuestion={handleDeleteQuestion}
                      onEditQuestion={handleEditQuestion}
                      editingIndex={editingIndex}
                      editingQuestion={editingQuestion}
                      onEditingQuestionChange={handleEditingQuestionChange}
                      onEditingOptionChange={handleEditingOptionChange}
                      onSaveEdit={handleSaveEdit}
                      onCancelEdit={handleCancelEdit}
                      themeClasses={themeClasses}
                    />
                  </div>

                  {/* Submit Exam */}
                  {questions.length > 0 && (
                    <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                      <button
                        onClick={handleSubmitExam}
                        disabled={isSubmitting || !examDetailsValid}
                        className={`w-full flex items-center justify-center gap-2 px-6 py-4 rounded-lg transition-all duration-200 text-lg font-medium ${
                          examDetailsValid
                            ? 'bg-green-600 text-white hover:bg-green-700'
                            : 'bg-gray-400 text-gray-200 cursor-not-allowed'
                        } disabled:opacity-50 disabled:cursor-not-allowed`}
                      >
                        <FiSave className="w-5 h-5" />
                        {isSubmitting ? (isEditMode ? "Updating Exam..." : "Creating Exam...") : (isEditMode ? "Update Exam" : "Create Exam")}
                      </button>
                      {!examDetailsValid && (
                        <p className="mt-2 text-sm text-red-600 dark:text-red-400 text-center">
                          Please complete all exam details in Step 1 before submitting
                        </p>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <ProgressBar
              progress={progress}
              totalMarks={totalMarks}
              questionsCount={questions.length}
            />
          </div>
        </div>
        </>
        )}

        {/* Success/Error Messages */}
        {success && (
          <div className="fixed bottom-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2">
            <FiCheck className="w-5 h-5" />
            {success}
          </div>
        )}

        {error && (
          <div className="fixed bottom-4 right-4 bg-red-600 text-white px-6 py-3 rounded-lg shadow-lg max-w-md">
            {error}
          </div>
        )}
      </div>
    </div>
  );
}
