import { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  fetchTeacherExamById,
  fetchExamById,
  deleteExam,
  updateExam,
  fetchComprehensiveExamDetails,
  fetchExamStatistics
} from '../../store/slices/ExamSlice';
import {
  FiArrowLeft,
  FiEdit,
  FiTrash2,
  FiClock,
  FiCalendar,
  FiFileText,
  FiCheckCircle,
  FiAlertCircle,
  FiPlay,
  FiPause,
  FiDownload,
  FiPrinter,
  FiBookOpen,
  FiTarget,
  FiUsers
} from 'react-icons/fi';

import QuestionsDisplay from '../../components/exam/QuestionsDisplay';
import LimitedExamEditModal from '../../components/exam/LimitedExamEditModal';
import ExamAssignmentStatus from '../../components/exam/ExamAssignmentStatus';
import ExamAnalytics from '../../components/exam/ExamAnalytics';
import { exportExamForStudents, exportExamForTeachers } from '../../utils/pdfExport';

const TeacherExamDetail = () => {

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { currentTheme } = useThemeProvider();

  // Extract exam ID from URL path since useParams might not work with DashboardLayout routing
  const pathParts = location.pathname.split('/');
  const examId = pathParts[pathParts.length - 1]; // Get the last part of the path

  // Redux state
  const {
    currentExam: exam,
    loading,
    error,
    comprehensiveExamDetails,
    examStatistics,
    comprehensiveLoading,
    statisticsLoading
  } = useSelector((state) => state.exams);
  
  // Local state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showLimitedEdit, setShowLimitedEdit] = useState(false);
  const [isExportingPDF, setIsExportingPDF] = useState(false);
  const [assignmentUpdateMessage, setAssignmentUpdateMessage] = useState('');

  // Theme classes
  const themeClasses = useMemo(() => ({
    bg: currentTheme === "dark" ? "bg-gray-900" : "bg-gray-50",
    text: currentTheme === "dark" ? "text-gray-100" : "text-gray-900",
    cardBg: currentTheme === "dark" ? "bg-gray-800" : "bg-white",
    button: currentTheme === "dark" ? "bg-blue-600 hover:bg-blue-700" : "bg-blue-600 hover:bg-blue-700"
  }), [currentTheme]);

  // Fetch exam details on component mount
  useEffect(() => {
    if (examId) {
      console.log('🎯 TeacherExamDetail: Fetching comprehensive exam data for ID:', examId);

      // Try comprehensive API first for enhanced data
      dispatch(fetchComprehensiveExamDetails(examId))
        .unwrap()
        .then((result) => {
          console.log('✅ Comprehensive exam data fetched successfully:', result);
          console.log('✅ Statistics:', result.statistics);
          console.log('✅ Assignment details:', result.assignment_details);
          console.log('✅ Available students:', result.available_students?.length || 0);
        })
        .catch((error) => {
          console.error('❌ Comprehensive API failed, falling back to basic exam data:', error);

          // Fallback to basic exam endpoint
          dispatch(fetchExamById(examId))
            .unwrap()
            .then((result) => {
              console.log('✅ Basic exam data fetched successfully:', result);
              // Also fetch statistics separately if comprehensive failed
              dispatch(fetchExamStatistics(examId))
                .unwrap()
                .then((stats) => {
                  console.log('✅ Statistics fetched separately:', stats);
                })
                .catch((statsError) => {
                  console.warn('⚠️ Statistics fetch failed:', statsError);
                });
            })
            .catch((basicError) => {
              console.error('❌ Basic exam fetch also failed, trying teacher endpoint:', basicError);
              // Final fallback to teacher-specific endpoint
              dispatch(fetchTeacherExamById(examId))
                .unwrap()
                .then((result) => {
                  console.log('✅ Exam fetched via teacher endpoint:', result);
                })
                .catch((teacherError) => {
                  console.error('❌ All API calls failed:', teacherError);
                });
            });
        });
    }
  }, [dispatch, examId, location.pathname]);

  // Get exam status info
  const getStatusInfo = (exam) => {
    if (!exam) return { icon: FiFileText, color: 'gray', label: 'Unknown' };
    
    const now = new Date();
    const startTime = exam.start_time ? new Date(exam.start_time) : null;
    const endTime = exam.end_time ? new Date(exam.end_time) : null;
    
    if (exam.status === 'draft') {
      return {
        icon: FiPause,
        color: 'gray',
        label: 'Draft'
      };
    } else if (exam.status === 'active') {
      if (startTime && now < startTime) {
        return {
          icon: FiClock,
          color: 'yellow',
          label: 'Scheduled'
        };
      } else if (endTime && now > endTime) {
        return {
          icon: FiCheckCircle,
          color: 'green',
          label: 'Completed'
        };
      } else {
        return {
          icon: FiPlay,
          color: 'green',
          label: 'Active'
        };
      }
    } else if (exam.status === 'completed') {
      return {
        icon: FiCheckCircle,
        color: 'blue',
        label: 'Completed'
      };
    }
    
    return {
      icon: FiAlertCircle,
      color: 'red',
      label: 'Unknown'
    };
  };

  // Handlers
  const handleBack = () => {
    navigate('/teacher/exams');
  };



  const handleDelete = async () => {
    try {
      await dispatch(deleteExam(examId)).unwrap();
      navigate('/teacher/exams');
    } catch (error) {
      console.error('Failed to delete exam:', error);
    }
  };

  const handleViewResults = () => {
    navigate(`/teacher/exam/${examId}/results`);
  };

  const handleEditExam = () => {
    // Open limited edit modal instead of navigating to full edit page
    setShowLimitedEdit(true);
  };

  const handleAssignmentUpdate = (message) => {
    setAssignmentUpdateMessage(message);
    // Refresh exam data to get updated assignment info
    dispatch(fetchExamById(examId));
    // Clear message after 3 seconds
    setTimeout(() => setAssignmentUpdateMessage(''), 3000);
  };

  const handleLimitedSave = async (updateData) => {
    try {
      console.log('📤 Updating exam with simplified data:', updateData);

      // Filter to only include allowed fields (duration and start_time)
      const examUpdateData = {};
      if (updateData.total_duration !== undefined) {
        examUpdateData.total_duration = updateData.total_duration;
      }
      if (updateData.start_time !== undefined) {
        examUpdateData.start_time = updateData.start_time;
      }

      await dispatch(updateExam({
        id: examId,
        examData: examUpdateData
      })).unwrap();

      console.log('✅ Exam updated successfully via PUT /api/exams/' + examId);

      // Refresh the exam data to reflect changes
      dispatch(fetchExamById(examId));

    } catch (error) {
      console.error('❌ Error updating exam:', error);
      throw error; // Re-throw to let the modal handle the error
    }
  };

  const handleExportPDF = async (includeAnswers = false) => {
    setIsExportingPDF(true);
    try {
      const result = includeAnswers
        ? exportExamForTeachers(exam)
        : exportExamForStudents(exam);

      if (result.success) {
        console.log(`PDF exported successfully: ${result.filename}`);
      } else {
        console.error('PDF export failed:', result.error);
      }
    } catch (error) {
      console.error('Error exporting PDF:', error);
    } finally {
      setIsExportingPDF(false);
    }
  };

  if (loading) {
    return (
      <div className={`min-h-screen ${themeClasses.bg} ${themeClasses.text} flex items-center justify-center`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading exam details...</p>
        </div>
      </div>
    );
  }



  if (error || !exam) {
    return (
      <div className={`min-h-screen ${themeClasses.bg} ${themeClasses.text} flex items-center justify-center`}>
        <div className="text-center">
          <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Exam Not Found</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error ? (typeof error === 'string' ? error : error.message || error.detail || 'An error occurred') : 'The exam you are looking for does not exist.'}
          </p>
          <button
            onClick={handleBack}
            className={`px-4 py-2 ${themeClasses.button} text-white rounded-lg transition-colors duration-200`}
          >
            Back to Exams
          </button>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusInfo(exam);

  return (
    <div className={`min-h-screen ${themeClasses.bg} transition-colors duration-300`}>
      <div className="max-w-7xl mx-auto p-3 sm:p-4 lg:p-6">
        {/* Header Section */}
        <div className="mb-6">
          {/* Navigation & Title */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
            <div className="flex items-center gap-4">
              <button
                onClick={handleBack}
                className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
              >
                <FiArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100">
                  {exam.title}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Exam Details & Management
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap items-center gap-2">
              <button
                onClick={handleEditExam}
                className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2 text-sm"
              >
                <FiEdit className="w-4 h-4" />
                Edit
              </button>

              <div className="flex items-center gap-1">
                <button
                  onClick={() => handleExportPDF(false)}
                  disabled={isExportingPDF}
                  className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors duration-200 flex items-center gap-2 text-sm"
                  title="Export PDF without answers"
                >
                  <FiDownload className="w-4 h-4" />
                  PDF
                </button>
                <button
                  onClick={() => handleExportPDF(true)}
                  disabled={isExportingPDF}
                  className="px-3 py-2 bg-green-700 text-white rounded-lg hover:bg-green-800 disabled:opacity-50 transition-colors duration-200 flex items-center gap-2 text-sm"
                  title="Export PDF with answers"
                >
                  <FiPrinter className="w-4 h-4" />
                  +A
                </button>
              </div>

              {exam.status === 'completed' && (
                <button
                  onClick={handleViewResults}
                  className="px-3 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200 flex items-center gap-2 text-sm"
                >
                  <FiFileText className="w-4 h-4" />
                  Results
                </button>
              )}

              <button
                onClick={() => setShowDeleteConfirm(true)}
                className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center gap-2 text-sm"
              >
                <FiTrash2 className="w-4 h-4" />
                Delete
              </button>
            </div>
          </div>

          {/* Status & Messages */}
          <div className="flex flex-col gap-4">
            {/* Status Badge */}
            <div>
              <span className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium
                ${statusInfo.color === 'gray' ? 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200' : ''}
                ${statusInfo.color === 'yellow' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : ''}
                ${statusInfo.color === 'green' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : ''}
                ${statusInfo.color === 'blue' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : ''}
                ${statusInfo.color === 'red' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : ''}
              `}>
                <statusInfo.icon className="w-4 h-4" />
                {statusInfo.label}
              </span>
            </div>

            {/* Assignment Update Success Message */}
            {assignmentUpdateMessage && (
              <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <div className="flex items-center gap-2">
                  <FiCheckCircle className="w-4 h-4 text-green-600" />
                  <p className="text-green-700 dark:text-green-400 text-sm">
                    {assignmentUpdateMessage}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          {/* Left Column - Exam Details & Questions */}
          <div className="lg:col-span-2 space-y-4">
            {/* Exam Overview Card */}
            <div className={`${themeClasses.cardBg} rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden`}>
              {/* Card Header */}
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    Exam Overview
                  </h2>
                  {exam.class_number && (
                    <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium">
                      Class {exam.class_number}
                    </span>
                  )}
                </div>
                {exam.description && (
                  <p className="text-gray-600 dark:text-gray-400 mt-2 text-sm">
                    {exam.description}
                  </p>
                )}
              </div>

              {/* Quick Stats Grid */}
              <div className="p-6">
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <FiBookOpen className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {exam.questions?.length || 0}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">Questions</div>
                  </div>

                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <FiTarget className="w-6 h-6 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {exam.total_marks || 0}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">Total Marks</div>
                  </div>

                  <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <FiClock className="w-6 h-6 text-orange-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {exam.total_duration || 0}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">Minutes</div>
                  </div>

                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <FiUsers className="w-6 h-6 text-purple-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {exam.total_assigned_students || exam.assigned_count || 0}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">Assigned</div>
                  </div>
                </div>

                {/* Schedule Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <FiCalendar className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Start Time</span>
                    </div>
                    <p className="text-gray-900 dark:text-gray-100 font-medium">
                      {exam.start_time ? new Date(exam.start_time).toLocaleString() : 'Not scheduled'}
                    </p>
                  </div>

                  <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <FiClock className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">End Time</span>
                    </div>
                    <p className="text-gray-900 dark:text-gray-100 font-medium">
                      {exam.end_time ? new Date(exam.end_time).toLocaleString() : 'Not scheduled'}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Questions Display */}
            <QuestionsDisplay questions={exam.questions} />
          </div>

          {/* Right Column - Assignment & Statistics */}
          <div className="lg:col-span-1 space-y-4">
            {/* Assignment Management */}
            <ExamAssignmentStatus
              examId={examId}
              onAssignmentUpdate={handleAssignmentUpdate}
            />

            {/* Enhanced Analytics */}
            <ExamAnalytics
              statistics={
                comprehensiveExamDetails?.statistics ||
                examStatistics ||
                // Fallback to basic exam data
                {
                  total_assigned: exam?.assigned_count || exam?.total_assigned_students || 0,
                  total_attempts: exam?.submitted_count || 0,
                  completed_attempts: exam?.submitted_count || 0,
                  completion_rate: exam?.assigned_count ?
                    Math.round((exam?.submitted_count || 0) / exam.assigned_count * 100) : 0,
                  marks_statistics: {
                    total_submissions: exam?.submitted_count || 0,
                    highest_marks: 0,
                    lowest_marks: 0,
                    average_marks: 0,
                    standard_deviation: 0
                  },
                  top_performers: []
                }
              }
              loading={comprehensiveLoading || statisticsLoading}
              showTopPerformers={true}
              showDetailedStats={true}
            />

            {/* Exam Metadata */}
            <div className={`${themeClasses.cardBg} p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700`}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                Exam Details
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Created</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {exam?.created_at ? new Date(exam.created_at).toLocaleDateString() : 'Unknown'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Last Updated</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {exam?.updated_at ? new Date(exam.updated_at).toLocaleDateString() : 'Unknown'}
                  </span>
                </div>
                {exam?.class_number && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Class</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {exam.class_number}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className={`${themeClasses.cardBg} p-6 rounded-xl shadow-xl max-w-md w-full mx-4`}>
              <h3 className="text-lg font-semibold mb-4">Confirm Delete</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Are you sure you want to delete "{exam.title}"? This action cannot be undone.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}



        {/* Limited Edit Modal */}
        {showLimitedEdit && (
          <LimitedExamEditModal
            exam={exam}
            isOpen={showLimitedEdit}
            onClose={() => setShowLimitedEdit(false)}
            onSave={handleLimitedSave}
          />
        )}
      </div>
    </div>
  );
};

export default TeacherExamDetail;
