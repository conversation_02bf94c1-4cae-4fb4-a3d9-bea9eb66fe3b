import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiHome,
  FiCheck,
  FiX,
  FiEye,
  FiCalendar,
  FiUser,
  FiMail,
  FiPlus,
  FiClock,
  FiAward,
  FiBookOpen
} from 'react-icons/fi';
import {
  applyForMentorship,
  selectMentorshipApplications,
  selectMentorshipApplicationsLoading,
  selectMentorshipApplicationsError,
  selectMentorshipApplyLoading,
  selectMentorshipApplySuccess
} from '../../store/slices/MentorsSlice';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import RoleGuard from '../../components/auth/RoleGuard';

const TeacherMentorshipPage = () => {
  const dispatch = useDispatch();
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [applicationData, setApplicationData] = useState({
    institute_id: '',
    application_message: '',
    portfolio_highlights: '',
    availability_notes: '',
    teaching_experience: '',
    subject_expertise: '',
    preferred_hourly_rate: 25
  });

  // Mock institutes data - in real app, this would come from an API
  const [availableInstitutes] = useState([
    { id: 1, name: 'Springfield University', location: 'Springfield, IL' },
    { id: 2, name: 'Riverside College', location: 'Riverside, CA' },
    { id: 3, name: 'Mountain View Institute', location: 'Mountain View, CO' },
    { id: 4, name: 'Coastal Academy', location: 'Miami, FL' }
  ]);

  // Load mentorship applications on component mount
  useEffect(() => {
    // For now, we'll use mock data. In a real app, you'd fetch from an API
    // dispatch(fetchMentorshipApplications());
  }, [dispatch]);

  // Redux state
  const associations = useSelector(selectMentorshipApplications);
  const associationsLoading = useSelector(selectMentorshipApplicationsLoading);
  const associationsError = useSelector(selectMentorshipApplicationsError);
  const applyLoading = useSelector(selectMentorshipApplyLoading);
  const applySuccess = useSelector(selectMentorshipApplySuccess);

  const handleApplyToInstitute = async () => {
    if (!applicationData.institute_id || !applicationData.application_message) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      await dispatch(applyForMentorship(applicationData)).unwrap();
      setShowApplicationModal(false);
      setApplicationData({
        institute_id: '',
        application_message: '',
        portfolio_highlights: '',
        availability_notes: '',
        teaching_experience: '',
        subject_expertise: '',
        preferred_hourly_rate: 25
      });
    } catch (error) {
      console.error('Failed to apply for mentorship:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setApplicationData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved':
        return <FiCheck className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <FiX className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <FiClock className="h-5 w-5 text-yellow-500" />;
      default:
        return <FiClock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (associationsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <RoleGuard allowedRoles={['teacher']}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Mentorship Applications</h1>
            <p className="mt-2 text-gray-600">
              Apply to become a mentor at educational institutes and manage your applications
            </p>
          </div>
          <button 
            onClick={() => setShowApplicationModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FiPlus className="h-4 w-4 mr-2" />
            Apply to Institute
          </button>
        </div>
      </div>

      {/* Error Message */}
      {associationsError && (
        <div className="mb-6">
          <ErrorMessage message={associationsError} />
        </div>
      )}

      {/* Success Message */}
      {applySuccess && (
        <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <FiCheck className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">
                Application submitted successfully!
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Applications List */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Your Applications</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Track the status of your mentorship applications
          </p>
        </div>
        
        {associations && associations.length > 0 ? (
          <ul className="divide-y divide-gray-200">
            {associations.map((association) => (
              <li key={association.id} className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <FiHome className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="ml-4">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-gray-900">
                          {association.institute?.name || 'Institute Name'}
                        </p>
                        <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(association.status)}`}>
                          {getStatusIcon(association.status)}
                          <span className="ml-1">{association.status}</span>
                        </span>
                      </div>
                      <p className="text-sm text-gray-500">
                        Applied on {new Date(association.applied_at).toLocaleDateString()}
                      </p>
                      {association.application_message && (
                        <p className="text-sm text-gray-600 mt-1">
                          {association.application_message.substring(0, 100)}...
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="text-gray-400 hover:text-gray-600">
                      <FiEye className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div className="px-4 py-12 text-center">
            <FiAward className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No applications yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start by applying to an institute to become a mentor.
            </p>
            <div className="mt-6">
              <button
                onClick={() => setShowApplicationModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiPlus className="h-4 w-4 mr-2" />
                Apply to Institute
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Application Modal */}
      {showApplicationModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Apply to Institute</h3>
                <button
                  onClick={() => setShowApplicationModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FiX className="h-6 w-6" />
                </button>
              </div>
              
              <form className="space-y-4">
                {/* Institute Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Select Institute *
                  </label>
                  <select
                    name="institute_id"
                    value={applicationData.institute_id}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="">Choose an institute...</option>
                    {availableInstitutes.map((institute) => (
                      <option key={institute.id} value={institute.id}>
                        {institute.name} - {institute.location}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Application Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Application Message *
                  </label>
                  <textarea
                    name="application_message"
                    value={applicationData.application_message}
                    onChange={handleInputChange}
                    rows={4}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Explain why you want to become a mentor at this institute..."
                    required
                  />
                </div>

                {/* Teaching Experience */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Teaching Experience
                  </label>
                  <textarea
                    name="teaching_experience"
                    value={applicationData.teaching_experience}
                    onChange={handleInputChange}
                    rows={3}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Describe your teaching experience..."
                  />
                </div>

                {/* Subject Expertise */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Subject Expertise
                  </label>
                  <input
                    type="text"
                    name="subject_expertise"
                    value={applicationData.subject_expertise}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., Mathematics, Physics, Computer Science"
                  />
                </div>

                {/* Preferred Hourly Rate */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Preferred Hourly Rate ($)
                  </label>
                  <input
                    type="number"
                    name="preferred_hourly_rate"
                    value={applicationData.preferred_hourly_rate}
                    onChange={handleInputChange}
                    min="10"
                    max="200"
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Portfolio Highlights */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Portfolio Highlights
                  </label>
                  <textarea
                    name="portfolio_highlights"
                    value={applicationData.portfolio_highlights}
                    onChange={handleInputChange}
                    rows={3}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Highlight your key achievements and qualifications..."
                  />
                </div>

                {/* Availability Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Availability Notes
                  </label>
                  <textarea
                    name="availability_notes"
                    value={applicationData.availability_notes}
                    onChange={handleInputChange}
                    rows={2}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="When are you available for mentoring sessions?"
                  />
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowApplicationModal(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleApplyToInstitute}
                    disabled={applyLoading}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {applyLoading ? (
                      <div className="flex items-center">
                        <LoadingSpinner size="sm" className="mr-2" />
                        Submitting...
                      </div>
                    ) : (
                      'Submit Application'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
      </div>
    </RoleGuard>
  );
};

export default TeacherMentorshipPage;
