import { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import {
  fetchAllTasksWithFilters,
  fetchTasksMinimal,
  fetchTaskSubmissions,
  deleteTask,
  selectTasks,
  selectTasksLoading,
  selectTasksError,
  selectSubmissions,
  clearTaskState
} from '../../store/slices/TaskSlice';
import CreateTask from './CreateTask';
import {
  FiPlus,
  FiSearch,
  FiFilter,
  FiCalendar,
  FiUsers,
  FiClock,
  FiCheckCircle,
  FiAlertCircle,
  FiEye,
  FiEdit3,
  FiTrash2,
  FiLoader,
  FiBook
} from 'react-icons/fi';

/**
 * TeacherTasks Page
 * Comprehensive task management dashboard for teachers
 */
const TeacherTasks = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();

  // Redux state
  const tasks = useSelector(selectTasks);
  const loading = useSelector(selectTasksLoading);
  const error = useSelector(selectTasksError);

  // Local state
  const [showCreateTask, setShowCreateTask] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [subjectFilter, setSubjectFilter] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');

  // Theme classes
  const bgPrimary = currentTheme === 'dark' ? 'bg-gray-800' : 'bg-white';
  const bgSecondary = currentTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-50';
  const textPrimary = currentTheme === 'dark' ? 'text-gray-100' : 'text-gray-900';
  const textSecondary = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600';
  const borderColor = currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300';

  // Load tasks on mount
  useEffect(() => {
    const filters = {
      subject_id: subjectFilter !== 'all' ? subjectFilter : undefined,
      skip: 0,
      limit: 100
    };

    dispatch(fetchTasksMinimal(filters));

    return () => {
      dispatch(clearTaskState());
    };
  }, [dispatch, subjectFilter]);

  // Filter and sort tasks
  const filteredTasks = useMemo(() => {
    if (!tasks || !Array.isArray(tasks)) {
      return [];
    }

    let filtered = [...tasks];

    // Apply status filter
    if (statusFilter && statusFilter !== 'all') {
      filtered = filtered.filter(task => task.status === statusFilter);
    }

    // Apply subject filter (using subject_name from minimal API)
    if (subjectFilter && subjectFilter !== 'all') {
      filtered = filtered.filter(task => task.subject_name === subjectFilter);
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(task =>
        task.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      // Handle special cases
      if (sortBy === 'deadline') {
        aValue = aValue ? new Date(aValue) : new Date(0);
        bValue = bValue ? new Date(bValue) : new Date(0);
      } else if (sortBy === 'created_at') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue?.toLowerCase() || '';
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [tasks, statusFilter, subjectFilter, searchTerm, sortBy, sortOrder]);

  // Calculate statistics
  const stats = useMemo(() => {
    if (!tasks || !Array.isArray(tasks)) {
      return {
        totalTasks: 0,
        activeTasks: 0,
        completedTasks: 0,
        pendingGrading: 0
      };
    }

    const totalTasks = tasks.length;
    const activeTasks = tasks.filter(t => t.status === 'PENDING' || t.status === 'IN_PROGRESS').length;
    const completedTasks = tasks.filter(t => t.status === 'COMPLETED').length;
    const pendingGrading = tasks.filter(t => t.pending_submissions > 0).length;

    return {
      totalTasks,
      activeTasks,
      completedTasks,
      pendingGrading
    };
  }, [tasks]);

  // Handle task actions
  const handleViewTask = (taskId) => {
    navigate(`/teacher/task/${taskId}`);
  };

  const handleEditTask = (taskId) => {
    navigate(`/teacher/task/${taskId}/edit`);
  };

  const handleDeleteTask = async (taskId) => {
    if (window.confirm('Are you sure you want to delete this task? This action cannot be undone.')) {
      try {
        await dispatch(deleteTask(taskId)).unwrap();
        // Refresh the tasks list
        dispatch(fetchTasksMinimal({ skip: 0, limit: 100 }));
      } catch (error) {
        console.error('Failed to delete task:', error);
        alert('Failed to delete task. Please try again.');
      }
    }
  };



  // Status badge component
  const StatusBadge = ({ status }) => {
    const statusConfig = {
      PENDING: {
        color: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
        text: 'Pending'
      },
      IN_PROGRESS: {
        color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
        text: 'In Progress'
      },
      COMPLETED: {
        color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
        text: 'Completed'
      },
      CANCELLED: {
        color: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
        text: 'Cancelled'
      }
    };

    const config = statusConfig[status] || statusConfig.PENDING;

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  // Stats card component
  const StatsCard = ({ title, value, icon: Icon, color }) => (
    <div className={`${bgPrimary} rounded-lg border ${borderColor} p-4 hover:shadow-sm transition-shadow`}>
      <div className="flex items-center gap-3">
        <div className={`p-2 rounded-lg ${color.replace('text-', 'bg-').replace('-600', '-100')} dark:${color.replace('text-', 'bg-').replace('-600', '-900/20')}`}>
          <Icon className={`w-5 h-5 ${color}`} />
        </div>
        <div>
          <p className={`text-lg font-semibold ${textPrimary}`}>{value}</p>
          <p className={`text-sm ${textSecondary}`}>{title}</p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className={`text-3xl font-bold ${textPrimary} mb-2`}>Assignments</h1>
            <p className={`text-base ${textSecondary}`}>
              Create and manage assignments for your classes
            </p>
          </div>

          <button
            onClick={() => setShowCreateTask(true)}
            className="mt-4 sm:mt-0 px-6 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 font-medium shadow-sm"
          >
            <FiPlus className="w-4 h-4" />
            Create assignment
          </button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <StatsCard
          title="Total Tasks"
          value={stats.totalTasks}
          icon={FiBook}
          color="text-blue-600"
        />
        <StatsCard
          title="Active Tasks"
          value={stats.activeTasks}
          icon={FiClock}
          color="text-green-600"
        />
        <StatsCard
          title="Completed"
          value={stats.completedTasks}
          icon={FiCheckCircle}
          color="text-purple-600"
        />
        <StatsCard
          title="Pending Grading"
          value={stats.pendingGrading}
          icon={FiAlertCircle}
          color="text-orange-600"
        />
      </div>

      {/* Filters and Search */}
      <div className={`${bgPrimary} rounded-lg border ${borderColor} p-4 mb-6`}>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <FiSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${textSecondary}`} />
            <input
              type="text"
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary}`}
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className={`px-3 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary}`}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="completed">Completed</option>
            <option value="draft">Draft</option>
          </select>

          {/* Subject Filter */}
          <select
            value={subjectFilter}
            onChange={(e) => setSubjectFilter(e.target.value)}
            className={`px-3 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary}`}
          >
            <option value="all">All Subjects</option>
            <option value="math">Mathematics</option>
            <option value="science">Science</option>
            <option value="english">English</option>
            <option value="history">History</option>
          </select>

          {/* Sort */}
          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field);
              setSortOrder(order);
            }}
            className={`px-3 py-2 border ${borderColor} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${bgPrimary} ${textPrimary}`}
          >
            <option value="created_at-desc">Newest First</option>
            <option value="created_at-asc">Oldest First</option>
            <option value="deadline-asc">Deadline (Soon)</option>
            <option value="deadline-desc">Deadline (Later)</option>
            <option value="name-asc">Name (A-Z)</option>
            <option value="name-desc">Name (Z-A)</option>
          </select>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <FiAlertCircle className="w-4 h-4" />
            <span>
              {typeof error === 'string'
                ? error
                : error?.detail || error?.message || 'An error occurred'
              }
            </span>
          </div>
        </div>
      )}

      {/* Tasks Grid */}
      {loading ? (
        <div className="text-center py-12">
          <FiLoader className={`w-6 h-6 animate-spin mx-auto mb-2 ${textSecondary}`} />
          <p className={`text-sm ${textSecondary}`}>Loading assignments...</p>
        </div>
      ) : filteredTasks.length === 0 ? (
        <div className="text-center py-12">
          <FiBook className={`w-12 h-12 mx-auto mb-4 ${textSecondary} opacity-50`} />
          <h3 className={`text-lg font-medium ${textPrimary} mb-2`}>No assignments yet</h3>
          <p className={`text-sm ${textSecondary} mb-4`}>
            {searchTerm || statusFilter !== 'all' || subjectFilter !== 'all'
              ? 'No assignments match your filters'
              : 'Create your first assignment to get started'
            }
          </p>
          {!searchTerm && statusFilter === 'all' && subjectFilter === 'all' && (
            <button
              onClick={() => setShowCreateTask(true)}
              className="px-6 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 mx-auto"
            >
              <FiPlus className="w-4 h-4" />
              Create assignment
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTasks.map((task) => (
            <article
              key={task.id}
              className={`${bgPrimary} rounded-lg border ${borderColor} hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 cursor-pointer overflow-hidden group`}
              onClick={() => handleViewTask(task.id)}
            >
              {/* Status indicator */}
              <div className={`h-1 ${
                task.status === 'IN_PROGRESS' ? 'bg-green-500' :
                task.status === 'COMPLETED' ? 'bg-blue-500' :
                task.status === 'PENDING' ? 'bg-orange-500' :
                task.status === 'CANCELLED' ? 'bg-red-500' : 'bg-gray-500'
              }`} />

              <div className="p-5">
                {/* Header with title and status */}
                <header className="flex items-start justify-between mb-3">
                  <div className="flex-1 min-w-0">
                    <h3 className={`font-semibold text-lg ${textPrimary} mb-1 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors`}>
                      {task.name}
                    </h3>
                    {task.subject_name && (
                      <p className={`text-sm ${textSecondary} font-medium`}>
                        {task.subject_name}
                      </p>
                    )}
                  </div>
                  <StatusBadge status={task.status} />
                </header>

                  {/* Description */}
                  {task.description && (
                    <p className={`text-sm ${textSecondary} mb-4 line-clamp-2 leading-relaxed`}>
                      {task.description}
                    </p>
                  )}

                  {/* Info Cards */}
                  <div className="space-y-3 mb-4">
                    {task.deadline && (
                      <div className={`flex items-center gap-2 p-2 rounded-md bg-gray-50 dark:bg-gray-800`}>
                        <FiCalendar className={`w-4 h-4 ${textSecondary}`} />
                        <span className={`text-sm ${textSecondary}`}>
                          Due {new Date(task.deadline).toLocaleDateString()} at{' '}
                          {new Date(task.deadline).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>
                    )}

                    <div className="flex items-center gap-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                      <FiUsers className="w-4 h-4 text-blue-600" />
                      <span className={`text-sm font-medium text-blue-700 dark:text-blue-400`}>
                        {task.assigned_students || 0} students assigned
                      </span>
                    </div>
                  </div>

                  {/* Footer */}
                  <div className="flex items-center justify-between pt-3 border-t border-gray-100 dark:border-gray-700">
                    <span className={`text-xs ${textSecondary}`}>
                      Created {new Date(task.created_at || Date.now()).toLocaleDateString()}
                    </span>

                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewTask(task.id);
                        }}
                        className={`p-1.5 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors text-blue-600 dark:text-blue-400`}
                        title="View assignment"
                      >
                        <FiEye className="w-4 h-4" />
                      </button>

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditTask(task.id);
                        }}
                        className={`p-1.5 rounded-md hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors text-green-600 dark:text-green-400`}
                        title="Edit assignment"
                      >
                        <FiEdit3 className="w-4 h-4" />
                      </button>

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteTask(task.id);
                        }}
                        className={`p-1.5 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors text-red-600 dark:text-red-400`}
                        title="Delete assignment"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </article>
            ))
          }
        </div>
      )}

      {/* Create Task Modal */}
      {showCreateTask && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <CreateTask onClose={() => setShowCreateTask(false)} />
          </div>
        </div>
      )}
    </div>
  );
};

export default TeacherTasks;
