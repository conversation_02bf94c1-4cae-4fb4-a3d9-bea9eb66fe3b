import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { useThemeProvider } from '../../providers/ThemeContext';
import StudentAssignmentSelector from '../../components/exam/StudentAssignmentSelector';

const StudentAssignmentTest = () => {
  const { currentTheme } = useThemeProvider();
  const [assignmentType, setAssignmentType] = useState("classroom");
  const [selectedStudentIds, setSelectedStudentIds] = useState([]);
  
  const themeClasses = {
    bg: currentTheme === "dark" ? "bg-gray-900" : "bg-white",
    text: currentTheme === "dark" ? "text-gray-100" : "text-gray-900",
    input: currentTheme === "dark" ? "bg-gray-800 text-gray-100 border-gray-700" : "bg-gray-50 text-gray-900 border-gray-300",
    label: currentTheme === "dark" ? "text-gray-300" : "text-gray-700"
  };

  const handleSubmit = () => {
    const assignmentData = {
      classroom_id: assignmentType === 'classroom' ? 'test-class-id' : null,
      student_ids: assignmentType === 'students' ? selectedStudentIds : []
    };

    console.log('Assignment Data:', assignmentData);
    alert(`Assignment Data: ${JSON.stringify(assignmentData, null, 2)}`);
  };

  return (
    <div className={`min-h-screen py-8 px-8 ${themeClasses.bg}`}>
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-violet-700 dark:text-violet-400 text-center">
          Student Assignment Selector Test
        </h1>
        
        <div className={`${themeClasses.bg} rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8`}>
          <h2 className="text-2xl font-semibold mb-6 text-gray-900 dark:text-gray-100">
            Test Assignment Functionality
          </h2>

          <StudentAssignmentSelector
            assignmentType={assignmentType}
            onAssignmentTypeChange={setAssignmentType}
            selectedStudentIds={selectedStudentIds}
            onSelectedStudentsChange={setSelectedStudentIds}
            classId="test-class-id"
            themeClasses={themeClasses}
          />

          <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <h3 className="font-semibold mb-2 text-gray-900 dark:text-gray-100">Current State:</h3>
            <div className="space-y-2 text-sm">
              <div className="text-gray-700 dark:text-gray-300">
                <strong>Assignment Type:</strong> {assignmentType}
              </div>
              <div className="text-gray-700 dark:text-gray-300">
                <strong>Selected Student IDs:</strong> {JSON.stringify(selectedStudentIds)}
              </div>
              <div className="text-gray-700 dark:text-gray-300">
                <strong>Assignment Object:</strong>
                <pre className="mt-1 p-2 bg-gray-200 dark:bg-gray-700 rounded text-xs">
{JSON.stringify({
  classroom_id: assignmentType === 'classroom' ? 'test-class-id' : null,
  student_ids: assignmentType === 'students' ? selectedStudentIds : []
}, null, 2)}
                </pre>
              </div>
            </div>
          </div>

          <div className="flex justify-end mt-8">
            <button
              onClick={handleSubmit}
              className="flex items-center gap-2 px-6 py-3 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-all duration-200"
            >
              Test Submit
            </button>
          </div>
        </div>

        <div className="mt-8 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h3 className="font-semibold mb-3 text-blue-900 dark:text-blue-100">Test Instructions:</h3>
          <ol className="list-decimal list-inside space-y-2 text-blue-800 dark:text-blue-200">
            <li>Try selecting "Assign to Entire Class" - the assignment object should have classroom_id and empty student_ids array</li>
            <li>Try selecting "Assign to Specific Students" - this should show the student list</li>
            <li>Search for students using the search box</li>
            <li>Select individual students or use "Select All"</li>
            <li>Check that the assignment object updates correctly with selected student IDs</li>
            <li>Click "Test Submit" to see the final assignment data structure</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default StudentAssignmentTest;
