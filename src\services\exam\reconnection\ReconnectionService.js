/**
 * Reconnection Service
 * Handles student reconnection requests and teacher approval workflow
 */

import EventEmitter from '../../../utils/EventEmitter';
import URL from '../../../utils/api/API_URL';

class ReconnectionService extends EventEmitter {
  constructor() {
    super();
    this.pendingRequests = new Map();
    this.baseUrl = URL;
  }

  /**
   * Request reconnection (student side)
   */
  async requestReconnection(sessionId, reason, token) {
    try {
      console.log('Requesting reconnection for session:', sessionId);
      
      const response = await fetch(`${this.baseUrl}/api/exams/session/${sessionId}/request-reconnection`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ 
          reason,
          timestamp: Date.now(),
          user_agent: navigator.userAgent,
          screen_resolution: `${screen.width}x${screen.height}`
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to request reconnection');
      }

      const result = await response.json();
      
      // Store request locally
      this.pendingRequests.set(result.request_id, {
        ...result,
        status: 'pending',
        requestedAt: Date.now()
      });

      this.emit('reconnectionRequested', result);
      console.log('Reconnection requested:', result.request_id);
      
      return result;
      
    } catch (error) {
      console.error('Error requesting reconnection:', error);
      this.emit('reconnectionError', { type: 'request', error: error.message });
      throw error;
    }
  }

  /**
   * Approve reconnection request (teacher side)
   */
  async approveReconnection(requestId, token, conditions = {}) {
    try {
      console.log('Approving reconnection request:', requestId);
      
      const response = await fetch(`${this.baseUrl}/api/exams/reconnection/${requestId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          conditions,
          approved_at: Date.now()
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to approve reconnection');
      }

      const result = await response.json();
      
      // Update local request
      const request = this.pendingRequests.get(requestId);
      if (request) {
        request.status = 'approved';
        request.approvedAt = Date.now();
        request.conditions = conditions;
      }

      this.emit('reconnectionApproved', { requestId, result });
      console.log('Reconnection approved:', requestId);
      
      return result;
      
    } catch (error) {
      console.error('Error approving reconnection:', error);
      this.emit('reconnectionError', { type: 'approve', requestId, error: error.message });
      throw error;
    }
  }

  /**
   * Deny reconnection request (teacher side)
   */
  async denyReconnection(requestId, reason, token) {
    try {
      console.log('Denying reconnection request:', requestId);
      
      const response = await fetch(`${this.baseUrl}/api/exams/reconnection/${requestId}/deny`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          denial_reason: reason,
          denied_at: Date.now()
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to deny reconnection');
      }

      const result = await response.json();
      
      // Update local request
      const request = this.pendingRequests.get(requestId);
      if (request) {
        request.status = 'denied';
        request.deniedAt = Date.now();
        request.denialReason = reason;
      }

      this.emit('reconnectionDenied', { requestId, reason, result });
      console.log('Reconnection denied:', requestId);
      
      return result;
      
    } catch (error) {
      console.error('Error denying reconnection:', error);
      this.emit('reconnectionError', { type: 'deny', requestId, error: error.message });
      throw error;
    }
  }

  /**
   * Execute reconnection (student side)
   */
  async executeReconnection(requestId, token) {
    try {
      console.log('Executing reconnection:', requestId);
      
      const response = await fetch(`${this.baseUrl}/api/exams/reconnection/${requestId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to execute reconnection');
      }

      const result = await response.json();
      
      // Update local request
      const request = this.pendingRequests.get(requestId);
      if (request) {
        request.status = 'executed';
        request.executedAt = Date.now();
      }

      this.emit('reconnectionExecuted', { requestId, result });
      console.log('Reconnection executed:', requestId);
      
      return result;
      
    } catch (error) {
      console.error('Error executing reconnection:', error);
      this.emit('reconnectionError', { type: 'execute', requestId, error: error.message });
      throw error;
    }
  }

  /**
   * Get reconnection status
   */
  async getReconnectionStatus(requestId, token) {
    try {
      const response = await fetch(`${this.baseUrl}/api/exams/reconnection/${requestId}/status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to get reconnection status');
      }

      const status = await response.json();
      
      // Update local request
      const request = this.pendingRequests.get(requestId);
      if (request) {
        Object.assign(request, status);
      }

      return status;
      
    } catch (error) {
      console.error('Error getting reconnection status:', error);
      throw error;
    }
  }

  /**
   * Get pending requests for exam (teacher side)
   */
  async getPendingRequests(examId, token) {
    try {
      const response = await fetch(`${this.baseUrl}/api/exams/${examId}/reconnection-requests`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to get pending requests');
      }

      const requests = await response.json();
      
      // Update local storage
      requests.forEach(request => {
        this.pendingRequests.set(request.request_id, request);
      });

      return requests;
      
    } catch (error) {
      console.error('Error getting pending requests:', error);
      throw error;
    }
  }

  /**
   * Poll for reconnection status updates (student side)
   */
  startStatusPolling(requestId, token, interval = 5000) {
    const pollInterval = setInterval(async () => {
      try {
        const status = await this.getReconnectionStatus(requestId, token);
        
        if (status.status === 'approved') {
          clearInterval(pollInterval);
          this.emit('reconnectionApproved', { requestId, result: status });
        } else if (status.status === 'denied') {
          clearInterval(pollInterval);
          this.emit('reconnectionDenied', { requestId, reason: status.denial_reason, result: status });
        }
        
      } catch (error) {
        console.error('Error polling reconnection status:', error);
        // Continue polling unless it's a critical error
        if (error.message.includes('not found')) {
          clearInterval(pollInterval);
        }
      }
    }, interval);

    return pollInterval;
  }

  /**
   * Get request by ID
   */
  getRequest(requestId) {
    return this.pendingRequests.get(requestId) || null;
  }

  /**
   * Get all pending requests
   */
  getAllPendingRequests() {
    return Array.from(this.pendingRequests.values())
      .filter(request => request.status === 'pending');
  }

  /**
   * Clear completed requests
   */
  clearCompletedRequests() {
    for (const [requestId, request] of this.pendingRequests.entries()) {
      if (['approved', 'denied', 'executed'].includes(request.status)) {
        this.pendingRequests.delete(requestId);
      }
    }
  }

  /**
   * Cleanup
   */
  cleanup() {

    this.pendingRequests.clear();
    this.removeAllListeners();

  }
}

// Create singleton instance
const reconnectionService = new ReconnectionService();

export default reconnectionService;
