/**
 * Enhanced Exam Security Service
 * Provides comprehensive anti-cheating measures for online exams
 */

class ExamSecurityService {
  constructor() {
    this.isActive = false;
    this.violations = [];
    this.onViolationCallback = null;
    this.securityChecks = {
      fullscreen: false,
      devtools: false,
      tabFocus: true,
      copyPaste: false,
      rightClick: false,
      keyboardShortcuts: false,
      screenshot: false
    };
    
    // Bind methods to preserve context
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.handleContextMenu = this.handleContextMenu.bind(this);
    this.handleCopy = this.handleCopy.bind(this);
    this.handlePaste = this.handlePaste.bind(this);
    this.handleFullscreenChange = this.handleFullscreenChange.bind(this);
    this.handleDevToolsDetection = this.handleDevToolsDetection.bind(this);
    this.handleBeforeUnload = this.handleBeforeUnload.bind(this);
  }

  /**
   * Activate security measures
   */
  activate(options = {}) {
    this.isActive = true;
    this.onViolationCallback = options.onViolation || null;
    

    
    // Request fullscreen mode
    this.requestFullscreen();
    
    // Add event listeners
    this.addEventListeners();
    
    // Start monitoring
    this.startMonitoring();
    
    // Disable browser features
    this.disableBrowserFeatures();
    

  }

  /**
   * Deactivate security measures
   */
  deactivate() {
    this.isActive = false;
    

    
    // Remove event listeners
    this.removeEventListeners();
    
    // Exit fullscreen
    this.exitFullscreen();
    
    // Re-enable browser features
    this.enableBrowserFeatures();
    

  }

  /**
   * Request fullscreen mode
   */
  requestFullscreen() {
    // Check if already in fullscreen
    const isFullscreen = document.fullscreenElement ||
                        document.webkitFullscreenElement ||
                        document.msFullscreenElement;

    if (isFullscreen) {

      return Promise.resolve();
    }

    const element = document.documentElement;


    if (element.requestFullscreen) {
      return element.requestFullscreen().then(() => {

      }).catch(err => {

        this.reportViolation('fullscreen_denied', 'User denied fullscreen access');
      });
    } else if (element.webkitRequestFullscreen) {
      element.webkitRequestFullscreen();
      return Promise.resolve();
    } else if (element.msRequestFullscreen) {
      element.msRequestFullscreen();
      return Promise.resolve();
    } else {

      this.reportViolation('fullscreen_unsupported', 'Fullscreen not supported');
      return Promise.reject(new Error('Fullscreen not supported'));
    }
  }

  /**
   * Exit fullscreen mode
   */
  exitFullscreen() {
    // Check if we're actually in fullscreen mode before trying to exit
    const isFullscreen = document.fullscreenElement ||
                        document.webkitFullscreenElement ||
                        document.msFullscreenElement;

    if (!isFullscreen) {

      return;
    }



    if (document.exitFullscreen) {
      document.exitFullscreen().catch(err => {

      });
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
  }

  /**
   * Add security event listeners
   */
  addEventListeners() {
    // Fullscreen monitoring
    document.addEventListener('fullscreenchange', this.handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('msfullscreenchange', this.handleFullscreenChange);
    
    // Tab focus monitoring
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    window.addEventListener('blur', this.handleVisibilityChange);
    window.addEventListener('focus', this.handleVisibilityChange);
    
    // Keyboard monitoring
    document.addEventListener('keydown', this.handleKeyDown, true);
    
    // Context menu blocking
    document.addEventListener('contextmenu', this.handleContextMenu);
    
    // Copy/paste blocking
    document.addEventListener('copy', this.handleCopy);
    document.addEventListener('paste', this.handlePaste);
    document.addEventListener('cut', this.handleCopy);
    
    // Page unload warning
    window.addEventListener('beforeunload', this.handleBeforeUnload);
    
    // Selection blocking
    document.addEventListener('selectstart', this.preventSelection);
    document.addEventListener('dragstart', this.preventSelection);
  }

  /**
   * Remove security event listeners
   */
  removeEventListeners() {
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('msfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('blur', this.handleVisibilityChange);
    window.removeEventListener('focus', this.handleVisibilityChange);
    document.removeEventListener('keydown', this.handleKeyDown, true);
    document.removeEventListener('contextmenu', this.handleContextMenu);
    document.removeEventListener('copy', this.handleCopy);
    document.removeEventListener('paste', this.handlePaste);
    document.removeEventListener('cut', this.handleCopy);
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    document.removeEventListener('selectstart', this.preventSelection);
    document.removeEventListener('dragstart', this.preventSelection);
  }

  /**
   * Handle fullscreen changes
   */
  handleFullscreenChange() {
    const isFullscreen = !!(
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.msFullscreenElement
    );
    
    if (!isFullscreen && this.isActive) {
      this.reportViolation('fullscreen_exit', 'User exited fullscreen mode');
      
      // Try to re-enter fullscreen
      setTimeout(() => {
        if (this.isActive) {
          this.requestFullscreen();
        }
      }, 1000);
    }
  }

  /**
   * Handle visibility changes (tab switching)
   */
  handleVisibilityChange() {
    if (document.hidden || !document.hasFocus()) {
      this.reportViolation('tab_switch', 'User switched tabs or lost focus');
    }
  }

  /**
   * Handle keyboard events
   */
  handleKeyDown(event) {
    const { ctrlKey, metaKey, altKey, shiftKey, key, keyCode } = event;
    
    // Block developer tools shortcuts
    const devToolsShortcuts = [
      { ctrl: true, key: 'F12' },
      { ctrl: true, shift: true, key: 'I' },
      { ctrl: true, shift: true, key: 'J' },
      { ctrl: true, shift: true, key: 'C' },
      { ctrl: true, key: 'U' },
      { key: 'F12' }
    ];
    
    // Block copy/paste shortcuts
    const copyPasteShortcuts = [
      { ctrl: true, key: 'c' },
      { ctrl: true, key: 'v' },
      { ctrl: true, key: 'x' },
      { ctrl: true, key: 'a' },
      { ctrl: true, key: 's' },
      { ctrl: true, key: 'p' }
    ];
    
    // Block refresh shortcuts
    const refreshShortcuts = [
      { ctrl: true, key: 'r' },
      { key: 'F5' },
      { ctrl: true, shift: true, key: 'r' }
    ];
    
    // Check for blocked shortcuts
    const isBlocked = [
      ...devToolsShortcuts,
      ...copyPasteShortcuts,
      ...refreshShortcuts
    ].some(shortcut => {
      return (
        (!shortcut.ctrl || ctrlKey || metaKey) &&
        (!shortcut.shift || shiftKey) &&
        (!shortcut.alt || altKey) &&
        (shortcut.key.toLowerCase() === key.toLowerCase() || shortcut.keyCode === keyCode)
      );
    });
    
    if (isBlocked) {
      event.preventDefault();
      event.stopPropagation();
      this.reportViolation('blocked_shortcut', `Blocked keyboard shortcut: ${key}`);
      return false;
    }
    
    // Block Alt+Tab (window switching)
    if (altKey && key === 'Tab') {
      event.preventDefault();
      this.reportViolation('alt_tab', 'Attempted to switch windows');
      return false;
    }
  }

  /**
   * Handle context menu (right-click)
   */
  handleContextMenu(event) {
    event.preventDefault();
    this.reportViolation('right_click', 'Right-click attempted');
    return false;
  }

  /**
   * Handle copy events
   */
  handleCopy(event) {
    event.preventDefault();
    this.reportViolation('copy_attempt', 'Copy/cut operation attempted');
    return false;
  }

  /**
   * Handle paste events
   */
  handlePaste(event) {
    event.preventDefault();
    this.reportViolation('paste_attempt', 'Paste operation attempted');
    return false;
  }

  /**
   * Prevent text selection
   */
  preventSelection(event) {
    event.preventDefault();
    return false;
  }

  /**
   * Handle page unload
   */
  handleBeforeUnload(event) {
    if (this.isActive) {
      const message = 'Are you sure you want to leave the exam? Your progress may be lost.';
      event.returnValue = message;
      this.reportViolation('page_unload', 'User attempted to leave the page');
      return message;
    }
  }

  /**
   * Start monitoring for developer tools
   */
  startMonitoring() {
    // DevTools detection using console.log timing
    this.devToolsInterval = setInterval(() => {
      this.handleDevToolsDetection();
    }, 1000);
  }

  /**
   * Detect developer tools
   */
  handleDevToolsDetection() {
    const threshold = 160;
    
    // Method 1: Console timing
    const start = performance.now();
    console.clear();
    const end = performance.now();
    
    if (end - start > threshold) {
      this.reportViolation('devtools_open', 'Developer tools detected');
    }
    
    // Method 2: Window size detection
    const widthThreshold = window.outerWidth - window.innerWidth > threshold;
    const heightThreshold = window.outerHeight - window.innerHeight > threshold;
    
    if (widthThreshold || heightThreshold) {
      this.reportViolation('devtools_size', 'Developer tools size detected');
    }
  }

  /**
   * Disable browser features
   */
  disableBrowserFeatures() {
    // Disable text selection
    document.body.style.userSelect = 'none';
    document.body.style.webkitUserSelect = 'none';
    document.body.style.mozUserSelect = 'none';
    document.body.style.msUserSelect = 'none';
    
    // Disable drag and drop
    document.body.style.webkitUserDrag = 'none';
    
    // Disable image saving
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      img.style.pointerEvents = 'none';
      img.draggable = false;
    });
  }

  /**
   * Re-enable browser features
   */
  enableBrowserFeatures() {
    document.body.style.userSelect = '';
    document.body.style.webkitUserSelect = '';
    document.body.style.mozUserSelect = '';
    document.body.style.msUserSelect = '';
    document.body.style.webkitUserDrag = '';
    
    if (this.devToolsInterval) {
      clearInterval(this.devToolsInterval);
    }
  }

  /**
   * Report security violation
   */
  reportViolation(type, details) {
    const violation = {
      type,
      details,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    this.violations.push(violation);
    

    
    if (this.onViolationCallback) {
      this.onViolationCallback(violation);
    }
  }

  /**
   * Get all violations
   */
  getViolations() {
    return this.violations;
  }

  /**
   * Clear violations
   */
  clearViolations() {
    this.violations = [];
  }
}

// Export singleton instance
export default new ExamSecurityService();
