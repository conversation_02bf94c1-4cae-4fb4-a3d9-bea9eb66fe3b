/**
 * Security Manager
 * Comprehensive security implementation for exam system
 */

class SecurityManager {
  constructor() {
    this.isActive = false;
    this.securityPolicies = new Map();
    this.violationCount = 0;
    this.maxViolations = 3;
    this.securityHeaders = new Map();
    this.initializeSecurityHeaders();
  }

  /**
   * Initialize security headers
   */
  initializeSecurityHeaders() {
    this.securityHeaders.set('Content-Security-Policy', 
      "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
      "style-src 'self' 'unsafe-inline'; " +
      "img-src 'self' data: blob:; " +
      "connect-src 'self' ws: wss:; " +
      "font-src 'self'; " +
      "object-src 'none'; " +
      "media-src 'self'; " +
      "frame-src 'none';"
    );
    
    this.securityHeaders.set('X-Content-Type-Options', 'nosniff');
    this.securityHeaders.set('X-Frame-Options', 'DENY');
    this.securityHeaders.set('X-XSS-Protection', '1; mode=block');
    this.securityHeaders.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    this.securityHeaders.set('Permissions-Policy', 
      'camera=(), microphone=(), geolocation=(), payment=(), usb=()'
    );
  }

  /**
   * Apply security headers (for development/testing)
   */
  applySecurityHeaders() {
    // In a real application, these would be set by the server
    // This is for development/testing purposes
    const metaTags = [];
    
    for (const [name, value] of this.securityHeaders) {
      const meta = document.createElement('meta');
      meta.httpEquiv = name;
      meta.content = value;
      document.head.appendChild(meta);
      metaTags.push(meta);
    }
    
    console.log('Security headers applied:', Array.from(this.securityHeaders.keys()));
    return metaTags;
  }

  /**
   * Activate comprehensive security measures
   */
  activate() {
    if (this.isActive) {
      console.warn('Security manager already active');
      return;
    }

    console.log('Activating security manager...');
    
    // Apply security headers
    this.securityMetaTags = this.applySecurityHeaders();
    
    // Enable browser lockdown
    this.enableBrowserLockdown();
    
    // Enable network monitoring
    this.enableNetworkMonitoring();
    
    // Enable performance monitoring
    this.enablePerformanceMonitoring();
    
    // Enable error boundary
    this.enableErrorBoundary();
    
    this.isActive = true;
    console.log('Security manager activated');
  }

  /**
   * Enable browser lockdown
   */
  enableBrowserLockdown() {
    // Disable right-click context menu
    document.addEventListener('contextmenu', this.preventDefaultEvent, true);
    
    // Disable text selection
    document.addEventListener('selectstart', this.preventDefaultEvent, true);
    
    // Disable drag and drop
    document.addEventListener('dragstart', this.preventDefaultEvent, true);
    
    // Disable print screen and other shortcuts
    document.addEventListener('keydown', this.handleKeyDown.bind(this), true);
    
    // Disable developer tools detection
    this.enableDevToolsDetection();
    
    // Force fullscreen mode
    this.enforceFullscreen();
    
    console.log('Browser lockdown enabled');
  }

  /**
   * Handle keyboard events
   */
  handleKeyDown(event) {
    const forbiddenKeys = [
      'F12', // Developer tools
      'F5',  // Refresh
      'F11', // Fullscreen toggle
      'PrintScreen',
      'Insert'
    ];
    
    const forbiddenCombos = [
      { ctrl: true, key: 'u' },      // View source
      { ctrl: true, key: 'U' },      // View source
      { ctrl: true, key: 's' },      // Save page
      { ctrl: true, key: 'S' },      // Save page
      { ctrl: true, key: 'a' },      // Select all
      { ctrl: true, key: 'A' },      // Select all
      { ctrl: true, key: 'c' },      // Copy
      { ctrl: true, key: 'C' },      // Copy
      { ctrl: true, key: 'v' },      // Paste
      { ctrl: true, key: 'V' },      // Paste
      { ctrl: true, key: 'x' },      // Cut
      { ctrl: true, key: 'X' },      // Cut
      { ctrl: true, key: 'r' },      // Refresh
      { ctrl: true, key: 'R' },      // Refresh
      { ctrl: true, key: 'w' },      // Close tab
      { ctrl: true, key: 'W' },      // Close tab
      { ctrl: true, key: 't' },      // New tab
      { ctrl: true, key: 'T' },      // New tab
      { ctrl: true, key: 'n' },      // New window
      { ctrl: true, key: 'N' },      // New window
      { ctrl: true, shift: true, key: 'i' }, // Developer tools
      { ctrl: true, shift: true, key: 'I' }, // Developer tools
      { ctrl: true, shift: true, key: 'j' }, // Console
      { ctrl: true, shift: true, key: 'J' }, // Console
      { ctrl: true, shift: true, key: 'c' }, // Inspector
      { ctrl: true, shift: true, key: 'C' }, // Inspector
      { alt: true, key: 'Tab' },     // Alt+Tab
      { alt: true, key: 'F4' },      // Alt+F4
    ];
    
    // Check forbidden keys
    if (forbiddenKeys.includes(event.key)) {
      event.preventDefault();
      event.stopPropagation();
      this.reportViolation('forbidden_key', { key: event.key });
      return false;
    }
    
    // Check forbidden combinations
    for (const combo of forbiddenCombos) {
      if (this.matchesCombo(event, combo)) {
        event.preventDefault();
        event.stopPropagation();
        this.reportViolation('forbidden_combo', { combo });
        return false;
      }
    }
  }

  /**
   * Check if event matches key combination
   */
  matchesCombo(event, combo) {
    return (
      (!combo.ctrl || event.ctrlKey) &&
      (!combo.alt || event.altKey) &&
      (!combo.shift || event.shiftKey) &&
      (!combo.meta || event.metaKey) &&
      event.key === combo.key
    );
  }

  /**
   * Prevent default event
   */
  preventDefaultEvent(event) {
    event.preventDefault();
    event.stopPropagation();
    return false;
  }

  /**
   * Enable developer tools detection
   */
  enableDevToolsDetection() {
    // Method 1: Console detection
    let devtools = { open: false, orientation: null };
    
    setInterval(() => {
      if (window.outerHeight - window.innerHeight > 200 || 
          window.outerWidth - window.innerWidth > 200) {
        if (!devtools.open) {
          devtools.open = true;
          this.reportViolation('devtools_opened', { method: 'size_detection' });
        }
      } else {
        devtools.open = false;
      }
    }, 500);
    
    // Method 2: Console.log timing
    let start = performance.now();
    console.log('%c', 'color: transparent');
    let end = performance.now();
    
    if (end - start > 100) {
      this.reportViolation('devtools_opened', { method: 'console_timing' });
    }
  }

  /**
   * Enforce fullscreen mode
   */
  enforceFullscreen() {
    const enterFullscreen = () => {
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen();
      } else if (document.documentElement.webkitRequestFullscreen) {
        document.documentElement.webkitRequestFullscreen();
      } else if (document.documentElement.msRequestFullscreen) {
        document.documentElement.msRequestFullscreen();
      }
    };
    
    // Enter fullscreen initially
    enterFullscreen();
    
    // Monitor fullscreen changes
    document.addEventListener('fullscreenchange', () => {
      if (!document.fullscreenElement) {
        this.reportViolation('fullscreen_exit', {});
        // Re-enter fullscreen after a short delay
        setTimeout(enterFullscreen, 1000);
      }
    });
    
    document.addEventListener('webkitfullscreenchange', () => {
      if (!document.webkitFullscreenElement) {
        this.reportViolation('fullscreen_exit', {});
        setTimeout(enterFullscreen, 1000);
      }
    });
  }

  /**
   * Enable network monitoring
   */
  enableNetworkMonitoring() {
    // Monitor online/offline status
    window.addEventListener('online', () => {
      console.log('Network connection restored');
    });
    
    window.addEventListener('offline', () => {
      this.reportViolation('network_disconnected', {});
    });
    
    // Monitor for suspicious network activity
    const originalFetch = window.fetch;
    window.fetch = (...args) => {
      const url = args[0];
      if (typeof url === 'string' && !url.startsWith(window.location.origin)) {
        this.reportViolation('external_request', { url });
      }
      return originalFetch.apply(window, args);
    };
  }

  /**
   * Enable performance monitoring
   */
  enablePerformanceMonitoring() {
    // Monitor memory usage
    if ('memory' in performance) {
      setInterval(() => {
        const memory = performance.memory;
        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {

        }
      }, 30000);
    }
    
    // Monitor long tasks
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {

          }
        }
      });
      
      observer.observe({ entryTypes: ['longtask'] });
    }
  }

  /**
   * Enable error boundary
   */
  enableErrorBoundary() {
    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error);
      this.reportViolation('javascript_error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    });
    
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      this.reportViolation('promise_rejection', {
        reason: event.reason?.toString()
      });
    });
  }

  /**
   * Report security violation
   */
  reportViolation(type, details) {
    this.violationCount++;
    
    const violation = {
      type,
      details,
      timestamp: Date.now(),
      count: this.violationCount,
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    console.warn('Security violation:', violation);
    
    // Emit event for external handling
    window.dispatchEvent(new CustomEvent('securityViolation', { detail: violation }));
    
    // Check if max violations reached
    if (this.violationCount >= this.maxViolations) {
      this.handleMaxViolations();
    }
  }

  /**
   * Handle maximum violations reached
   */
  handleMaxViolations() {
    console.error('Maximum security violations reached');
    
    window.dispatchEvent(new CustomEvent('securityDisqualification', {
      detail: {
        reason: 'Maximum security violations exceeded',
        violationCount: this.violationCount,
        timestamp: Date.now()
      }
    }));
  }

  /**
   * Deactivate security measures
   */
  deactivate() {
    if (!this.isActive) {
      return;
    }
    
    console.log('Deactivating security manager...');
    
    // Remove event listeners
    document.removeEventListener('contextmenu', this.preventDefaultEvent, true);
    document.removeEventListener('selectstart', this.preventDefaultEvent, true);
    document.removeEventListener('dragstart', this.preventDefaultEvent, true);
    document.removeEventListener('keydown', this.handleKeyDown, true);
    
    // Remove security meta tags
    if (this.securityMetaTags) {
      this.securityMetaTags.forEach(tag => {
        if (tag.parentNode) {
          tag.parentNode.removeChild(tag);
        }
      });
    }
    
    // Exit fullscreen
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
    
    this.isActive = false;
    console.log('Security manager deactivated');
  }

  /**
   * Get security status
   */
  getStatus() {
    return {
      isActive: this.isActive,
      violationCount: this.violationCount,
      maxViolations: this.maxViolations,
      isFullscreen: !!document.fullscreenElement,
      isOnline: navigator.onLine
    };
  }
}

// Create singleton instance
const securityManager = new SecurityManager();

export default securityManager;
