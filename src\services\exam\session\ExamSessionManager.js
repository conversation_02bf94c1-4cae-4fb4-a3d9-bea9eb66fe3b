/**
 * Exam Session Manager
 * Handles exam session lifecycle management including start, pause, resume, and submission
 */

import EventEmitter from '../../../utils/EventEmitter';
import URL from '../../../utils/api/API_URL';

class ExamSessionManager extends EventEmitter {
  constructor() {
    super();
    this.activeSessions = new Map();
    this.sessionTimers = new Map();
    this.baseUrl = URL;
  }

  /**
   * Start a new exam session
   */
  async startSession(examId, token) {
    try {


      const response = await fetch(`${this.baseUrl}/api/exams/session/exam-session/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ exam_id: examId })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to start exam session');
      }

      const sessionData = await response.json();
      
      // Store session locally
      this.activeSessions.set(sessionData.session_id, {
        ...sessionData,
        status: 'active',
        startTime: Date.now(),
        lastActivity: Date.now()
      });

      // Start session timer
      this.startSessionTimer(sessionData.session_id, sessionData.duration_minutes * 60);

      this.emit('sessionStarted', sessionData);

      
      return sessionData;
      
    } catch (error) {

      this.emit('sessionError', { type: 'start', error: error.message });
      throw error;
    }
  }

  /**
   * Pause an exam session
   */
  async pauseSession(sessionId, reason, token) {
    try {

      
      const response = await fetch(`${this.baseUrl}/api/exams/session/${sessionId}/pause`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ reason })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to pause exam session');
      }

      const result = await response.json();
      
      // Update local session
      const session = this.activeSessions.get(sessionId);
      if (session) {
        session.status = 'paused';
        session.pauseReason = reason;
        session.pausedAt = Date.now();
      }

      // Stop session timer
      this.stopSessionTimer(sessionId);

      this.emit('sessionPaused', { sessionId, reason });

      
      return result;
      
    } catch (error) {

      this.emit('sessionError', { type: 'pause', sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * Resume a paused exam session
   */
  async resumeSession(sessionId, token) {
    try {

      
      const response = await fetch(`${this.baseUrl}/api/exams/session/${sessionId}/resume`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to resume exam session');
      }

      const result = await response.json();
      
      // Update local session
      const session = this.activeSessions.get(sessionId);
      if (session) {
        session.status = 'active';
        session.resumedAt = Date.now();
        delete session.pauseReason;
        delete session.pausedAt;
        
        // Restart timer with remaining time
        this.startSessionTimer(sessionId, result.remaining_time);
      }

      this.emit('sessionResumed', { sessionId });

      
      return result;
      
    } catch (error) {

      this.emit('sessionError', { type: 'resume', sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * Submit exam session according to API specification
   */
  async submitSession(sessionId, answers, token, exam = null, questions = null, isAutoSubmit = false) {
    try {


      // Prepare submission payload according to API specification
      const submissionPayload = { session_id: sessionId };

      // Add complete exam data if provided (new API format)
      if (exam && questions) {

        submissionPayload.exam = exam;
        submissionPayload.questions = questions;
        submissionPayload.student_answers = answers || []; // Allow empty answers for auto-submit
      }

      // Choose the correct endpoint based on submission type
      const endpoint = isAutoSubmit
        ? `${this.baseUrl}/api/exams/session/exam-session/auto-submit`
        : `${this.baseUrl}/api/exams/session/exam-session/submit`;



      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(submissionPayload)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to submit exam session');
      }

      const result = await response.json();
      
      // Update local session
      const session = this.activeSessions.get(sessionId);
      if (session) {
        session.status = 'submitted';
        session.submittedAt = Date.now();
        session.answers = answers;
      }

      // Stop session timer
      this.stopSessionTimer(sessionId);

      this.emit('sessionSubmitted', { sessionId, result });

      
      return result;
      
    } catch (error) {

      this.emit('sessionError', { type: 'submit', sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * Terminate exam session (admin action)
   */
  async terminateSession(sessionId, reason, token) {
    try {

      
      const response = await fetch(`${this.baseUrl}/api/exams/session/${sessionId}/terminate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ reason })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to terminate exam session');
      }

      const result = await response.json();
      
      // Update local session
      const session = this.activeSessions.get(sessionId);
      if (session) {
        session.status = 'terminated';
        session.terminationReason = reason;
        session.terminatedAt = Date.now();
      }

      // Stop session timer
      this.stopSessionTimer(sessionId);

      this.emit('sessionTerminated', { sessionId, reason });

      
      return result;
      
    } catch (error) {

      this.emit('sessionError', { type: 'terminate', sessionId, error: error.message });
      throw error;
    }
  }

  /**
   * Get session status
   */
  getSessionStatus(sessionId) {
    return this.activeSessions.get(sessionId) || null;
  }

  /**
   * Get all active sessions
   */
  getAllActiveSessions() {
    return Array.from(this.activeSessions.values());
  }

  /**
   * Update session activity
   */
  updateSessionActivity(sessionId) {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.lastActivity = Date.now();
    }
  }

  /**
   * Start session timer
   */
  startSessionTimer(sessionId, durationSeconds) {
    // Clear existing timer
    this.stopSessionTimer(sessionId);
    
    const timer = setTimeout(() => {

      this.handleSessionTimeout(sessionId);
    }, durationSeconds * 1000);
    
    this.sessionTimers.set(sessionId, timer);
    
    // Also set up periodic warnings
    this.setupTimeWarnings(sessionId, durationSeconds);
  }

  /**
   * Stop session timer
   */
  stopSessionTimer(sessionId) {
    const timer = this.sessionTimers.get(sessionId);
    if (timer) {
      clearTimeout(timer);
      this.sessionTimers.delete(sessionId);
    }
  }

  /**
   * Setup time warnings
   */
  setupTimeWarnings(sessionId, durationSeconds) {
    // Warning at 30 minutes remaining
    if (durationSeconds > 1800) {
      setTimeout(() => {
        this.emit('timeWarning', { sessionId, type: '30min', remaining: 1800 });
      }, (durationSeconds - 1800) * 1000);
    }
    
    // Warning at 10 minutes remaining
    if (durationSeconds > 600) {
      setTimeout(() => {
        this.emit('timeWarning', { sessionId, type: '10min', remaining: 600 });
      }, (durationSeconds - 600) * 1000);
    }
    
    // Warning at 5 minutes remaining
    if (durationSeconds > 300) {
      setTimeout(() => {
        this.emit('timeWarning', { sessionId, type: '5min', remaining: 300 });
      }, (durationSeconds - 300) * 1000);
    }
  }

  /**
   * Handle session timeout
   */
  handleSessionTimeout(sessionId) {
    const session = this.activeSessions.get(sessionId);
    if (session && session.status === 'active') {
      session.status = 'expired';
      session.expiredAt = Date.now();
      
      this.emit('sessionExpired', { sessionId });

    }
  }

  /**
   * Cleanup session
   */
  cleanupSession(sessionId) {
    this.stopSessionTimer(sessionId);
    this.activeSessions.delete(sessionId);

  }

  /**
   * Cleanup all sessions
   */
  cleanup() {

    
    // Stop all timers
    for (const timer of this.sessionTimers.values()) {
      clearTimeout(timer);
    }
    this.sessionTimers.clear();
    
    // Clear sessions
    this.activeSessions.clear();
    
    // Remove all listeners
    this.removeAllListeners();
    

  }
}

// Create singleton instance
const examSessionManager = new ExamSessionManager();

export default examSessionManager;
