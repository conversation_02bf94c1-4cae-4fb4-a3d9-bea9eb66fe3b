/**
 * Exam Session WebSocket Service
 * Handles real-time communication for exam sessions based on API guide
 */

import URL from '../../../utils/api/API_URL';

class ExamSessionWebSocketService {
  constructor() {
    this.socket = null;
    this.sessionId = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.heartbeatInterval = null;
    this.messageQueue = [];
    this.eventListeners = new Map();
    
    // Bind methods
    this.connect = this.connect.bind(this);
    this.disconnect = this.disconnect.bind(this);
    this.sendMessage = this.sendMessage.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.handleOpen = this.handleOpen.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleError = this.handleError.bind(this);
  }

  /**
   * Connect to session-based WebSocket
   */
  async connect(sessionId, token) {
    try {
      this.sessionId = sessionId;

      console.log('🔌 WebSocket Connection Debug Info:');
      console.log('- Session ID:', sessionId);
      console.log('- Token available:', !!token);
      console.log('- Base URL:', URL);

      // Convert HTTP URL to WebSocket URL
      const baseUrl = URL.replace('https://', 'wss://').replace('http://', 'ws://');
      const wsUrl = `${baseUrl}/api/exams/session/ws/exam-session/${sessionId}`;

      console.log('🔌 Connecting to session WebSocket:', wsUrl);
      console.log('🔌 Full WebSocket URL:', wsUrl);
      
      this.socket = new WebSocket(wsUrl);

      // Set up event handlers
      this.socket.onopen = this.handleOpen;
      this.socket.onmessage = this.handleMessage;
      this.socket.onclose = this.handleClose;
      this.socket.onerror = this.handleError;

      console.log('🔌 WebSocket object created, waiting for connection...');
      
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 10000);
        
        this.socket.onopen = (event) => {
          console.log('🎉 WebSocket onopen event fired');
          clearTimeout(timeout);
          this.handleOpen(event);
          resolve();
        };

        this.socket.onerror = (error) => {
          console.error('🚨 WebSocket onerror event fired:', error);
          clearTimeout(timeout);
          this.handleError(error);
          reject(error);
        };
      });
    } catch (error) {
      console.error('❌ Failed to connect to session WebSocket:', error);
      throw error;
    }
  }

  /**
   * Connect to direct exam WebSocket
   */
  async connectDirect(examId, studentId, token) {
    try {
      // Convert HTTP URL to WebSocket URL
      const baseUrl = URL.replace('https://', 'wss://').replace('http://', 'ws://');
      const wsUrl = `${baseUrl}/ws/exam/${examId}/${studentId}`;
      
      console.log('🔌 Connecting to direct exam WebSocket:', wsUrl);
      
      this.socket = new WebSocket(wsUrl);
      
      // Set up event handlers
      this.socket.onopen = this.handleOpen;
      this.socket.onmessage = this.handleMessage;
      this.socket.onclose = this.handleClose;
      this.socket.onerror = this.handleError;
      
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 10000);
        
        this.socket.onopen = (event) => {
          clearTimeout(timeout);
          this.handleOpen(event);
          
          // Send join exam message for direct connection
          this.sendMessage({
            type: 'join_exam',
            exam_id: examId,
            student_id: studentId,
            timestamp: new Date().toISOString()
          });
          
          resolve();
        };
        
        this.socket.onerror = (error) => {
          clearTimeout(timeout);
          this.handleError(error);
          reject(error);
        };
      });
    } catch (error) {
      console.error('❌ Failed to connect to direct exam WebSocket:', error);
      throw error;
    }
  }

  /**
   * Disconnect from WebSocket
   */
  disconnect() {
    console.log('🔌 Disconnecting from WebSocket...');
    
    this.isConnected = false;
    
    // Clear heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    // Close socket
    if (this.socket) {
      this.socket.close(1000, 'Normal closure');
      this.socket = null;
    }
    
    // Clear session data
    this.sessionId = null;
    this.reconnectAttempts = 0;
    this.messageQueue = [];
  }

  /**
   * Send message to WebSocket
   */
  sendMessage(message) {
    if (this.isConnected && this.socket.readyState === WebSocket.OPEN) {
      const messageStr = JSON.stringify(message);
      console.log('📤 Sending WebSocket message:', message);
      this.socket.send(messageStr);
    } else {
      console.warn('⚠️ WebSocket not connected, queuing message:', message);
      this.messageQueue.push(message);
    }
  }

  /**
   * Send heartbeat
   */
  sendHeartbeat() {
    this.sendMessage({
      type: 'heartbeat',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Save answer with enhanced data format
   */
  saveAnswer(questionId, answer, timeSpentSeconds = 0) {
    const enhancedAnswer = {
      answer: typeof answer === 'string' ? answer : answer.answer || answer.text || '',
      text: typeof answer === 'string' ? answer : answer.answer || answer.text || '', // compatibility
      time_spent_seconds: timeSpentSeconds,
      submitted_at: new Date().toISOString()
    };

    this.sendMessage({
      type: 'save_answer',
      question_id: questionId,
      answer: enhancedAnswer.answer, // Send simple format for compatibility
      enhanced_answer: enhancedAnswer, // Send enhanced format
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Submit exam
   */
  submitExam(examId, studentId, finalAnswers) {
    this.sendMessage({
      type: 'submit_exam',
      exam_id: examId,
      student_id: studentId,
      final_answers: finalAnswers,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Report suspicious activity
   */
  reportSuspiciousActivity(activity, details) {
    this.sendMessage({
      type: 'suspicious_activity',
      activity: activity,
      details: details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Send cheat detection (for session-based)
   */
  sendCheatDetection(details) {
    this.sendMessage({
      type: 'cheat',
      details: details
    });
  }

  /**
   * Send answers update (for session-based)
   */
  sendAnswersUpdate(answers) {
    this.sendMessage({
      answers: answers
    });
  }

  /**
   * Handle WebSocket open
   */
  handleOpen(event) {
    console.log('✅ WebSocket connected successfully');
    this.isConnected = true;
    this.reconnectAttempts = 0;
    
    // Start heartbeat
    this.startHeartbeat();
    
    // Send queued messages
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.sendMessage(message);
    }
    
    // Emit connection event
    this.emit('connected', event);
  }

  /**
   * Handle WebSocket message
   */
  handleMessage(event) {
    try {
      const message = JSON.parse(event.data);
      console.log('📥 Received WebSocket message:', message);
      
      // Handle different message types
      switch (message.type) {
        case 'connection_established':
          this.emit('connection_established', message);
          break;
          
        case 'heartbeat_response':
          this.emit('heartbeat_response', message);
          break;
          
        case 'exam_joined':
          this.emit('exam_joined', message);
          break;
          
        case 'answer_saved':
          this.emit('answer_saved', message);
          break;
          
        case 'exam_submitted':
          this.emit('exam_submitted', message);
          break;
          
        case 'activity_logged':
          this.emit('activity_logged', message);
          break;
          
        case 'session_resume':
          this.emit('session_resume', message);
          break;
          
        case 'answers_saved':
          this.emit('answers_saved', message);
          break;
          
        case 'error':
          this.emit('error', message);
          break;
          
        case 'timeout_warning':
          this.emit('timeout_warning', message);
          break;
          
        default:
          console.warn('⚠️ Unknown message type:', message.type);
          this.emit('unknown_message', message);
      }
      
      // Emit general message event
      this.emit('message', message);
      
    } catch (error) {
      console.error('❌ Failed to parse WebSocket message:', error);
      this.emit('parse_error', { error, rawData: event.data });
    }
  }

  /**
   * Handle WebSocket close
   */
  handleClose(event) {
    console.log('🔌 WebSocket disconnected:', event.code, event.reason);
    this.isConnected = false;
    
    // Clear heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    // Emit disconnection event
    this.emit('disconnected', event);
    
    // Attempt reconnection if not a normal closure
    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.attemptReconnection();
    }
  }

  /**
   * Handle WebSocket error
   */
  handleError(error) {
    console.error('❌ WebSocket error:', error);
    this.emit('error', error);
  }

  /**
   * Start heartbeat interval
   */
  startHeartbeat() {
    // Send heartbeat every 20 seconds (as per API guide rate limits)
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, 20000);
  }

  /**
   * Attempt reconnection
   */
  attemptReconnection() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`🔄 Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms...`);
    
    setTimeout(() => {
      if (this.sessionId) {
        this.connect(this.sessionId);
      }
    }, delay);
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Emit event
   */
  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`❌ Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      sessionId: this.sessionId,
      reconnectAttempts: this.reconnectAttempts,
      readyState: this.socket ? this.socket.readyState : WebSocket.CLOSED
    };
  }
}

// Export singleton instance
export default new ExamSessionWebSocketService();
