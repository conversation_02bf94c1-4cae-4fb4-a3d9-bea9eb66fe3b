/**
 * EduFair Exam WebSocket Service
 * Handles real-time communication during exam sessions
 * 
 * Features:
 * - Automatic reconnection with exponential backoff
 * - Message queuing during disconnections
 * - Heartbeat management
 * - Event-driven architecture
 * - Comprehensive error handling
 */

import EventEmitter from '../../../utils/EventEmitter';
import URL from '../../../utils/api/API_URL';
import { EXAM_CONFIG } from '../../../config/environment';

// WebSocket connection states
export const WS_STATES = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
};

// Message types as per specification
export const MESSAGE_TYPES = {
  HEARTBEAT: 'heartbeat',
  ANSWERS: 'answers',
  CHEAT: 'cheat',
  SESSION_RESUME: 'session_resume',
  ANSWERS_SAVED: 'answers_saved',
  DISQUALIFIED: 'disqualified',
  TIME_UPDATE: 'time_update',
  CONNECTION_STATUS: 'connection_status'
};

// Connection events
export const WS_EVENTS = {
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  RECONNECTING: 'reconnecting',
  MESSAGE_RECEIVED: 'message_received',
  ERROR: 'error',
  DISQUALIFIED: 'disqualified'
};

class ExamWebSocketService extends EventEmitter {
  constructor() {
    super();
    this.ws = null;
    this.sessionId = null;
    this.token = null;
    this.baseUrl = URL.replace('https://', 'wss://').replace('http://', 'ws://');
    
    // Connection management
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = EXAM_CONFIG.MAX_RECONNECT_ATTEMPTS;
    this.reconnectDelay = EXAM_CONFIG.RECONNECT_DELAY;
    this.maxReconnectDelay = EXAM_CONFIG.MAX_RECONNECT_DELAY;
    
    // Message queuing
    this.messageQueue = [];
    this.isOnline = navigator.onLine;
    
    // Heartbeat management
    this.heartbeatInterval = null;
    this.heartbeatFrequency = EXAM_CONFIG.HEARTBEAT_FREQUENCY;
    this.lastHeartbeat = null;
    
    // Bind methods
    this.connect = this.connect.bind(this);
    this.disconnect = this.disconnect.bind(this);
    this.sendMessage = this.sendMessage.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.startHeartbeat = this.startHeartbeat.bind(this);
    this.stopHeartbeat = this.stopHeartbeat.bind(this);
    
    // Monitor network status
    window.addEventListener('online', () => {
      this.isOnline = true;
      if (this.sessionId && !this.isConnected()) {
        this.reconnect();
      }
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.emit(WS_EVENTS.DISCONNECTED, { reason: 'network_offline' });
    });
  }

  /**
   * Connect to exam WebSocket
   * @param {string} sessionId - Exam session ID
   * @param {string} token - JWT authentication token
   */
  async connect(sessionId, token) {
    if (this.isConnecting || this.isConnected()) {

      return;
    }

    this.sessionId = sessionId;
    this.token = token;
    this.isConnecting = true;

    try {
      const wsUrl = `${this.baseUrl}/api/ws/exam-session/${sessionId}?token=${token}`;

      
      this.ws = new WebSocket(wsUrl);
      this.setupEventHandlers();
      
    } catch (error) {

      this.isConnecting = false;
      this.emit(WS_EVENTS.ERROR, error);
      this.scheduleReconnect();
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  setupEventHandlers() {
    if (!this.ws) return;

    this.ws.onopen = () => {

      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.reconnectDelay = EXAM_CONFIG.RECONNECT_DELAY;
      
      this.startHeartbeat();
      this.processMessageQueue();
      this.emit(WS_EVENTS.CONNECTED);
    };

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      } catch (error) {

        this.emit(WS_EVENTS.ERROR, error);
      }
    };

    this.ws.onclose = (event) => {

      this.isConnecting = false;
      this.stopHeartbeat();
      
      // Handle different close codes as per specification
      if (event.code === 1008) {
        // Policy violation - don't reconnect
        this.emit(WS_EVENTS.ERROR, { 
          code: 'POLICY_VIOLATION', 
          message: 'Session invalid or unauthorized' 
        });
      } else if (event.code === 1011) {
        // Internal error - attempt reconnect
        this.emit(WS_EVENTS.DISCONNECTED, { reason: 'server_error' });
        this.scheduleReconnect();
      } else {
        // Normal closure or network issue
        this.emit(WS_EVENTS.DISCONNECTED, { reason: 'connection_lost' });
        if (this.sessionId) {
          this.scheduleReconnect();
        }
      }
    };

    this.ws.onerror = (error) => {

      this.emit(WS_EVENTS.ERROR, error);
    };
  }

  /**
   * Handle incoming WebSocket messages
   */
  handleMessage(data) {

    
    switch (data.type || data.event) {
      case MESSAGE_TYPES.SESSION_RESUME:
        this.emit(WS_EVENTS.MESSAGE_RECEIVED, {
          type: 'session_resume',
          data: {
            currentAnswers: data.current_answers,
            remainingTime: data.remaining_time_seconds,
            strikes: data.strikes,
            sessionId: data.session_id
          }
        });
        break;
        
      case MESSAGE_TYPES.ANSWERS_SAVED:
        this.emit(WS_EVENTS.MESSAGE_RECEIVED, {
          type: 'answers_saved',
          data: { timestamp: data.timestamp }
        });
        break;
        
      case MESSAGE_TYPES.DISQUALIFIED:
        this.stopHeartbeat();
        this.emit(WS_EVENTS.DISQUALIFIED, {
          reason: data.reason || 'Cheating detected'
        });
        break;
        
      case MESSAGE_TYPES.TIME_UPDATE:
        this.emit(WS_EVENTS.MESSAGE_RECEIVED, {
          type: 'time_update',
          data: { remainingTime: data.remaining_time_seconds }
        });
        break;
        
      default:
        this.emit(WS_EVENTS.MESSAGE_RECEIVED, { type: 'unknown', data });
    }
  }

  /**
   * Send message through WebSocket
   */
  sendMessage(message) {
    if (!this.isConnected()) {

      this.messageQueue.push(message);
      return false;
    }

    try {
      this.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {

      this.messageQueue.push(message);
      return false;
    }
  }

  /**
   * Send heartbeat message
   */
  sendHeartbeat() {
    const success = this.sendMessage({ type: MESSAGE_TYPES.HEARTBEAT });
    if (success) {
      this.lastHeartbeat = Date.now();
    }
  }

  /**
   * Start heartbeat interval
   */
  startHeartbeat() {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, this.heartbeatFrequency);
    
    // Send initial heartbeat
    this.sendHeartbeat();
  }

  /**
   * Stop heartbeat interval
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Process queued messages
   */
  processMessageQueue() {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift();
      this.sendMessage(message);
    }
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {

      this.emit(WS_EVENTS.ERROR, { 
        code: 'MAX_RECONNECT_ATTEMPTS', 
        message: 'Unable to reconnect to exam session' 
      });
      return;
    }

    if (!this.isOnline) {

      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);
    

    this.emit(WS_EVENTS.RECONNECTING, { attempt: this.reconnectAttempts, delay });
    
    setTimeout(() => {
      if (this.sessionId && this.token) {
        this.connect(this.sessionId, this.token);
      }
    }, delay);
  }

  /**
   * Manual reconnection
   */
  reconnect() {
    if (this.sessionId && this.token) {
      this.disconnect();
      setTimeout(() => {
        this.connect(this.sessionId, this.token);
      }, EXAM_CONFIG.RECONNECT_DELAY);
    }
  }

  /**
   * Disconnect WebSocket
   */
  disconnect() {
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Normal closure');
      this.ws = null;
    }
    
    this.sessionId = null;
    this.token = null;
    this.isConnecting = false;
    this.messageQueue = [];
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Get connection status
   */
  getConnectionStatus() {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'connecting';
      case WebSocket.OPEN: return 'connected';
      case WebSocket.CLOSING: return 'closing';
      case WebSocket.CLOSED: return 'disconnected';
      default: return 'unknown';
    }
  }

  /**
   * Send answers to server with enhanced format support
   */
  syncAnswers(answers) {
    // Support both simple and enhanced answer formats
    const processedAnswers = {};

    Object.keys(answers).forEach(questionId => {
      const answer = answers[questionId];

      if (typeof answer === 'string') {
        // Simple format - keep as is for compatibility
        processedAnswers[questionId] = answer;
      } else if (typeof answer === 'object' && answer !== null) {
        // Enhanced format - send both formats
        processedAnswers[questionId] = {
          answer: answer.answer || answer.text || '',
          text: answer.text || answer.answer || '', // compatibility alias
          time_spent_seconds: answer.time_spent_seconds || 0,
          submitted_at: answer.submitted_at || new Date().toISOString(),
          last_modified: answer.last_modified || new Date().toISOString()
        };
      }
    });

    return this.sendMessage({
      type: 'sync_answers',
      answers: processedAnswers,
      enhanced_format: true,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Report cheating incident
   */
  reportCheating(type, details = {}) {
    return this.sendMessage({
      type: MESSAGE_TYPES.CHEAT,
      cheat_type: type,
      details: details,
      timestamp: Date.now()
    });
  }
}

// Export singleton instance
export default new ExamWebSocketService();
