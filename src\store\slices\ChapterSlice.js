import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../../utils/api/API_URL";

const BASE_URL = `${URL}/api/chapters`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks

// Fetch all chapters with pagination
export const fetchChapters = createAsyncThunk(
  "chapters/fetchChapters",
  async ({ skip = 0, limit = 100 } = {}, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/?skip=${skip}&limit=${limit}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data; // { chapters: [...], total: N }
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Create chapter
export const createChapter = createAsyncThunk(
  "chapters/createChapter",
  async (chapterData, thunkAPI) => {
    try {
      // Ensure required fields are present
      const requiredData = {
        name: chapterData.name,
        subject_id: chapterData.subject_id,
        description: chapterData.description || "", // Provide default empty string if missing
      };
      
      console.log('Creating chapter with data:', requiredData);
      
      const res = await axios.post(`${BASE_URL}/`, requiredData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      console.error('Error creating chapter:', err.response?.data);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch chapter by ID
export const fetchChapterById = createAsyncThunk(
  "chapters/fetchChapterById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update chapter
export const updateChapter = createAsyncThunk(
  "chapters/updateChapter",
  async ({ id, chapterData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${id}`, chapterData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Delete chapter
export const deleteChapter = createAsyncThunk(
  "chapters/deleteChapter",
  async (id, thunkAPI) => {
    try {
      const res = await axios.delete(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return { id, detail: res.data.detail };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch chapters by subject with pagination
export const fetchChaptersBySubject = createAsyncThunk(
  "chapters/fetchChaptersBySubject",
  async ({ subjectId, skip = 0, limit = 100 }, thunkAPI) => {
    try {
      const res = await axios.get(
        `${BASE_URL}/subjects/${subjectId}/chapters?skip=${skip}&limit=${limit}`,
        {
          headers: { Authorization: `Bearer ${getAuthToken()}` },
        }
      );
      return res.data; // { chapters: [...], total: N }
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch topics for a chapter
export const fetchChapterTopics = createAsyncThunk(
  "chapters/fetchChapterTopics",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}/topics`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data; // chapter object with topics
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  chapters: [],
  total: 0,
  currentChapter: null,
  topics: [],
  chaptersBySubject: [],
  chaptersBySubjectTotal: 0,
  loading: false,
  error: null,
  deleteDetail: null,
};

// Slice
const chapterSlice = createSlice({
  name: "chapters",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch All
      .addCase(fetchChapters.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchChapters.fulfilled, (state, action) => {
        state.loading = false;
        state.chapters = action.payload.chapters;
        state.total = action.payload.total;
      })
      .addCase(fetchChapters.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create
      .addCase(createChapter.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createChapter.fulfilled, (state, action) => {
        state.loading = false;
        state.chapters.push(action.payload);
      })
      .addCase(createChapter.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch by ID
      .addCase(fetchChapterById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchChapterById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentChapter = action.payload;
      })
      .addCase(fetchChapterById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update
      .addCase(updateChapter.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateChapter.fulfilled, (state, action) => {
        state.loading = false;
        const idx = state.chapters.findIndex(c => c.id === action.payload.id);
        if (idx !== -1) {
          state.chapters[idx] = action.payload;
        }
        if (state.currentChapter && state.currentChapter.id === action.payload.id) {
          state.currentChapter = action.payload;
        }
      })
      .addCase(updateChapter.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete
      .addCase(deleteChapter.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.deleteDetail = null;
      })
      .addCase(deleteChapter.fulfilled, (state, action) => {
        state.loading = false;
        state.chapters = state.chapters.filter(
          (chapter) => chapter.id !== action.payload.id
        );
        state.deleteDetail = action.payload.detail;
      })
      .addCase(deleteChapter.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch chapters by subject
      .addCase(fetchChaptersBySubject.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchChaptersBySubject.fulfilled, (state, action) => {
        state.loading = false;
        state.chaptersBySubject = action.payload.chapters;
        state.chaptersBySubjectTotal = action.payload.total;
      })
      .addCase(fetchChaptersBySubject.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch topics for a chapter
      .addCase(fetchChapterTopics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchChapterTopics.fulfilled, (state, action) => {
        state.loading = false;
        state.topics = action.payload.topics || [];
        // Optionally update currentChapter if needed
        state.currentChapter = action.payload;
      })
      .addCase(fetchChapterTopics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default chapterSlice.reducer;
