import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../../utils/api/API_URL";

const API_BASE = `${URL}/api/classrooms`;
const getToken = () => localStorage.getItem("token");

// 1. Create Classroom Endpoint
export const createClassroom = createAsyncThunk(
  "classroom/create",
  async (classroomData, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/create`, classroomData, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Get All Own Classes Endpoint
export const fetchAllOwnClasses = createAsyncThunk(
  "classroom/fetchAllOwn",
  async (_, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/all`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Get Classroom By Id Endpoint
export const fetchClassroomById = createAsyncThunk(
  "classroom/fetchById",
  async (classroom_id, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/${classroom_id}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 4. Update Classroom Endpoint
export const updateClassroom = createAsyncThunk(
  "classroom/update",
  async ({ classroom_id, data }, { rejectWithValue }) => {
    try {
      const res = await axios.put(`${API_BASE}/${classroom_id}`, data, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 5. Delete Classroom Endpoint
export const deleteClassroom = createAsyncThunk(
  "classroom/delete",
  async (classroom_id, { rejectWithValue }) => {
    try {
      const res = await axios.delete(`${API_BASE}/${classroom_id}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 6. Get Classroom By Id For Student Endpoint
export const fetchClassroomForStudent = createAsyncThunk(
  "classroom/fetchForStudent",
  async (classroom_id, { rejectWithValue }) => {
    try {
      console.log('🔍 fetchClassroomForStudent called with classroom_id:', classroom_id);
      const url = `${API_BASE}/student/${classroom_id}`;
      console.log('🔍 fetchClassroomForStudent URL:', url);

      const res = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });

      console.log('✅ fetchClassroomForStudent success:', res.data);
      return res.data;
    } catch (err) {
      console.error('❌ fetchClassroomForStudent error:', err.response?.data || err.message);
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 7. Request Student To Join Classroom Endpoint
export const requestStudentToJoinClassroom = createAsyncThunk(
  "classroom/requestStudentJoin",
  async ({ classroom_id, requestData }, { rejectWithValue }) => {
    try {
      // Combine classroom_id with requestData to send both student_id and classroom_id
      const payload = {
        ...requestData,
        classroom_id: classroom_id
      };

      const res = await axios.post(`${API_BASE}/request/students`, payload, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 8. Get All Requests Sent by Teacher Endpoint
export const getAllRequestsSentByTeacher = createAsyncThunk(
  "classroom/getAllRequestsSentByTeacher",
  async (_, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/classrooms/all/sent/requests`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 9. Get All Students In Classroom Endpoint
export const fetchStudentsInClassroom = createAsyncThunk(
  "classroom/fetchStudents",
  async (classroom_id, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/${classroom_id}/students`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 10. Remove Student From Classroom Endpoint
export const removeStudentFromClassroom = createAsyncThunk(
  "classroom/removeStudent",
  async ({ classroom_id, student_id }, { rejectWithValue }) => {
    try {
      const res = await axios.delete(`${API_BASE}/${classroom_id}/students/${student_id}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 11. Remove Student Request
export const removeStudentRequest = createAsyncThunk(
  "classroom/removeStudentRequest",
  async ({ request_id, classroom_id }, { rejectWithValue }) => {
    try {
      const res = await axios.delete(`${API_BASE}/request/${request_id}/class/${classroom_id}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 12. Accept Student Request
export const acceptStudentRequest = createAsyncThunk(
  "classroom/acceptStudentRequest",
  async (request_id, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/accept/${request_id}`, null, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 12. Get All Own Students Endpoint
export const fetchAllOwnStudents = createAsyncThunk(
  "classroom/fetchAllOwnStudents",
  async (_, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/all/own/students`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 13. Get All Students Not In Classroom Endpoint
export const fetchStudentsNotInClassroom = createAsyncThunk(
  "classroom/fetchStudentsNotInClassroom",
  async ({ classroom_id, skip = 0, limit = 10, username_filter = "" }, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
        username_filter: username_filter,
      });

      const res = await axios.get(`${API_BASE}/all/students/not/in/classroom/${classroom_id}?${params}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 14. Get Teacher's Students for Exam Assignment
export const fetchTeacherStudents = createAsyncThunk(
  "classroom/fetchTeacherStudents",
  async ({ skip = 0, limit = 100, username_filter = "" }, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      if (username_filter) {
        params.append('username_filter', username_filter);
      }

      const res = await axios.get(`${API_BASE}/my/students?${params}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 14. Get My Classrooms Endpoint
export const fetchMyClassrooms = createAsyncThunk(
  "classroom/fetchMyClassrooms",
  async (_, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/my/classrooms`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 15. Get Paginated Classrooms for Dropdown
export const fetchClassroomsPaginated = createAsyncThunk(
  "classroom/fetchPaginated",
  async ({ skip = 0, limit = 20, search = "" }, { rejectWithValue }) => {
    try {
      // Use the existing working endpoint - /api/classrooms/all
      const res = await axios.get(`${API_BASE}/all`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });

      // Handle pagination and search on client side
      let classrooms = Array.isArray(res.data) ? res.data : [];

      // Apply search filter if provided
      if (search) {
        classrooms = classrooms.filter(classroom =>
          classroom.name?.toLowerCase().includes(search.toLowerCase())
        );
      }

      // Apply pagination
      const total = classrooms.length;
      const paginatedClassrooms = classrooms.slice(skip, skip + limit);

      return {
        classrooms: paginatedClassrooms,
        total,
        skip,
        limit
      };
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 16. Get Paginated Students for Dropdown
export const fetchStudentsPaginated = createAsyncThunk(
  "classroom/fetchStudentsPaginated",
  async ({ skip = 0, limit = 20, search = "" }, { rejectWithValue }) => {
    try {
      // Use the existing fetchTeacherStudents logic
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      if (search) {
        params.append('username_filter', search);
      }

      const res = await axios.get(`${API_BASE}/my/students?${params}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });

      // Handle the response - it should be an array of students
      const students = Array.isArray(res.data) ? res.data : [];

      return {
        students,
        total: students.length + skip, // Approximate total for pagination
        skip,
        limit
      };
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 15. Get All Requests For Student Endpoint
export const fetchAllRequestsForStudent = createAsyncThunk(
  "classroom/fetchAllRequestsForStudent",
  async (_, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/all/requests/for/student`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  classrooms: [],
  classroom: null,
  students: [],
  studentsNotInClassroom: [],
  studentRequests: [],
  sentRequests: [], // All requests sent by teacher to students
  ownStudents: [],
  teacherStudents: [], // Students from teacher's classrooms for exam assignment
  myClassrooms: [],

  // Paginated dropdown data
  paginatedClassrooms: [],
  paginatedStudents: [],
  classroomsPagination: { skip: 0, limit: 20, hasMore: true, total: 0 },
  studentsPagination: { skip: 0, limit: 20, hasMore: true, total: 0 },

  loading: false,
  error: null,
  success: null,
};

// Slice
const classroomSlice = createSlice({
  name: "classroom",
  initialState,
  reducers: {
    clearClassroom(state) {
      state.classroom = null;
      state.students = [];
      state.studentsNotInClassroom = [];
      state.studentRequests = [];
      state.sentRequests = [];
      state.teacherStudents = [];
      state.error = null;
      state.success = null;
    },
    clearError(state) {
      state.error = null;
    },
    clearSuccess(state) {
      state.success = null;
    },
    clearSentRequests(state) {
      state.sentRequests = [];
    },
    clearPaginatedData(state) {
      state.paginatedClassrooms = [];
      state.paginatedStudents = [];
      state.classroomsPagination = { skip: 0, limit: 20, hasMore: true, total: 0 };
      state.studentsPagination = { skip: 0, limit: 20, hasMore: true, total: 0 };
    },
  },
  extraReducers: (builder) => {
    builder
      // Create Classroom
      .addCase(createClassroom.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(createClassroom.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Classroom created successfully";
        state.classroom = action.payload;
      })
      .addCase(createClassroom.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch All Own Classes
      .addCase(fetchAllOwnClasses.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllOwnClasses.fulfilled, (state, action) => {
        state.loading = false;
        state.classrooms = action.payload;
      })
      .addCase(fetchAllOwnClasses.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch Classroom By Id
      .addCase(fetchClassroomById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchClassroomById.fulfilled, (state, action) => {
        state.loading = false;
        state.classroom = action.payload;
      })
      .addCase(fetchClassroomById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update Classroom
      .addCase(updateClassroom.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updateClassroom.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Classroom updated successfully";
        state.classroom = action.payload;
      })
      .addCase(updateClassroom.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete Classroom
      .addCase(deleteClassroom.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(deleteClassroom.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Classroom deleted successfully";
        state.classroom = null;
      })
      .addCase(deleteClassroom.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch Classroom For Student
      .addCase(fetchClassroomForStudent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchClassroomForStudent.fulfilled, (state, action) => {
        state.loading = false;
        state.classroom = action.payload;
        // If the classroom data includes students, set them in the state
        if (action.payload && action.payload.students) {
          state.students = action.payload.students;
        }
      })
      .addCase(fetchClassroomForStudent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Request Student To Join Classroom
      .addCase(requestStudentToJoinClassroom.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(requestStudentToJoinClassroom.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Join request sent successfully";
      })
      .addCase(requestStudentToJoinClassroom.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch Students In Classroom
      .addCase(fetchStudentsInClassroom.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchStudentsInClassroom.fulfilled, (state, action) => {
        state.loading = false;
        state.students = action.payload;
      })
      .addCase(fetchStudentsInClassroom.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Remove Student From Classroom
      .addCase(removeStudentFromClassroom.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(removeStudentFromClassroom.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Student removed from classroom successfully";
      })
      .addCase(removeStudentFromClassroom.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Remove Student Request
      .addCase(removeStudentRequest.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(removeStudentRequest.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Student request removed successfully";
      })
      .addCase(removeStudentRequest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Accept Student Request
      .addCase(acceptStudentRequest.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(acceptStudentRequest.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Student request accepted successfully";
      })
      .addCase(acceptStudentRequest.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get All Requests Sent by Teacher
      .addCase(getAllRequestsSentByTeacher.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(getAllRequestsSentByTeacher.fulfilled, (state, action) => {
        state.loading = false;
        state.sentRequests = action.payload;
      })
      .addCase(getAllRequestsSentByTeacher.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch All Own Students
      .addCase(fetchAllOwnStudents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllOwnStudents.fulfilled, (state, action) => {
        state.loading = false;
        state.ownStudents = action.payload;
      })
      .addCase(fetchAllOwnStudents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch Students Not In Classroom
      .addCase(fetchStudentsNotInClassroom.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchStudentsNotInClassroom.fulfilled, (state, action) => {
        state.loading = false;
        state.studentsNotInClassroom = action.payload;
      })
      .addCase(fetchStudentsNotInClassroom.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch My Classrooms
      .addCase(fetchMyClassrooms.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMyClassrooms.fulfilled, (state, action) => {
        state.loading = false;
        state.myClassrooms = action.payload;
      })
      .addCase(fetchMyClassrooms.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch All Requests For Student
      .addCase(fetchAllRequestsForStudent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllRequestsForStudent.fulfilled, (state, action) => {
        state.loading = false;
        state.studentRequests = action.payload;
      })
      .addCase(fetchAllRequestsForStudent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch Teacher Students for Exam Assignment
      .addCase(fetchTeacherStudents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTeacherStudents.fulfilled, (state, action) => {
        state.loading = false;
        state.teacherStudents = action.payload;
      })
      .addCase(fetchTeacherStudents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch Paginated Classrooms
      .addCase(fetchClassroomsPaginated.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchClassroomsPaginated.fulfilled, (state, action) => {
        state.loading = false;


        // Handle different possible response structures
        const classrooms = action.payload?.classrooms || action.payload || [];
        const total = action.payload?.total || classrooms.length;
        const skip = action.payload?.skip || 0;

        // Ensure classrooms is an array
        const classroomsArray = Array.isArray(classrooms) ? classrooms : [];

        if (skip === 0) {
          // Reset data for new search
          state.paginatedClassrooms = classroomsArray;
        } else {
          // Append data for pagination
          state.paginatedClassrooms = [...state.paginatedClassrooms, ...classroomsArray];
        }

        state.classroomsPagination = {
          skip: skip + classroomsArray.length,
          limit: action.payload?.limit || 20,
          total,
          hasMore: skip + classroomsArray.length < total
        };
      })
      .addCase(fetchClassroomsPaginated.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch Paginated Students
      .addCase(fetchStudentsPaginated.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchStudentsPaginated.fulfilled, (state, action) => {
        state.loading = false;


        // Handle different possible response structures
        const students = action.payload?.students || action.payload || [];
        const total = action.payload?.total || students.length;
        const skip = action.payload?.skip || 0;

        // Ensure students is an array
        const studentsArray = Array.isArray(students) ? students : [];

        if (skip === 0) {
          // Reset data for new search
          state.paginatedStudents = studentsArray;
        } else {
          // Append data for pagination
          state.paginatedStudents = [...state.paginatedStudents, ...studentsArray];
        }

        state.studentsPagination = {
          skip: skip + studentsArray.length,
          limit: action.payload?.limit || 20,
          total,
          hasMore: skip + studentsArray.length < total
        };
      })
      .addCase(fetchStudentsPaginated.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearClassroom, clearError, clearSuccess, clearSentRequests, clearPaginatedData } = classroomSlice.actions;
export default classroomSlice.reducer;