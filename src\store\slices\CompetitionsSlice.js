import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Base URL for competitions endpoints
const BASE_URL = import.meta.env.VITE_API_URL || 'https://edufair.duckdns.org';
const API_BASE = `${BASE_URL}/api/competitions`;

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Async Thunks for API calls

// 1. Create Competition from Exam
export const createCompetitionFromExam = createAsyncThunk(
  'competitions/createFromExam',
  async ({ examId, queryParams }, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      
      // Add query parameters
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const res = await axios.post(`${API_BASE}/from-exam/${examId}?${params}`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Get All Competitions
export const fetchCompetitions = createAsyncThunk(
  'competitions/fetchCompetitions',
  async (queryParams = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      
      // Add query parameters
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const res = await axios.get(`${API_BASE}?${params}`);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Assign Mentor to Competition (Organizers Only)
export const assignMentorToCompetition = createAsyncThunk(
  'competitions/assignMentor',
  async ({ competitionId, queryParams }, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      
      // Add query parameters
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(item => params.append(key, item));
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const res = await axios.post(`${API_BASE}/${competitionId}/assign-mentor?${params}`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  // Competitions list
  competitions: [],
  competitionsLoading: false,
  competitionsError: null,
  competitionsPagination: {
    total: 0,
    skip: 0,
    limit: 20
  },

  // Competition creation
  createLoading: false,
  createError: null,
  createSuccess: false,

  // Mentor assignment
  assignMentorLoading: false,
  assignMentorError: null,
  assignMentorSuccess: false,

  // Filters
  filters: {
    skip: 0,
    limit: 20,
    category_id: null,
    status: null
  },

  // UI state
  selectedCompetition: null,
  showCompetitionDetails: false
};

// Competitions Slice
const competitionsSlice = createSlice({
  name: 'competitions',
  initialState,
  reducers: {
    // Update filters
    updateFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },

    // Reset filters
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },

    // Select competition for details view
    selectCompetition: (state, action) => {
      state.selectedCompetition = action.payload;
      state.showCompetitionDetails = true;
    },

    // Close competition details
    closeCompetitionDetails: (state) => {
      state.selectedCompetition = null;
      state.showCompetitionDetails = false;
    },

    // Clear errors
    clearErrors: (state) => {
      state.competitionsError = null;
      state.createError = null;
      state.assignMentorError = null;
    },

    // Clear success states
    clearSuccessStates: (state) => {
      state.createSuccess = false;
      state.assignMentorSuccess = false;
    },

    // Reset competitions state
    resetCompetitionsState: (state) => {
      return initialState;
    }
  },
  extraReducers: (builder) => {
    builder
      // Create Competition from Exam
      .addCase(createCompetitionFromExam.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
        state.createSuccess = false;
      })
      .addCase(createCompetitionFromExam.fulfilled, (state, action) => {
        state.createLoading = false;
        state.createSuccess = true;
        // Add the new competition to the list
        // Ensure competitions is an array before adding
        if (!Array.isArray(state.competitions)) {
          state.competitions = [];
        }
        state.competitions.unshift(action.payload);
      })
      .addCase(createCompetitionFromExam.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
      })

      // Fetch Competitions
      .addCase(fetchCompetitions.pending, (state) => {
        state.competitionsLoading = true;
        state.competitionsError = null;
      })
      .addCase(fetchCompetitions.fulfilled, (state, action) => {
        state.competitionsLoading = false;
        // Handle different possible response structures
        const competitions = action.payload?.competitions || action.payload || [];
        // Ensure competitions is an array
        state.competitions = Array.isArray(competitions) ? competitions : [];

        // Update pagination if provided
        if (action.payload?.total !== undefined) {
          state.competitionsPagination = {
            total: action.payload.total,
            skip: action.payload.skip || 0,
            limit: action.payload.limit || 20
          };
        }
      })
      .addCase(fetchCompetitions.rejected, (state, action) => {
        state.competitionsLoading = false;
        state.competitionsError = action.payload;
      })

      // Assign Mentor to Competition
      .addCase(assignMentorToCompetition.pending, (state) => {
        state.assignMentorLoading = true;
        state.assignMentorError = null;
        state.assignMentorSuccess = false;
      })
      .addCase(assignMentorToCompetition.fulfilled, (state, action) => {
        state.assignMentorLoading = false;
        state.assignMentorSuccess = true;
      })
      .addCase(assignMentorToCompetition.rejected, (state, action) => {
        state.assignMentorLoading = false;
        state.assignMentorError = action.payload;
      });
  }
});

// Actions
export const {
  updateFilters,
  resetFilters,
  selectCompetition,
  closeCompetitionDetails,
  clearErrors,
  clearSuccessStates,
  resetCompetitionsState
} = competitionsSlice.actions;

// Selectors
export const selectCompetitions = (state) => {
  const competitions = state.competitions.competitions;
  // Ensure competitions is always an array
  return Array.isArray(competitions) ? competitions : [];
};
export const selectCompetitionsLoading = (state) => state.competitions.competitionsLoading;
export const selectCompetitionsError = (state) => state.competitions.competitionsError;
export const selectCompetitionsPagination = (state) => state.competitions.competitionsPagination;

export const selectCreateLoading = (state) => state.competitions.createLoading;
export const selectCreateError = (state) => state.competitions.createError;
export const selectCreateSuccess = (state) => state.competitions.createSuccess;

export const selectAssignMentorLoading = (state) => state.competitions.assignMentorLoading;
export const selectAssignMentorError = (state) => state.competitions.assignMentorError;
export const selectAssignMentorSuccess = (state) => state.competitions.assignMentorSuccess;

export const selectFilters = (state) => state.competitions.filters;
export const selectSelectedCompetition = (state) => state.competitions.selectedCompetition;
export const selectShowCompetitionDetails = (state) => state.competitions.showCompetitionDetails;

// Helper selectors
export const selectUpcomingCompetitions = (state) => {
  const competitions = state.competitions.competitions;
  // Ensure competitions is an array before filtering
  if (!Array.isArray(competitions)) {
    return [];
  }
  return competitions.filter(comp => comp.status === 'upcoming');
};

export const selectOngoingCompetitions = (state) => {
  const competitions = state.competitions.competitions;
  // Ensure competitions is an array before filtering
  if (!Array.isArray(competitions)) {
    return [];
  }
  return competitions.filter(comp => comp.status === 'ongoing');
};

export const selectCompletedCompetitions = (state) => {
  const competitions = state.competitions.competitions;
  // Ensure competitions is an array before filtering
  if (!Array.isArray(competitions)) {
    return [];
  }
  return competitions.filter(comp => comp.status === 'completed');
};

export const selectCompetitionsByCategory = (categoryId) => (state) => {
  const competitions = state.competitions.competitions;
  // Ensure competitions is an array before filtering
  if (!Array.isArray(competitions)) {
    return [];
  }
  return competitions.filter(comp => comp.category?.id === categoryId);
};

export default competitionsSlice.reducer;
