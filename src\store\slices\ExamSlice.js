import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../../utils/api/API_URL";

const BASE_URL = `${URL}/api`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks
export const fetchExams = createAsyncThunk(
  "exams/fetchExams",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/exams/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchExamById = createAsyncThunk(
  "exams/fetchExamById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/exams/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const createExam = createAsyncThunk(
  "exams/createExam",
  async (examData, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/exams/`, examData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const updateExam = createAsyncThunk(
  "exams/updateExam",
  async ({ id, examData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/exams/${id}`, examData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const deleteExam = createAsyncThunk(
  "exams/deleteExam",
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/exams/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const createExamWithAssignment = createAsyncThunk(
  "exams/createExamWithAssignment",
  async (payload, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/exams/create-with-assignment`, payload, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Assign Exam to Students/Classrooms
export const assignExam = createAsyncThunk(
  "exams/assignExam",
  async ({ examId, assignmentData }, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/exams/${examId}/assign`, assignmentData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Remove Exam Assignment
export const unassignExam = createAsyncThunk(
  "exams/unassignExam",
  async ({ examId, studentIds }, thunkAPI) => {
    try {
      const res = await axios.delete(`${BASE_URL}/exams/${examId}/unassign`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
        data: { student_ids: studentIds }
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get Current Exam Assignments
export const getExamAssignments = createAsyncThunk(
  "exams/getExamAssignments",
  async (examId, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/exams/${examId}/assignments`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 1. Get Teacher Exams
export const getTeacherExams = createAsyncThunk(
  "exams/getTeacherExams",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/exams/my-exams`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Get Student Upcoming Exams
export const getStudentUpcomingExams = createAsyncThunk(
  "exams/getStudentUpcomingExams",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/exams/student/upcoming`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Get Student Exam by ID
export const getStudentExam = createAsyncThunk(
  "exams/getStudentExam",
  async (exam_id, thunkAPI) => {
    try {
      console.log('getStudentExam - Starting API call for exam_id:', exam_id);
      console.log('getStudentExam - API URL:', `${BASE_URL}/exams/student/${exam_id}`);
      console.log('getStudentExam - Auth token:', getAuthToken() ? 'Present' : 'Missing');

      const res = await axios.get(`${BASE_URL}/exams/student/${exam_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      console.log('getStudentExam - API response:', res.data);
      return res.data;
    } catch (err) {
      console.error('getStudentExam - API error:', err);
      console.error('getStudentExam - Error response:', err.response?.data);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 4. Submit Student Exam Answers
export const submitStudentExam = createAsyncThunk(
  "exams/submitStudentExam",
  async ({ examId, answers }, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/exams/student/${examId}/submit`,
        { answers },
        {
          headers: { Authorization: `Bearer ${getAuthToken()}` },
        }
      );
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 4. Get All Exams List (Admin Only) - Optimized
export const fetchAllExamsList = createAsyncThunk(
  "exams/fetchAllExamsList",
  async ({ skip = 0, limit = 100 } = {}, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/exams/list/all?skip=${skip}&limit=${limit}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return { data: res.data, skip, limit };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 5. Get Teacher's Exams List - Optimized
export const fetchTeacherExamsList = createAsyncThunk(
  "exams/fetchTeacherExamsList",
  async ({ skip = 0, limit = 100 } = {}, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/exams/my-exams/list?skip=${skip}&limit=${limit}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return { data: res.data, skip, limit };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 6. Get Teacher's Exam by ID
export const fetchTeacherExamById = createAsyncThunk(
  "exams/fetchTeacherExamById",
  async (id, thunkAPI) => {
    try {
      const url = `${BASE_URL}/exams/my-exams/${id}`;
      console.log('🌐 Making API call to:', url);
      const res = await axios.get(url, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      console.log('📦 API Response:', res.data);
      return res.data;
    } catch (err) {
      console.error('🚨 API Error:', err);
      console.error('🚨 Error Response:', err.response?.data);
      console.error('🚨 Error Status:', err.response?.status);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 7. Get Comprehensive Exam Details
export const fetchComprehensiveExamDetails = createAsyncThunk(
  "exams/fetchComprehensiveExamDetails",
  async (id, thunkAPI) => {
    try {
      const url = `${BASE_URL}/exams/${id}/comprehensive`;
      console.log('🌐 Making comprehensive API call to:', url);
      const res = await axios.get(url, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      console.log('📊 Comprehensive API Response:', res.data);
      return res.data;
    } catch (err) {
      console.error('❌ Comprehensive API Error:', err.response?.data || err.message);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 8. Get Available Students for Assignment
export const fetchAvailableStudents = createAsyncThunk(
  "exams/fetchAvailableStudents",
  async (id, thunkAPI) => {
    try {
      const url = `${BASE_URL}/exams/${id}/available-students`;
      console.log('🌐 Making available students API call to:', url);
      const res = await axios.get(url, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      console.log('📊 Available Students API Response:', res.data);
      return res.data;
    } catch (err) {
      console.error('❌ Available Students API Error:', err.response?.data || err.message);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 9. Get Exam Statistics
export const fetchExamStatistics = createAsyncThunk(
  "exams/fetchExamStatistics",
  async (id, thunkAPI) => {
    try {
      const url = `${BASE_URL}/exams/${id}/statistics`;
      console.log('🌐 Making statistics API call to:', url);
      const res = await axios.get(url, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      console.log('📊 Statistics API Response:', res.data);
      return res.data;
    } catch (err) {
      console.error('❌ Statistics API Error:', err.response?.data || err.message);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

const initialState = {
  exams: [],
  currentExam: null,
  loading: false,
  error: null,
  success: null,
  assignmentResult: null,
  submitting: false,
  submissionResult: null,
  // Comprehensive exam data
  comprehensiveExamDetails: null,
  availableStudents: [],
  examStatistics: null,
  comprehensiveLoading: false,
  statisticsLoading: false,
  studentsLoading: false,
  // New optimized exam lists
  examsList: {
    data: [],
    loading: false,
    error: null,
    pagination: {
      skip: 0,
      limit: 100,
      hasMore: true
    }
  },
  teacherExamsList: {
    data: [],
    loading: false,
    error: null,
    pagination: {
      skip: 0,
      limit: 100,
      hasMore: true
    }
  },
  // Assignment management state
  assignments: {
    data: null,
    loading: false,
    error: null
  },
  assignmentOperation: {
    loading: false,
    error: null,
    success: null
  }
};

const examSlice = createSlice({
  name: "exams",
  initialState,
  reducers: {
    // Reset exam lists
    resetExamsList: (state) => {
      state.examsList = {
        data: [],
        loading: false,
        error: null,
        pagination: {
          skip: 0,
          limit: 100,
          hasMore: true
        }
      };
    },
    resetTeacherExamsList: (state) => {
      state.teacherExamsList = {
        data: [],
        loading: false,
        error: null,
        pagination: {
          skip: 0,
          limit: 100,
          hasMore: true
        }
      };
    },
    // Clear errors
    clearExamsListError: (state) => {
      state.examsList.error = null;
      state.teacherExamsList.error = null;
    },
    // Set current exam from existing data
    setCurrentExam: (state, action) => {
      state.currentExam = action.payload;
      state.loading = false;
      state.error = null;
    },
    // Clear assignment errors
    clearAssignmentError: (state) => {
      state.assignmentOperation.error = null;
      state.assignments.error = null;
    },
    // Clear assignment success
    clearAssignmentSuccess: (state) => {
      state.assignmentOperation.success = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch all
      .addCase(fetchExams.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchExams.fulfilled, (state, action) => {
        state.loading = false;
        state.exams = action.payload;
      })
      .addCase(fetchExams.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Fetch by ID
      .addCase(fetchExamById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchExamById.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        // Handle enhanced exam details from updated GET endpoint
        state.currentExam = {
          ...action.payload,
          // Ensure compatibility with existing code
          questions: action.payload.questions || [],
          assigned_students: action.payload.assigned_students || [],
          class_number: action.payload.class_number || null,
          assignment_info: action.payload.assignment_info || {}
        };
      })
      .addCase(fetchExamById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Fetch Teacher Exam by ID
      .addCase(fetchTeacherExamById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTeacherExamById.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        // Handle enhanced exam details from updated GET endpoint
        state.currentExam = {
          ...action.payload,
          // Ensure compatibility with existing code
          questions: action.payload.questions || [],
          assigned_students: action.payload.assigned_students || [],
          class_number: action.payload.class_number || null,
          assignment_info: action.payload.assignment_info || {}
        };
      })
      .addCase(fetchTeacherExamById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Fetch Comprehensive Exam Details
      .addCase(fetchComprehensiveExamDetails.pending, (state) => {
        state.comprehensiveLoading = true;
        state.error = null;
      })
      .addCase(fetchComprehensiveExamDetails.fulfilled, (state, action) => {
        state.comprehensiveLoading = false;
        state.error = null;
        state.comprehensiveExamDetails = action.payload;
        // Also update currentExam with comprehensive data
        state.currentExam = {
          ...action.payload,
          // Ensure backward compatibility
          submitted_count: action.payload.statistics?.completed_attempts || 0,
          assigned_count: action.payload.statistics?.total_assigned || 0,
          total_assigned_students: action.payload.statistics?.total_assigned || 0,
        };
      })
      .addCase(fetchComprehensiveExamDetails.rejected, (state, action) => {
        state.comprehensiveLoading = false;
        state.error = action.payload;
      })
      // Fetch Available Students
      .addCase(fetchAvailableStudents.pending, (state) => {
        state.studentsLoading = true;
        state.error = null;
      })
      .addCase(fetchAvailableStudents.fulfilled, (state, action) => {
        state.studentsLoading = false;
        state.error = null;
        state.availableStudents = action.payload;
      })
      .addCase(fetchAvailableStudents.rejected, (state, action) => {
        state.studentsLoading = false;
        state.error = action.payload;
      })
      // Fetch Exam Statistics
      .addCase(fetchExamStatistics.pending, (state) => {
        state.statisticsLoading = true;
        state.error = null;
      })
      .addCase(fetchExamStatistics.fulfilled, (state, action) => {
        state.statisticsLoading = false;
        state.error = null;
        state.examStatistics = action.payload;
      })
      .addCase(fetchExamStatistics.rejected, (state, action) => {
        state.statisticsLoading = false;
        state.error = action.payload;
      })
      // Create
      .addCase(createExam.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(createExam.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Exam created successfully.";
        state.exams.push(action.payload);
      })
      .addCase(createExam.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Update
      .addCase(updateExam.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updateExam.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Exam updated successfully.";
        const idx = state.exams.findIndex(e => e.id === action.payload.id);
        if (idx !== -1) state.exams[idx] = action.payload;
        if (state.currentExam && state.currentExam.id === action.payload.id) {
          state.currentExam = action.payload;
        }
      })
      .addCase(updateExam.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Delete
      .addCase(deleteExam.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(deleteExam.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Exam deleted successfully.";
        state.exams = state.exams.filter(e => e.id !== action.payload);
        if (state.currentExam && state.currentExam.id === action.payload) {
          state.currentExam = null;
        }
      })
      .addCase(deleteExam.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Create with assignment
      .addCase(createExamWithAssignment.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
        state.assignmentResult = null;
      })
      .addCase(createExamWithAssignment.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Exam with assignment created successfully.";
        state.assignmentResult = action.payload;
      })
      .addCase(createExamWithAssignment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.assignmentResult = null;
      })
      // Get Teacher Exams
      .addCase(getTeacherExams.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getTeacherExams.fulfilled, (state, action) => {
        state.loading = false;
        state.exams = action.payload; // Assuming action.payload is the array of exams
      })
      .addCase(getTeacherExams.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Get Student Upcoming Exams
      .addCase(getStudentUpcomingExams.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getStudentUpcomingExams.fulfilled, (state, action) => {
        state.loading = false;
        state.exams = action.payload; // Assuming action.payload is the array of exams
      })
      .addCase(getStudentUpcomingExams.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Get Student Exam by ID
      .addCase(getStudentExam.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getStudentExam.fulfilled, (state, action) => {
        state.loading = false;
        state.currentExam = action.payload; // Assuming action.payload is the exam object
      })
      .addCase(getStudentExam.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Submit Student Exam
      .addCase(submitStudentExam.pending, (state) => {
        state.submitting = true;
        state.error = null;
      })
      .addCase(submitStudentExam.fulfilled, (state, action) => {
        state.submitting = false;
        state.submissionResult = action.payload;
        state.success = "Exam submitted successfully!";
      })
      .addCase(submitStudentExam.rejected, (state, action) => {
        state.submitting = false;
        state.error = action.payload;
      })
      // Fetch All Exams List (Admin Only) - Optimized
      .addCase(fetchAllExamsList.pending, (state) => {
        state.examsList.loading = true;
        state.examsList.error = null;
      })
      .addCase(fetchAllExamsList.fulfilled, (state, action) => {
        state.examsList.loading = false;
        const { data, skip, limit } = action.payload;

        if (skip === 0) {
          // First page - replace data
          state.examsList.data = data;
        } else {
          // Subsequent pages - append data
          state.examsList.data = [...state.examsList.data, ...data];
        }

        state.examsList.pagination = {
          skip: skip + data.length,
          limit,
          hasMore: data.length === limit
        };
      })
      .addCase(fetchAllExamsList.rejected, (state, action) => {
        state.examsList.loading = false;
        state.examsList.error = action.payload;
      })
      // Fetch Teacher's Exams List - Optimized
      .addCase(fetchTeacherExamsList.pending, (state) => {
        state.teacherExamsList.loading = true;
        state.teacherExamsList.error = null;
      })
      .addCase(fetchTeacherExamsList.fulfilled, (state, action) => {
        state.teacherExamsList.loading = false;
        const { data, skip, limit } = action.payload;

        if (skip === 0) {
          // First page - replace data
          state.teacherExamsList.data = data;
        } else {
          // Subsequent pages - append data
          state.teacherExamsList.data = [...state.teacherExamsList.data, ...data];
        }

        state.teacherExamsList.pagination = {
          skip: skip + data.length,
          limit,
          hasMore: data.length === limit
        };
      })
      .addCase(fetchTeacherExamsList.rejected, (state, action) => {
        state.teacherExamsList.loading = false;
        state.teacherExamsList.error = action.payload;
      })

      // Assignment operations
      .addCase(assignExam.pending, (state) => {
        state.assignmentOperation.loading = true;
        state.assignmentOperation.error = null;
        state.assignmentOperation.success = null;
      })
      .addCase(assignExam.fulfilled, (state, action) => {
        state.assignmentOperation.loading = false;
        state.assignmentOperation.success = action.payload.message || "Exam assigned successfully";
        // Update current exam if it matches
        if (state.currentExam && state.currentExam.id === action.payload.exam_id) {
          state.currentExam.assigned_students = action.payload.assigned_student_ids || [];
        }
      })
      .addCase(assignExam.rejected, (state, action) => {
        state.assignmentOperation.loading = false;
        state.assignmentOperation.error = action.payload;
      })
      .addCase(unassignExam.pending, (state) => {
        state.assignmentOperation.loading = true;
        state.assignmentOperation.error = null;
        state.assignmentOperation.success = null;
      })
      .addCase(unassignExam.fulfilled, (state, action) => {
        state.assignmentOperation.loading = false;
        state.assignmentOperation.success = action.payload.message || "Students unassigned successfully";
        // Update current exam if it matches
        if (state.currentExam && state.currentExam.id === action.payload.exam_id) {
          state.currentExam.assigned_students = action.payload.assigned_student_ids || [];
        }
      })
      .addCase(unassignExam.rejected, (state, action) => {
        state.assignmentOperation.loading = false;
        state.assignmentOperation.error = action.payload;
      })
      .addCase(getExamAssignments.pending, (state) => {
        state.assignments.loading = true;
        state.assignments.error = null;
      })
      .addCase(getExamAssignments.fulfilled, (state, action) => {
        state.assignments.loading = false;
        state.assignments.data = action.payload;
      })
      .addCase(getExamAssignments.rejected, (state, action) => {
        state.assignments.loading = false;
        state.assignments.error = action.payload;
      })

      // Comprehensive update

  },
});

// Export actions
export const {
  resetExamsList,
  resetTeacherExamsList,
  clearExamsListError,
  setCurrentExam,
  clearAssignmentError,
  clearAssignmentSuccess
} = examSlice.actions;

export default examSlice.reducer;
