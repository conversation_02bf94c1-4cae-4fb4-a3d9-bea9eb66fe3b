import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Base URL for home tutoring endpoints
const BASE_URL = import.meta.env.VITE_API_URL || 'https://edufair.duckdns.org';
const API_BASE = `${BASE_URL}/api/subscriptions`;

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Async Thunks for API calls

// 1. Search Home Tutors (POST)
export const searchHomeTutors = createAsyncThunk(
  'homeTutoring/searchTutors',
  async (searchParams, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/home-tutors/search`, searchParams);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Get Home Tutors (GET with query params)
export const fetchHomeTutors = createAsyncThunk(
  'homeTutoring/fetchTutors',
  async (queryParams = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      
      // Add query parameters
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(item => params.append(key, item));
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const res = await axios.get(`${API_BASE}/home-tutors?${params}`);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Get My Teacher Profile
export const fetchMyTeacherProfile = createAsyncThunk(
  'homeTutoring/fetchMyProfile',
  async (_, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/teacher/profile`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      if (err.response?.status === 404) {
        return null; // No profile found
      }
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 4. Update Teacher Profile
export const updateTeacherProfile = createAsyncThunk(
  'homeTutoring/updateProfile',
  async (profileData, { rejectWithValue }) => {
    try {
      const res = await axios.put(`${API_BASE}/teacher/profile`, profileData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 5. Enable Home Tutoring
export const enableHomeTutoring = createAsyncThunk(
  'homeTutoring/enableHomeTutoring',
  async (_, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/teacher/enable-home-tutoring`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  // Tutor search results
  tutors: [],
  searchResults: null,
  tutorsLoading: false,
  tutorsError: null,
  
  // Search filters and pagination
  searchFilters: {
    subject_ids: [],
    latitude: null,
    longitude: null,
    radius_km: 10,
    max_hourly_rate: null,
    min_rating: null,
    available_days: [],
    skip: 0,
    limit: 20
  },

  // Teacher profile management
  myProfile: null,
  profileLoading: false,
  profileError: null,
  profileUpdateLoading: false,
  profileUpdateError: null,
  profileUpdateSuccess: false,

  // Home tutoring enablement
  enableLoading: false,
  enableError: null,
  enableSuccess: false,

  // UI state
  selectedTutor: null,
  showTutorDetails: false,
};

// Home Tutoring Slice
const homeTutoringSlice = createSlice({
  name: 'homeTutoring',
  initialState,
  reducers: {
    // Update search filters
    updateSearchFilters: (state, action) => {
      state.searchFilters = { ...state.searchFilters, ...action.payload };
    },

    // Reset search filters
    resetSearchFilters: (state) => {
      state.searchFilters = initialState.searchFilters;
    },

    // Select tutor for details view
    selectTutor: (state, action) => {
      state.selectedTutor = action.payload;
      state.showTutorDetails = true;
    },

    // Close tutor details
    closeTutorDetails: (state) => {
      state.selectedTutor = null;
      state.showTutorDetails = false;
    },

    // Clear errors
    clearErrors: (state) => {
      state.tutorsError = null;
      state.profileError = null;
      state.profileUpdateError = null;
      state.enableError = null;
    },

    // Clear success states
    clearSuccessStates: (state) => {
      state.profileUpdateSuccess = false;
      state.enableSuccess = false;
    },

    // Reset state
    resetHomeTutoringState: (state) => {
      return initialState;
    }
  },
  extraReducers: (builder) => {
    builder
      // Search Home Tutors (POST)
      .addCase(searchHomeTutors.pending, (state) => {
        state.tutorsLoading = true;
        state.tutorsError = null;
      })
      .addCase(searchHomeTutors.fulfilled, (state, action) => {
        state.tutorsLoading = false;
        state.tutors = action.payload.tutors || [];
        state.searchResults = action.payload;
      })
      .addCase(searchHomeTutors.rejected, (state, action) => {
        state.tutorsLoading = false;
        state.tutorsError = action.payload;
      })

      // Fetch Home Tutors (GET)
      .addCase(fetchHomeTutors.pending, (state) => {
        state.tutorsLoading = true;
        state.tutorsError = null;
      })
      .addCase(fetchHomeTutors.fulfilled, (state, action) => {
        state.tutorsLoading = false;
        state.tutors = action.payload.tutors || [];
        state.searchResults = action.payload;
      })
      .addCase(fetchHomeTutors.rejected, (state, action) => {
        state.tutorsLoading = false;
        state.tutorsError = action.payload;
      })

      // Fetch My Teacher Profile
      .addCase(fetchMyTeacherProfile.pending, (state) => {
        state.profileLoading = true;
        state.profileError = null;
      })
      .addCase(fetchMyTeacherProfile.fulfilled, (state, action) => {
        state.profileLoading = false;
        state.myProfile = action.payload;
      })
      .addCase(fetchMyTeacherProfile.rejected, (state, action) => {
        state.profileLoading = false;
        state.profileError = action.payload;
      })

      // Update Teacher Profile
      .addCase(updateTeacherProfile.pending, (state) => {
        state.profileUpdateLoading = true;
        state.profileUpdateError = null;
        state.profileUpdateSuccess = false;
      })
      .addCase(updateTeacherProfile.fulfilled, (state, action) => {
        state.profileUpdateLoading = false;
        state.profileUpdateSuccess = true;
        state.myProfile = action.payload;
      })
      .addCase(updateTeacherProfile.rejected, (state, action) => {
        state.profileUpdateLoading = false;
        state.profileUpdateError = action.payload;
      })

      // Enable Home Tutoring
      .addCase(enableHomeTutoring.pending, (state) => {
        state.enableLoading = true;
        state.enableError = null;
        state.enableSuccess = false;
      })
      .addCase(enableHomeTutoring.fulfilled, (state, action) => {
        state.enableLoading = false;
        state.enableSuccess = true;
        state.myProfile = action.payload;
      })
      .addCase(enableHomeTutoring.rejected, (state, action) => {
        state.enableLoading = false;
        state.enableError = action.payload;
      });
  }
});

// Actions
export const {
  updateSearchFilters,
  resetSearchFilters,
  selectTutor,
  closeTutorDetails,
  clearErrors,
  clearSuccessStates,
  resetHomeTutoringState
} = homeTutoringSlice.actions;

// Selectors
export const selectTutors = (state) => state.homeTutoring.tutors;
export const selectSearchResults = (state) => state.homeTutoring.searchResults;
export const selectTutorsLoading = (state) => state.homeTutoring.tutorsLoading;
export const selectTutorsError = (state) => state.homeTutoring.tutorsError;

export const selectSearchFilters = (state) => state.homeTutoring.searchFilters;
export const selectSelectedTutor = (state) => state.homeTutoring.selectedTutor;
export const selectShowTutorDetails = (state) => state.homeTutoring.showTutorDetails;

export const selectMyProfile = (state) => state.homeTutoring.myProfile;
export const selectProfileLoading = (state) => state.homeTutoring.profileLoading;
export const selectProfileError = (state) => state.homeTutoring.profileError;
export const selectProfileUpdateLoading = (state) => state.homeTutoring.profileUpdateLoading;
export const selectProfileUpdateError = (state) => state.homeTutoring.profileUpdateError;
export const selectProfileUpdateSuccess = (state) => state.homeTutoring.profileUpdateSuccess;

export const selectEnableLoading = (state) => state.homeTutoring.enableLoading;
export const selectEnableError = (state) => state.homeTutoring.enableError;
export const selectEnableSuccess = (state) => state.homeTutoring.enableSuccess;

// Helper selectors
export const selectIsHomeTutoringEnabled = (state) => {
  const profile = state.homeTutoring.myProfile;
  return profile?.offers_home_tutoring || false;
};

export const selectTutorsBySubject = (subjectId) => (state) => {
  const tutors = state.homeTutoring.tutors;
  return tutors.filter(tutor => 
    tutor.subjects?.some(subject => subject.subject_id === subjectId)
  );
};

export const selectNearbyTutors = (maxDistance) => (state) => {
  const tutors = state.homeTutoring.tutors;
  return tutors.filter(tutor => 
    tutor.distance_km <= maxDistance
  );
};

export default homeTutoringSlice.reducer;
