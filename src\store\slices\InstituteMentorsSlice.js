import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Base URL for mentors endpoints - using existing API structure
const BASE_URL = import.meta.env.VITE_API_URL || 'https://edufair.duckdns.org';
const API_BASE = `${BASE_URL}/api/mentors`;
const INSTITUTE_API_BASE = `${BASE_URL}/api/institute`;

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Thunks for institute mentors APIs

// Fetch mentor applications (Institute Dashboard endpoint)
export const fetchMentorApplications = createAsyncThunk(
  "instituteMentors/fetchApplications",
  async ({ skip = 0, limit = 20, status = "" } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      if (status) {
        params.append('status', status);
      }

      // Using the correct institute mentors applications endpoint
      const res = await axios.get(`${INSTITUTE_API_BASE}/mentors/applications?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      // Extract applications from the response
      const applications = res.data.applications || [];
      return {
        applications,
        total: res.data.totalApplications || applications.length,
        skip,
        limit
      };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Approve mentor application (using existing API structure)
export const approveMentorApplication = createAsyncThunk(
  "instituteMentors/approveApplication",
  async ({ applicationId, responseData }, thunkAPI) => {
    try {
      // Using the correct institute mentor application approve endpoint
      const res = await axios.post(`${INSTITUTE_API_BASE}/mentors/applications/${applicationId}/approve`, {
        response: "approved",
        responseMessage: responseData.responseMessage || "Welcome to our institute!",
        finalHourlyRate: responseData.finalHourlyRate,
        finalHoursPerWeek: responseData.finalHoursPerWeek,
        startDate: responseData.startDate
      }, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Reject mentor application (using existing API structure)
export const rejectMentorApplication = createAsyncThunk(
  "instituteMentors/rejectApplication",
  async ({ applicationId, responseData }, thunkAPI) => {
    try {
      // Using the correct institute mentor application reject endpoint
      const res = await axios.post(`${INSTITUTE_API_BASE}/mentors/applications/${applicationId}/reject`, {
        reason: responseData.reason || "Thank you for your application.",
      }, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Invite mentor (using existing API)
export const inviteMentor = createAsyncThunk(
  "instituteMentors/inviteMentor",
  async (invitationData, thunkAPI) => {
    try {
      // Using the correct institute mentor invite endpoint
      const res = await axios.post(`${INSTITUTE_API_BASE}/mentors/invite`, {
        mentorId: invitationData.mentorId,
        invitationMessage: invitationData.invitationMessage,
        offeredHourlyRate: invitationData.offeredHourlyRate,
        expectedHoursPerWeek: invitationData.expectedHoursPerWeek,
        subjectsToCover: invitationData.subjectsToCover,
        startDate: invitationData.startDate
      }, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch mentor invitations
export const fetchMentorInvitations = createAsyncThunk(
  "instituteMentors/fetchInvitations",
  async ({ skip = 0, limit = 20 } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      const res = await axios.get(`${INSTITUTE_API_BASE}/mentors/invitations?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch pending applications only
export const fetchPendingApplications = createAsyncThunk(
  "instituteMentors/fetchPendingApplications",
  async ({ skip = 0, limit = 20 } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      const res = await axios.get(`${INSTITUTE_API_BASE}/mentors/applications/pending?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch all applications (pending + approved + rejected)
export const fetchAllApplications = createAsyncThunk(
  "instituteMentors/fetchAllApplications",
  async ({ skip = 0, limit = 20 } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      const res = await axios.get(`${INSTITUTE_API_BASE}/mentors/applications/all?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch applications history (processed applications)
export const fetchApplicationsHistory = createAsyncThunk(
  "instituteMentors/fetchApplicationsHistory",
  async ({ skip = 0, limit = 20 } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      const res = await axios.get(`${INSTITUTE_API_BASE}/mentors/applications/history?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch received applications from mentors
export const fetchReceivedApplications = createAsyncThunk(
  "instituteMentors/fetchReceivedApplications",
  async ({ skip = 0, limit = 20 } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      const res = await axios.get(`${INSTITUTE_API_BASE}/mentors/applications/received?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch sent invitations
export const fetchSentInvitations = createAsyncThunk(
  "instituteMentors/fetchSentInvitations",
  async ({ skip = 0, limit = 20 } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      const res = await axios.get(`${INSTITUTE_API_BASE}/mentors/invitations/sent?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch all mentors (using existing public mentors API + institute dashboard)
export const fetchInstituteMentors = createAsyncThunk(
  "instituteMentors/fetchMentors",
  async ({ skip = 0, limit = 20, status = "", expertise = "" } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      if (status) {
        params.append('status', status);
      }
      if (expertise) {
        params.append('expertise', expertise);
      }

      // Get institute active mentors data
      const dashboardRes = await axios.get(`${INSTITUTE_API_BASE}/mentors/active?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      // Get the mentors data from dashboard response
      const mentors = dashboardRes.data.data || [];

      return {
        data: mentors,
        total: dashboardRes.data.total || mentors.length,
        skip,
        limit
      };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get mentor by ID (using existing public API)
export const fetchMentorById = createAsyncThunk(
  "instituteMentors/fetchMentorById",
  async (mentorId, thunkAPI) => {
    try {
      // Using the correct institute mentor details endpoint
      const res = await axios.get(`${INSTITUTE_API_BASE}/mentors/${mentorId}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update mentor
export const updateMentor = createAsyncThunk(
  "instituteMentors/updateMentor",
  async ({ mentorId, mentorData }, thunkAPI) => {
    try {
      const res = await axios.put(`${INSTITUTE_API_BASE}/mentors/${mentorId}`, mentorData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Activate mentor
export const activateMentor = createAsyncThunk(
  "instituteMentors/activateMentor",
  async (mentorId, thunkAPI) => {
    try {
      const res = await axios.post(`${INSTITUTE_API_BASE}/mentors/${mentorId}/activate`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Deactivate mentor
export const deactivateMentor = createAsyncThunk(
  "instituteMentors/deactivateMentor",
  async ({ mentorId, reason }, thunkAPI) => {
    try {
      const res = await axios.post(`${INSTITUTE_API_BASE}/mentors/${mentorId}/deactivate`, {
        reason: reason || "Institute decision"
      }, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Terminate mentor association (legacy function - use deactivateMentor instead)
export const terminateMentorAssociation = createAsyncThunk(
  "instituteMentors/terminateAssociation",
  async ({ mentorId, reason }, thunkAPI) => {
    try {
      // Redirect to deactivate mentor endpoint
      const res = await axios.post(`${INSTITUTE_API_BASE}/mentors/${mentorId}/deactivate`, {
        reason: reason || "Institute decision"
      }, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get mentor performance (using existing mentor profile API)
export const fetchMentorPerformance = createAsyncThunk(
  "instituteMentors/fetchPerformance",
  async (mentorId, thunkAPI) => {
    try {
      // Using the correct institute mentor performance endpoint
      const res = await axios.get(`${INSTITUTE_API_BASE}/mentors/${mentorId}/performance`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      // Extract performance data from mentor profile
      const mentor = res.data;
      return {
        mentorId,
        averageRating: mentor.averageRating || 0,
        totalSessions: mentor.totalSessions || 0,
        completedSessions: mentor.completedSessions || 0,
        responseTime: mentor.responseTime || 'N/A',
        satisfactionScore: mentor.satisfactionScore || 0,
        expertise: mentor.expertise || [],
        joinDate: mentor.joinDate,
        lastActive: mentor.lastActive
      };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get mentor assignments (using existing associations API)
export const fetchMentorAssignments = createAsyncThunk(
  "instituteMentors/fetchAssignments",
  async (mentorId, thunkAPI) => {
    try {
      // Using the correct institute mentor assignments endpoint
      const res = await axios.get(`${INSTITUTE_API_BASE}/mentors/${mentorId}/assignments`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      // Filter for current institute's associations
      const assignments = res.data.filter(association =>
        association.status === 'approved' || association.status === 'active'
      );

      return assignments;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Assign competition to mentor
export const assignCompetitionToMentor = createAsyncThunk(
  "instituteMentors/assignCompetition",
  async ({ mentorId, competitionData }, thunkAPI) => {
    try {
      const res = await axios.post(`${INSTITUTE_API_BASE}/mentors/${mentorId}/assign-competition`, competitionData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get mentor performance report (using dashboard analytics)
export const fetchMentorPerformanceReport = createAsyncThunk(
  "instituteMentors/fetchPerformanceReport",
  async (_, thunkAPI) => {
    try {
      // Using the existing institute dashboard analytics endpoint
      const res = await axios.get(`${INSTITUTE_API_BASE}/dashboard/analytics`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      // Extract mentor performance data from analytics
      return {
        mentorEffectiveness: res.data.mentorEffectiveness || [],
        totalMentors: res.data.totalMentors || 0,
        activeMentors: res.data.activeMentors || 0,
        averageRating: res.data.averageMentorRating || 0,
        totalSessions: res.data.totalMentorSessions || 0
      };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Get mentor utilization report (using dashboard analytics)
export const fetchMentorUtilizationReport = createAsyncThunk(
  "instituteMentors/fetchUtilizationReport",
  async (_, thunkAPI) => {
    try {
      // Using the existing institute dashboard analytics endpoint
      const res = await axios.get(`${INSTITUTE_API_BASE}/dashboard/analytics`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      // Extract utilization data
      return {
        utilizationRate: res.data.mentorUtilizationRate || 0,
        hoursWorked: res.data.totalMentorHours || 0,
        hoursAvailable: res.data.totalAvailableHours || 0,
        mentorDistribution: res.data.mentorDistribution || []
      };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial state
const initialState = {
  // Mentor applications
  applications: {
    data: [],
    total: 0,
    pagination: {
      skip: 0,
      limit: 20,
      hasMore: true,
    },
  },
  applicationsLoading: false,
  applicationsError: null,

  // Application actions
  approveLoading: false,
  approveError: null,
  approveSuccess: false,
  rejectLoading: false,
  rejectError: null,
  rejectSuccess: false,

  // Mentor invitations
  invitations: {
    data: [],
    total: 0,
    pagination: {
      skip: 0,
      limit: 20,
      hasMore: true,
    },
  },
  invitationsLoading: false,
  invitationsError: null,
  inviteLoading: false,
  inviteError: null,
  inviteSuccess: false,

  // Mentors list
  mentors: {
    data: [],
    total: 0,
    pagination: {
      skip: 0,
      limit: 20,
      hasMore: true,
    },
  },
  mentorsLoading: false,
  mentorsError: null,

  // Current mentor
  currentMentor: null,
  currentMentorLoading: false,
  currentMentorError: null,

  // Mentor actions
  updateLoading: false,
  updateError: null,
  updateSuccess: false,
  terminateLoading: false,
  terminateError: null,
  terminateSuccess: false,

  // Mentor performance
  performance: {},
  performanceLoading: false,
  performanceError: null,

  // Mentor assignments
  assignments: [],
  assignmentsLoading: false,
  assignmentsError: null,

  // Reports
  performanceReport: {},
  performanceReportLoading: false,
  performanceReportError: null,
  utilizationReport: {},
  utilizationReportLoading: false,
  utilizationReportError: null,
};

// Institute Mentors Slice
const instituteMentorsSlice = createSlice({
  name: "instituteMentors",
  initialState,
  reducers: {
    // Clear all errors
    clearErrors: (state) => {
      state.applicationsError = null;
      state.approveError = null;
      state.rejectError = null;
      state.invitationsError = null;
      state.inviteError = null;
      state.mentorsError = null;
      state.currentMentorError = null;
      state.updateError = null;
      state.terminateError = null;
      state.performanceError = null;
      state.assignmentsError = null;
      state.performanceReportError = null;
      state.utilizationReportError = null;
    },

    // Clear success states
    clearSuccessStates: (state) => {
      state.approveSuccess = false;
      state.rejectSuccess = false;
      state.inviteSuccess = false;
      state.updateSuccess = false;
      state.terminateSuccess = false;
    },

    // Reset current mentor
    resetCurrentMentor: (state) => {
      state.currentMentor = null;
      state.currentMentorError = null;
    },

    // Reset pagination
    resetPagination: (state, action) => {
      const { dataType } = action.payload;
      if (state[dataType]) {
        state[dataType].pagination = {
          skip: 0,
          limit: 20,
          hasMore: true,
        };
        state[dataType].data = [];
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Applications
      .addCase(fetchMentorApplications.pending, (state) => {
        state.applicationsLoading = true;
        state.applicationsError = null;
      })
      .addCase(fetchMentorApplications.fulfilled, (state, action) => {
        state.applicationsLoading = false;
        const { applications, total, skip, limit } = action.payload;
        
        if (skip === 0) {
          state.applications.data = applications || [];
        } else {
          state.applications.data = [...state.applications.data, ...(applications || [])];
        }
        
        state.applications.total = total || 0;
        state.applications.pagination = {
          skip: skip + (applications?.length || 0),
          limit,
          hasMore: (applications?.length || 0) === limit,
        };
      })
      .addCase(fetchMentorApplications.rejected, (state, action) => {
        state.applicationsLoading = false;
        state.applicationsError = action.payload;
      })

      // Approve Application
      .addCase(approveMentorApplication.pending, (state) => {
        state.approveLoading = true;
        state.approveError = null;
        state.approveSuccess = false;
      })
      .addCase(approveMentorApplication.fulfilled, (state, action) => {
        state.approveLoading = false;
        state.approveSuccess = true;
        // Update application status in the list
        const index = state.applications.data.findIndex(app => app.id === action.payload.id);
        if (index !== -1) {
          state.applications.data[index] = action.payload;
        }
      })
      .addCase(approveMentorApplication.rejected, (state, action) => {
        state.approveLoading = false;
        state.approveError = action.payload;
      })

      // Reject Application
      .addCase(rejectMentorApplication.pending, (state) => {
        state.rejectLoading = true;
        state.rejectError = null;
        state.rejectSuccess = false;
      })
      .addCase(rejectMentorApplication.fulfilled, (state, action) => {
        state.rejectLoading = false;
        state.rejectSuccess = true;
        // Update application status in the list
        const index = state.applications.data.findIndex(app => app.id === action.payload.id);
        if (index !== -1) {
          state.applications.data[index] = action.payload;
        }
      })
      .addCase(rejectMentorApplication.rejected, (state, action) => {
        state.rejectLoading = false;
        state.rejectError = action.payload;
      })

      // Invite Mentor
      .addCase(inviteMentor.pending, (state) => {
        state.inviteLoading = true;
        state.inviteError = null;
        state.inviteSuccess = false;
      })
      .addCase(inviteMentor.fulfilled, (state, action) => {
        state.inviteLoading = false;
        state.inviteSuccess = true;
        // Add new invitation to the list
        state.invitations.data.unshift(action.payload);
        state.invitations.total += 1;
      })
      .addCase(inviteMentor.rejected, (state, action) => {
        state.inviteLoading = false;
        state.inviteError = action.payload;
      })

      // Fetch Invitations
      .addCase(fetchMentorInvitations.pending, (state) => {
        state.invitationsLoading = true;
        state.invitationsError = null;
      })
      .addCase(fetchMentorInvitations.fulfilled, (state, action) => {
        state.invitationsLoading = false;
        const { data, total, skip, limit } = action.payload;

        if (skip === 0) {
          state.invitations.data = data || [];
        } else {
          state.invitations.data = [...state.invitations.data, ...(data || [])];
        }

        state.invitations.total = total || 0;
        state.invitations.pagination = {
          skip: skip + (data?.length || 0),
          limit,
          hasMore: (data?.length || 0) === limit,
        };
      })
      .addCase(fetchMentorInvitations.rejected, (state, action) => {
        state.invitationsLoading = false;
        state.invitationsError = action.payload;
      })

      // Fetch Mentors
      .addCase(fetchInstituteMentors.pending, (state) => {
        state.mentorsLoading = true;
        state.mentorsError = null;
      })
      .addCase(fetchInstituteMentors.fulfilled, (state, action) => {
        state.mentorsLoading = false;
        const { data, total, skip, limit } = action.payload;

        if (skip === 0) {
          state.mentors.data = data || [];
        } else {
          state.mentors.data = [...state.mentors.data, ...(data || [])];
        }

        state.mentors.total = total || 0;
        state.mentors.pagination = {
          skip: skip + (data?.length || 0),
          limit,
          hasMore: (data?.length || 0) === limit,
        };
      })
      .addCase(fetchInstituteMentors.rejected, (state, action) => {
        state.mentorsLoading = false;
        state.mentorsError = action.payload;
      })

      // Fetch Mentor By ID
      .addCase(fetchMentorById.pending, (state) => {
        state.currentMentorLoading = true;
        state.currentMentorError = null;
      })
      .addCase(fetchMentorById.fulfilled, (state, action) => {
        state.currentMentorLoading = false;
        state.currentMentor = action.payload;
      })
      .addCase(fetchMentorById.rejected, (state, action) => {
        state.currentMentorLoading = false;
        state.currentMentorError = action.payload;
      })

      // Update Mentor
      .addCase(updateMentor.pending, (state) => {
        state.updateLoading = true;
        state.updateError = null;
        state.updateSuccess = false;
      })
      .addCase(updateMentor.fulfilled, (state, action) => {
        state.updateLoading = false;
        state.updateSuccess = true;
        state.currentMentor = action.payload;
        // Update mentor in the list
        const index = state.mentors.data.findIndex(mentor => mentor.id === action.payload.id);
        if (index !== -1) {
          state.mentors.data[index] = action.payload;
        }
      })
      .addCase(updateMentor.rejected, (state, action) => {
        state.updateLoading = false;
        state.updateError = action.payload;
      })

      // Terminate Mentor Association
      .addCase(terminateMentorAssociation.pending, (state) => {
        state.terminateLoading = true;
        state.terminateError = null;
        state.terminateSuccess = false;
      })
      .addCase(terminateMentorAssociation.fulfilled, (state, action) => {
        state.terminateLoading = false;
        state.terminateSuccess = true;
        // Update association status in the list
        const mentorIndex = state.mentors.data.findIndex(mentor =>
          mentor.associationId === action.payload.id
        );
        if (mentorIndex !== -1) {
          state.mentors.data[mentorIndex].associationStatus = 'terminated';
        }
      })
      .addCase(terminateMentorAssociation.rejected, (state, action) => {
        state.terminateLoading = false;
        state.terminateError = action.payload;
      })

      // Fetch Mentor Performance
      .addCase(fetchMentorPerformance.pending, (state) => {
        state.performanceLoading = true;
        state.performanceError = null;
      })
      .addCase(fetchMentorPerformance.fulfilled, (state, action) => {
        state.performanceLoading = false;
        state.performance = action.payload;
      })
      .addCase(fetchMentorPerformance.rejected, (state, action) => {
        state.performanceLoading = false;
        state.performanceError = action.payload;
      })

      // Fetch Mentor Assignments
      .addCase(fetchMentorAssignments.pending, (state) => {
        state.assignmentsLoading = true;
        state.assignmentsError = null;
      })
      .addCase(fetchMentorAssignments.fulfilled, (state, action) => {
        state.assignmentsLoading = false;
        state.assignments = action.payload?.assignments || action.payload || [];
      })
      .addCase(fetchMentorAssignments.rejected, (state, action) => {
        state.assignmentsLoading = false;
        state.assignmentsError = action.payload;
      })

      // Fetch Performance Report
      .addCase(fetchMentorPerformanceReport.pending, (state) => {
        state.performanceReportLoading = true;
        state.performanceReportError = null;
      })
      .addCase(fetchMentorPerformanceReport.fulfilled, (state, action) => {
        state.performanceReportLoading = false;
        state.performanceReport = action.payload;
      })
      .addCase(fetchMentorPerformanceReport.rejected, (state, action) => {
        state.performanceReportLoading = false;
        state.performanceReportError = action.payload;
      })

      // Fetch Utilization Report
      .addCase(fetchMentorUtilizationReport.pending, (state) => {
        state.utilizationReportLoading = true;
        state.utilizationReportError = null;
      })
      .addCase(fetchMentorUtilizationReport.fulfilled, (state, action) => {
        state.utilizationReportLoading = false;
        state.utilizationReport = action.payload;
      })
      .addCase(fetchMentorUtilizationReport.rejected, (state, action) => {
        state.utilizationReportLoading = false;
        state.utilizationReportError = action.payload;
      });
  },
});

// Export actions
export const { clearErrors, clearSuccessStates, resetCurrentMentor, resetPagination } = instituteMentorsSlice.actions;

// Export selectors
export const selectApplications = (state) => state.instituteMentors.applications;
export const selectApplicationsLoading = (state) => state.instituteMentors.applicationsLoading;
export const selectApplicationsError = (state) => state.instituteMentors.applicationsError;

export const selectApproveLoading = (state) => state.instituteMentors.approveLoading;
export const selectApproveError = (state) => state.instituteMentors.approveError;
export const selectApproveSuccess = (state) => state.instituteMentors.approveSuccess;

export const selectRejectLoading = (state) => state.instituteMentors.rejectLoading;
export const selectRejectError = (state) => state.instituteMentors.rejectError;
export const selectRejectSuccess = (state) => state.instituteMentors.rejectSuccess;

export const selectInvitations = (state) => state.instituteMentors.invitations;
export const selectInvitationsLoading = (state) => state.instituteMentors.invitationsLoading;
export const selectInvitationsError = (state) => state.instituteMentors.invitationsError;

export const selectInviteLoading = (state) => state.instituteMentors.inviteLoading;
export const selectInviteError = (state) => state.instituteMentors.inviteError;
export const selectInviteSuccess = (state) => state.instituteMentors.inviteSuccess;

export const selectMentors = (state) => state.instituteMentors.mentors;
export const selectMentorsLoading = (state) => state.instituteMentors.mentorsLoading;
export const selectMentorsError = (state) => state.instituteMentors.mentorsError;

export const selectCurrentMentor = (state) => state.instituteMentors.currentMentor;
export const selectCurrentMentorLoading = (state) => state.instituteMentors.currentMentorLoading;
export const selectCurrentMentorError = (state) => state.instituteMentors.currentMentorError;

export const selectUpdateLoading = (state) => state.instituteMentors.updateLoading;
export const selectUpdateError = (state) => state.instituteMentors.updateError;
export const selectUpdateSuccess = (state) => state.instituteMentors.updateSuccess;

export const selectTerminateLoading = (state) => state.instituteMentors.terminateLoading;
export const selectTerminateError = (state) => state.instituteMentors.terminateError;
export const selectTerminateSuccess = (state) => state.instituteMentors.terminateSuccess;

export const selectMentorPerformance = (state) => state.instituteMentors.performance;
export const selectMentorPerformanceLoading = (state) => state.instituteMentors.performanceLoading;
export const selectMentorPerformanceError = (state) => state.instituteMentors.performanceError;

export const selectMentorAssignments = (state) => state.instituteMentors.assignments;
export const selectMentorAssignmentsLoading = (state) => state.instituteMentors.assignmentsLoading;
export const selectMentorAssignmentsError = (state) => state.instituteMentors.assignmentsError;

export const selectPerformanceReport = (state) => state.instituteMentors.performanceReport;
export const selectPerformanceReportLoading = (state) => state.instituteMentors.performanceReportLoading;
export const selectPerformanceReportError = (state) => state.instituteMentors.performanceReportError;

export const selectUtilizationReport = (state) => state.instituteMentors.utilizationReport;
export const selectUtilizationReportLoading = (state) => state.instituteMentors.utilizationReportLoading;
export const selectUtilizationReportError = (state) => state.instituteMentors.utilizationReportError;

export default instituteMentorsSlice.reducer;
