import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../../utils/api/API_URL";

const BASE_URL = `${URL}/api/institute/reports`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks for institute reports APIs

// Mentor Reports
export const fetchMentorPerformanceReport = createAsyncThunk(
  "instituteReports/fetchMentorPerformance",
  async ({ dateRange, mentorId } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams();
      if (dateRange) {
        params.append('dateRange', dateRange);
      }
      if (mentorId) {
        params.append('mentorId', mentorId);
      }

      const res = await axios.get(`${BASE_URL}/mentor-performance?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchMentorUtilizationReport = createAsyncThunk(
  "instituteReports/fetchMentorUtilization",
  async ({ dateRange } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams();
      if (dateRange) {
        params.append('dateRange', dateRange);
      }

      const res = await axios.get(`${BASE_URL}/mentor-utilization?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchMentorApplicationsSummary = createAsyncThunk(
  "instituteReports/fetchMentorApplicationsSummary",
  async ({ dateRange } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams();
      if (dateRange) {
        params.append('dateRange', dateRange);
      }

      const res = await axios.get(`${BASE_URL}/mentor-applications-summary?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchMentorSatisfactionReport = createAsyncThunk(
  "instituteReports/fetchMentorSatisfaction",
  async ({ dateRange } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams();
      if (dateRange) {
        params.append('dateRange', dateRange);
      }

      const res = await axios.get(`${BASE_URL}/mentor-satisfaction?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Event Reports
export const fetchEventPerformanceReport = createAsyncThunk(
  "instituteReports/fetchEventPerformance",
  async ({ dateRange, eventId } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams();
      if (dateRange) {
        params.append('dateRange', dateRange);
      }
      if (eventId) {
        params.append('eventId', eventId);
      }

      const res = await axios.get(`${BASE_URL}/event-performance?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchEventAttendanceReport = createAsyncThunk(
  "instituteReports/fetchEventAttendance",
  async ({ dateRange, eventId } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams();
      if (dateRange) {
        params.append('dateRange', dateRange);
      }
      if (eventId) {
        params.append('eventId', eventId);
      }

      const res = await axios.get(`${BASE_URL}/event-attendance?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchEventFeedbackSummary = createAsyncThunk(
  "instituteReports/fetchEventFeedbackSummary",
  async ({ dateRange, eventId } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams();
      if (dateRange) {
        params.append('dateRange', dateRange);
      }
      if (eventId) {
        params.append('eventId', eventId);
      }

      const res = await axios.get(`${BASE_URL}/event-feedback-summary?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchEventROIAnalysis = createAsyncThunk(
  "instituteReports/fetchEventROIAnalysis",
  async ({ dateRange, eventId } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams();
      if (dateRange) {
        params.append('dateRange', dateRange);
      }
      if (eventId) {
        params.append('eventId', eventId);
      }

      const res = await axios.get(`${BASE_URL}/event-roi-analysis?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Custom Reports
export const createCustomMentorReport = createAsyncThunk(
  "instituteReports/createCustomMentorReport",
  async (reportConfig, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/custom-mentor-report`, reportConfig, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const createCustomEventReport = createAsyncThunk(
  "instituteReports/createCustomEventReport",
  async (reportConfig, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/custom-event-report`, reportConfig, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Export & Scheduling
export const exportReport = createAsyncThunk(
  "instituteReports/exportReport",
  async ({ reportType, reportId, format = 'pdf' }, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/export`, {
        reportType,
        reportId,
        format,
      }, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
        responseType: 'blob',
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchScheduledReports = createAsyncThunk(
  "instituteReports/fetchScheduledReports",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/scheduled`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const scheduleReport = createAsyncThunk(
  "instituteReports/scheduleReport",
  async (scheduleConfig, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/schedule`, scheduleConfig, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const deleteScheduledReport = createAsyncThunk(
  "instituteReports/deleteScheduledReport",
  async (scheduleId, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/scheduled/${scheduleId}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return scheduleId;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchExportHistory = createAsyncThunk(
  "instituteReports/fetchExportHistory",
  async ({ skip = 0, limit = 20 } = {}, thunkAPI) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
      });

      const res = await axios.get(`${BASE_URL}/export-history?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial state
const initialState = {
  // Mentor Reports
  mentorPerformanceReport: {},
  mentorPerformanceLoading: false,
  mentorPerformanceError: null,

  mentorUtilizationReport: {},
  mentorUtilizationLoading: false,
  mentorUtilizationError: null,

  mentorApplicationsSummary: {},
  mentorApplicationsLoading: false,
  mentorApplicationsError: null,

  mentorSatisfactionReport: {},
  mentorSatisfactionLoading: false,
  mentorSatisfactionError: null,

  // Event Reports
  eventPerformanceReport: {},
  eventPerformanceLoading: false,
  eventPerformanceError: null,

  eventAttendanceReport: {},
  eventAttendanceLoading: false,
  eventAttendanceError: null,

  eventFeedbackSummary: {},
  eventFeedbackLoading: false,
  eventFeedbackError: null,

  eventROIAnalysis: {},
  eventROILoading: false,
  eventROIError: null,

  // Custom Reports
  customReportLoading: false,
  customReportError: null,
  customReportSuccess: false,

  // Export & Scheduling
  exportLoading: false,
  exportError: null,
  exportSuccess: false,

  scheduledReports: [],
  scheduledReportsLoading: false,
  scheduledReportsError: null,

  scheduleLoading: false,
  scheduleError: null,
  scheduleSuccess: false,

  exportHistory: {
    data: [],
    total: 0,
    pagination: {
      skip: 0,
      limit: 20,
      hasMore: true,
    },
  },
  exportHistoryLoading: false,
  exportHistoryError: null,
};

// Institute Reports Slice
const instituteReportsSlice = createSlice({
  name: "instituteReports",
  initialState,
  reducers: {
    // Clear all errors
    clearErrors: (state) => {
      state.mentorPerformanceError = null;
      state.mentorUtilizationError = null;
      state.mentorApplicationsError = null;
      state.mentorSatisfactionError = null;
      state.eventPerformanceError = null;
      state.eventAttendanceError = null;
      state.eventFeedbackError = null;
      state.eventROIError = null;
      state.customReportError = null;
      state.exportError = null;
      state.scheduledReportsError = null;
      state.scheduleError = null;
      state.exportHistoryError = null;
    },

    // Clear success states
    clearSuccessStates: (state) => {
      state.customReportSuccess = false;
      state.exportSuccess = false;
      state.scheduleSuccess = false;
    },

    // Reset reports data
    resetReportsData: (state) => {
      state.mentorPerformanceReport = {};
      state.mentorUtilizationReport = {};
      state.mentorApplicationsSummary = {};
      state.mentorSatisfactionReport = {};
      state.eventPerformanceReport = {};
      state.eventAttendanceReport = {};
      state.eventFeedbackSummary = {};
      state.eventROIAnalysis = {};
    },
  },
  extraReducers: (builder) => {
    builder
      // Mentor Performance Report
      .addCase(fetchMentorPerformanceReport.pending, (state) => {
        state.mentorPerformanceLoading = true;
        state.mentorPerformanceError = null;
      })
      .addCase(fetchMentorPerformanceReport.fulfilled, (state, action) => {
        state.mentorPerformanceLoading = false;
        state.mentorPerformanceReport = action.payload;
      })
      .addCase(fetchMentorPerformanceReport.rejected, (state, action) => {
        state.mentorPerformanceLoading = false;
        state.mentorPerformanceError = action.payload;
      });
  },
});

// Export actions
export const { clearErrors, clearSuccessStates, resetReportsData } = instituteReportsSlice.actions;

// Export selectors
export const selectMentorPerformanceReport = (state) => state.instituteReports.mentorPerformanceReport;
export const selectMentorPerformanceLoading = (state) => state.instituteReports.mentorPerformanceLoading;
export const selectMentorPerformanceError = (state) => state.instituteReports.mentorPerformanceError;

export default instituteReportsSlice.reducer;
