// features/auth/login/LoginSlice.js
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../../utils/api/API_URL.js";

export const loginUser = createAsyncThunk(
  "login/loginUser",
  async ({ email, password }, thunkAPI) => {
    try {
      const response = await axios.post(`${URL}/api/users/signin`, {
        email,
        password,
      });

      return response.data; 
    } catch (error) {
      const err = error.response?.data?.detail || "Login failed";
      return thunkAPI.rejectWithValue(err);
    }
  }
);

const loginSlice = createSlice({
  name: "login",
  initialState: {
    user: null,        
    status: "idle",
    error: null,
  },
  reducers: {
    logout: (state) => {
      state.user = null;
      state.status = "idle";
      state.error = null;
      localStorage.removeItem("token");
      localStorage.removeItem("role");
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginUser.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.user = action.payload;

        // Save to localStorage
        localStorage.setItem("token", action.payload.access_token);
        localStorage.setItem("role", action.payload.role);
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      });
  },
});

export const { logout } = loginSlice.actions;
export default loginSlice.reducer;
