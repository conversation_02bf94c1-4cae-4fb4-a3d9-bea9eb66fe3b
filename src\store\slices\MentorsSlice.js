import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Base URL for mentors endpoints
const BASE_URL = import.meta.env.VITE_API_URL || 'https://edufair.duckdns.org';
const API_BASE = `${BASE_URL}/api/mentors`;

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Async Thunks for API calls

// 1. Register as Mentor
export const registerAsMentor = createAsyncThunk(
  'mentors/registerAsMentor',
  async (mentorData, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/register`, mentorData);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Get Public Mentors
export const fetchPublicMentors = createAsyncThunk(
  'mentors/fetchPublicMentors',
  async (queryParams = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      
      // Add query parameters
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(item => params.append(key, item));
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const res = await axios.get(`${API_BASE}/public?${params}`);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Get Mentor Details
export const fetchMentorDetails = createAsyncThunk(
  'mentors/fetchMentorDetails',
  async (mentorId, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/${mentorId}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 4. Update Mentor Profile (Mentors Only)
export const updateMentorProfile = createAsyncThunk(
  'mentors/updateProfile',
  async (profileData, { rejectWithValue }) => {
    try {
      const res = await axios.put(`${API_BASE}/profile`, profileData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 5. Apply to Institute (Mentors Only)
export const applyToInstitute = createAsyncThunk(
  'mentors/applyToInstitute',
  async (applicationData, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/apply-to-institute`, applicationData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 5a. Apply for Mentorship (Teachers Only)
export const applyForMentorship = createAsyncThunk(
  'mentors/applyForMentorship',
  async (applicationData, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/apply-for-mentorship`, applicationData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 6. Invite Mentor (Institutes Only)
export const inviteMentor = createAsyncThunk(
  'mentors/inviteMentor',
  async (invitationData, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/invite-mentor`, invitationData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 7. Respond to Association (Mentors/Institutes)
export const respondToAssociation = createAsyncThunk(
  'mentors/respondToAssociation',
  async ({ associationId, responseData }, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/associations/${associationId}/respond`, responseData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 8. Get Mentor Assignments (Mentors Only)
export const fetchMentorAssignments = createAsyncThunk(
  'mentors/fetchAssignments',
  async (queryParams = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const res = await axios.get(`${BASE_URL}/api/mentor/assignments?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 9. Respond to Assignment (Mentors Only)
export const respondToAssignment = createAsyncThunk(
  'mentors/respondToAssignment',
  async ({ assignmentId, responseData }, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${BASE_URL}/api/mentor/assignments/${assignmentId}/respond`, responseData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 10. Get Answers to Check (Mentors Only)
export const fetchAnswersToCheck = createAsyncThunk(
  'mentors/fetchAnswersToCheck',
  async (queryParams = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const res = await axios.get(`${BASE_URL}/api/mentor/answers-to-check?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 11. Submit Answer Score (Mentors Only)
export const submitAnswerScore = createAsyncThunk(
  'mentors/submitAnswerScore',
  async ({ answerId, mentor_score, mentor_feedback }, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({
        mentor_score: mentor_score.toString()
      });
      if (mentor_feedback) {
        params.append('mentor_feedback', mentor_feedback);
      }

      const res = await axios.post(`${BASE_URL}/api/mentor/answers/${answerId}/score?${params}`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 12. Bulk Score Answers (Mentors Only)
export const bulkScoreAnswers = createAsyncThunk(
  'mentors/bulkScoreAnswers',
  async (scoresData, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${BASE_URL}/api/mentor/answers/bulk-score`, scoresData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 13. Get Mentor Statistics (Mentors Only)
export const fetchMentorStatistics = createAsyncThunk(
  'mentors/fetchStatistics',
  async (queryParams = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const res = await axios.get(`${BASE_URL}/api/mentor/statistics?${params}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  // Public mentors
  publicMentors: [],
  publicMentorsLoading: false,
  publicMentorsError: null,
  publicMentorsPagination: {
    total: 0,
    page: 1,
    size: 20,
    total_pages: 1
  },

  // Mentor details
  currentMentor: null,
  mentorDetailsLoading: false,
  mentorDetailsError: null,

  // Mentor profile (for logged-in mentors)
  myProfile: null,
  profileLoading: false,
  profileError: null,
  profileUpdateLoading: false,
  profileUpdateError: null,
  profileUpdateSuccess: false,

  // Registration
  registrationLoading: false,
  registrationError: null,
  registrationSuccess: false,

  // Institute associations
  associations: [],
  associationsLoading: false,
  associationsError: null,
  applyLoading: false,
  applyError: null,
  applySuccess: false,
  inviteLoading: false,
  inviteError: null,
  inviteSuccess: false,

  // Teacher mentorship applications
  mentorshipApplications: [],
  mentorshipApplicationsLoading: false,
  mentorshipApplicationsError: null,
  mentorshipApplyLoading: false,
  mentorshipApplyError: null,
  mentorshipApplySuccess: false,

  // Mentor assignments
  assignments: [],
  assignmentsLoading: false,
  assignmentsError: null,
  assignmentResponseLoading: false,
  assignmentResponseError: null,
  assignmentResponseSuccess: false,

  // Answer checking
  answersToCheck: [],
  answersLoading: false,
  answersError: null,
  scoreSubmissionLoading: false,
  scoreSubmissionError: null,
  scoreSubmissionSuccess: false,
  bulkScoreLoading: false,
  bulkScoreError: null,
  bulkScoreSuccess: false,

  // Statistics
  statistics: null,
  statisticsLoading: false,
  statisticsError: null,

  // Search filters
  searchFilters: {
    search: '',
    expertise_areas: [],
    experience_years_min: null,
    experience_years_max: null,
    hourly_rate_min: null,
    hourly_rate_max: null,
    rating_min: null,
    country: '',
    verified_only: true,
    page: 1,
    size: 20
  },

  // UI state
  selectedMentor: null,
  showMentorDetails: false
};

// Mentors Slice
const mentorsSlice = createSlice({
  name: 'mentors',
  initialState,
  reducers: {
    // Update search filters
    updateSearchFilters: (state, action) => {
      state.searchFilters = { ...state.searchFilters, ...action.payload };
    },

    // Reset search filters
    resetSearchFilters: (state) => {
      state.searchFilters = initialState.searchFilters;
    },

    // Select mentor for details view
    selectMentor: (state, action) => {
      state.selectedMentor = action.payload;
      state.showMentorDetails = true;
    },

    // Close mentor details
    closeMentorDetails: (state) => {
      state.selectedMentor = null;
      state.showMentorDetails = false;
    },

    // Clear errors
    clearErrors: (state) => {
      state.publicMentorsError = null;
      state.mentorDetailsError = null;
      state.profileError = null;
      state.profileUpdateError = null;
      state.registrationError = null;
      state.associationsError = null;
      state.applyError = null;
      state.inviteError = null;
      state.mentorshipApplicationsError = null;
      state.mentorshipApplyError = null;
      state.assignmentsError = null;
      state.assignmentResponseError = null;
      state.answersError = null;
      state.scoreSubmissionError = null;
      state.bulkScoreError = null;
      state.statisticsError = null;
    },

    // Clear success states
    clearSuccessStates: (state) => {
      state.profileUpdateSuccess = false;
      state.registrationSuccess = false;
      state.applySuccess = false;
      state.inviteSuccess = false;
      state.assignmentResponseSuccess = false;
      state.scoreSubmissionSuccess = false;
      state.bulkScoreSuccess = false;
    },

    // Reset mentors state
    resetMentorsState: (state) => {
      return initialState;
    }
  },
  extraReducers: (builder) => {
    builder
      // Register as Mentor
      .addCase(registerAsMentor.pending, (state) => {
        state.registrationLoading = true;
        state.registrationError = null;
        state.registrationSuccess = false;
      })
      .addCase(registerAsMentor.fulfilled, (state, action) => {
        state.registrationLoading = false;
        state.registrationSuccess = true;
        state.myProfile = action.payload.mentor_profile;
      })
      .addCase(registerAsMentor.rejected, (state, action) => {
        state.registrationLoading = false;
        state.registrationError = action.payload;
      })

      // Fetch Public Mentors
      .addCase(fetchPublicMentors.pending, (state) => {
        state.publicMentorsLoading = true;
        state.publicMentorsError = null;
      })
      .addCase(fetchPublicMentors.fulfilled, (state, action) => {
        state.publicMentorsLoading = false;
        state.publicMentors = action.payload.mentors;
        state.publicMentorsPagination = {
          total: action.payload.total,
          page: action.payload.page,
          size: action.payload.size,
          total_pages: action.payload.total_pages
        };
      })
      .addCase(fetchPublicMentors.rejected, (state, action) => {
        state.publicMentorsLoading = false;
        state.publicMentorsError = action.payload;
      })

      // Fetch Mentor Details
      .addCase(fetchMentorDetails.pending, (state) => {
        state.mentorDetailsLoading = true;
        state.mentorDetailsError = null;
      })
      .addCase(fetchMentorDetails.fulfilled, (state, action) => {
        state.mentorDetailsLoading = false;
        state.currentMentor = action.payload;
      })
      .addCase(fetchMentorDetails.rejected, (state, action) => {
        state.mentorDetailsLoading = false;
        state.mentorDetailsError = action.payload;
      })

      // Update Mentor Profile
      .addCase(updateMentorProfile.pending, (state) => {
        state.profileUpdateLoading = true;
        state.profileUpdateError = null;
        state.profileUpdateSuccess = false;
      })
      .addCase(updateMentorProfile.fulfilled, (state, action) => {
        state.profileUpdateLoading = false;
        state.profileUpdateSuccess = true;
        state.myProfile = action.payload;
      })
      .addCase(updateMentorProfile.rejected, (state, action) => {
        state.profileUpdateLoading = false;
        state.profileUpdateError = action.payload;
      })

      // Apply to Institute
      .addCase(applyToInstitute.pending, (state) => {
        state.applyLoading = true;
        state.applyError = null;
        state.applySuccess = false;
      })
      .addCase(applyToInstitute.fulfilled, (state, action) => {
        state.applyLoading = false;
        state.applySuccess = true;
        // Add to associations if it exists
        if (state.associations) {
          state.associations.unshift(action.payload);
        }
      })
      .addCase(applyToInstitute.rejected, (state, action) => {
        state.applyLoading = false;
        state.applyError = action.payload;
      })

      // Invite Mentor
      .addCase(inviteMentor.pending, (state) => {
        state.inviteLoading = true;
        state.inviteError = null;
        state.inviteSuccess = false;
      })
      .addCase(inviteMentor.fulfilled, (state, action) => {
        state.inviteLoading = false;
        state.inviteSuccess = true;
      })
      .addCase(inviteMentor.rejected, (state, action) => {
        state.inviteLoading = false;
        state.inviteError = action.payload;
      })

      // Apply for Mentorship (Teachers)
      .addCase(applyForMentorship.pending, (state) => {
        state.mentorshipApplyLoading = true;
        state.mentorshipApplyError = null;
        state.mentorshipApplySuccess = false;
      })
      .addCase(applyForMentorship.fulfilled, (state, action) => {
        state.mentorshipApplyLoading = false;
        state.mentorshipApplySuccess = true;
        // Add the new application to the list
        state.mentorshipApplications.unshift(action.payload);
      })
      .addCase(applyForMentorship.rejected, (state, action) => {
        state.mentorshipApplyLoading = false;
        state.mentorshipApplyError = action.payload;
      })

      // Respond to Association
      .addCase(respondToAssociation.fulfilled, (state, action) => {
        // Update the association in the list
        const index = state.associations.findIndex(assoc => assoc.id === action.payload.id);
        if (index !== -1) {
          state.associations[index] = action.payload;
        }
      })

      // Fetch Mentor Assignments
      .addCase(fetchMentorAssignments.pending, (state) => {
        state.assignmentsLoading = true;
        state.assignmentsError = null;
      })
      .addCase(fetchMentorAssignments.fulfilled, (state, action) => {
        state.assignmentsLoading = false;
        state.assignments = action.payload.assignments;
      })
      .addCase(fetchMentorAssignments.rejected, (state, action) => {
        state.assignmentsLoading = false;
        state.assignmentsError = action.payload;
      })

      // Respond to Assignment
      .addCase(respondToAssignment.pending, (state) => {
        state.assignmentResponseLoading = true;
        state.assignmentResponseError = null;
        state.assignmentResponseSuccess = false;
      })
      .addCase(respondToAssignment.fulfilled, (state, action) => {
        state.assignmentResponseLoading = false;
        state.assignmentResponseSuccess = true;
        // Update the assignment in the list
        const index = state.assignments.findIndex(assignment => assignment.id === action.payload.id);
        if (index !== -1) {
          state.assignments[index] = action.payload;
        }
      })
      .addCase(respondToAssignment.rejected, (state, action) => {
        state.assignmentResponseLoading = false;
        state.assignmentResponseError = action.payload;
      })

      // Fetch Answers to Check
      .addCase(fetchAnswersToCheck.pending, (state) => {
        state.answersLoading = true;
        state.answersError = null;
      })
      .addCase(fetchAnswersToCheck.fulfilled, (state, action) => {
        state.answersLoading = false;
        state.answersToCheck = action.payload.answers;
      })
      .addCase(fetchAnswersToCheck.rejected, (state, action) => {
        state.answersLoading = false;
        state.answersError = action.payload;
      })

      // Submit Answer Score
      .addCase(submitAnswerScore.pending, (state) => {
        state.scoreSubmissionLoading = true;
        state.scoreSubmissionError = null;
        state.scoreSubmissionSuccess = false;
      })
      .addCase(submitAnswerScore.fulfilled, (state, action) => {
        state.scoreSubmissionLoading = false;
        state.scoreSubmissionSuccess = true;
        // Update the answer in the list
        const index = state.answersToCheck.findIndex(answer => answer.id === action.payload.id);
        if (index !== -1) {
          state.answersToCheck[index] = { ...state.answersToCheck[index], ...action.payload };
        }
      })
      .addCase(submitAnswerScore.rejected, (state, action) => {
        state.scoreSubmissionLoading = false;
        state.scoreSubmissionError = action.payload;
      })

      // Bulk Score Answers
      .addCase(bulkScoreAnswers.pending, (state) => {
        state.bulkScoreLoading = true;
        state.bulkScoreError = null;
        state.bulkScoreSuccess = false;
      })
      .addCase(bulkScoreAnswers.fulfilled, (state, action) => {
        state.bulkScoreLoading = false;
        state.bulkScoreSuccess = true;
        // Update answers based on results
        action.payload.results.forEach(result => {
          if (result.status === 'success') {
            const index = state.answersToCheck.findIndex(answer => answer.id === result.answer_id);
            if (index !== -1) {
              state.answersToCheck[index].final_score = result.final_score;
            }
          }
        });
      })
      .addCase(bulkScoreAnswers.rejected, (state, action) => {
        state.bulkScoreLoading = false;
        state.bulkScoreError = action.payload;
      })

      // Fetch Mentor Statistics
      .addCase(fetchMentorStatistics.pending, (state) => {
        state.statisticsLoading = true;
        state.statisticsError = null;
      })
      .addCase(fetchMentorStatistics.fulfilled, (state, action) => {
        state.statisticsLoading = false;
        state.statistics = action.payload;
      })
      .addCase(fetchMentorStatistics.rejected, (state, action) => {
        state.statisticsLoading = false;
        state.statisticsError = action.payload;
      });
  }
});

// Actions
export const {
  updateSearchFilters,
  resetSearchFilters,
  selectMentor,
  closeMentorDetails,
  clearErrors,
  clearSuccessStates,
  resetMentorsState
} = mentorsSlice.actions;

// Selectors
export const selectPublicMentors = (state) => state.mentors.publicMentors;
export const selectPublicMentorsLoading = (state) => state.mentors.publicMentorsLoading;
export const selectPublicMentorsError = (state) => state.mentors.publicMentorsError;
export const selectPublicMentorsPagination = (state) => state.mentors.publicMentorsPagination;

export const selectCurrentMentor = (state) => state.mentors.currentMentor;
export const selectMentorDetailsLoading = (state) => state.mentors.mentorDetailsLoading;
export const selectMentorDetailsError = (state) => state.mentors.mentorDetailsError;

export const selectMyProfile = (state) => state.mentors.myProfile;
export const selectProfileLoading = (state) => state.mentors.profileLoading;
export const selectProfileError = (state) => state.mentors.profileError;
export const selectProfileUpdateLoading = (state) => state.mentors.profileUpdateLoading;
export const selectProfileUpdateError = (state) => state.mentors.profileUpdateError;
export const selectProfileUpdateSuccess = (state) => state.mentors.profileUpdateSuccess;

export const selectRegistrationLoading = (state) => state.mentors.registrationLoading;
export const selectRegistrationError = (state) => state.mentors.registrationError;
export const selectRegistrationSuccess = (state) => state.mentors.registrationSuccess;

export const selectAssociations = (state) => state.mentors.associations;
export const selectAssociationsLoading = (state) => state.mentors.associationsLoading;
export const selectAssociationsError = (state) => state.mentors.associationsError;
export const selectApplyLoading = (state) => state.mentors.applyLoading;
export const selectApplyError = (state) => state.mentors.applyError;
export const selectApplySuccess = (state) => state.mentors.applySuccess;
export const selectInviteLoading = (state) => state.mentors.inviteLoading;
export const selectInviteError = (state) => state.mentors.inviteError;
export const selectInviteSuccess = (state) => state.mentors.inviteSuccess;

export const selectMentorshipApplications = (state) => state.mentors.mentorshipApplications;
export const selectMentorshipApplicationsLoading = (state) => state.mentors.mentorshipApplicationsLoading;
export const selectMentorshipApplicationsError = (state) => state.mentors.mentorshipApplicationsError;
export const selectMentorshipApplyLoading = (state) => state.mentors.mentorshipApplyLoading;
export const selectMentorshipApplyError = (state) => state.mentors.mentorshipApplyError;
export const selectMentorshipApplySuccess = (state) => state.mentors.mentorshipApplySuccess;

export const selectAssignments = (state) => state.mentors.assignments;
export const selectAssignmentsLoading = (state) => state.mentors.assignmentsLoading;
export const selectAssignmentsError = (state) => state.mentors.assignmentsError;
export const selectAssignmentResponseLoading = (state) => state.mentors.assignmentResponseLoading;
export const selectAssignmentResponseError = (state) => state.mentors.assignmentResponseError;
export const selectAssignmentResponseSuccess = (state) => state.mentors.assignmentResponseSuccess;

export const selectAnswersToCheck = (state) => state.mentors.answersToCheck;
export const selectAnswersLoading = (state) => state.mentors.answersLoading;
export const selectAnswersError = (state) => state.mentors.answersError;

export const selectScoreSubmissionLoading = (state) => state.mentors.scoreSubmissionLoading;
export const selectScoreSubmissionError = (state) => state.mentors.scoreSubmissionError;
export const selectScoreSubmissionSuccess = (state) => state.mentors.scoreSubmissionSuccess;

export const selectBulkScoreLoading = (state) => state.mentors.bulkScoreLoading;
export const selectBulkScoreError = (state) => state.mentors.bulkScoreError;
export const selectBulkScoreSuccess = (state) => state.mentors.bulkScoreSuccess;

export const selectStatistics = (state) => state.mentors.statistics;
export const selectStatisticsLoading = (state) => state.mentors.statisticsLoading;
export const selectStatisticsError = (state) => state.mentors.statisticsError;

export const selectSearchFilters = (state) => state.mentors.searchFilters;
export const selectSelectedMentor = (state) => state.mentors.selectedMentor;
export const selectShowMentorDetails = (state) => state.mentors.showMentorDetails;

// Helper selectors
export const selectMentorsByExpertise = (expertiseArea) => (state) => {
  const mentors = state.mentors.publicMentors;
  return mentors.filter(mentor =>
    mentor.expertise_areas?.includes(expertiseArea)
  );
};

export const selectVerifiedMentors = (state) => {
  const mentors = state.mentors.publicMentors;
  return mentors.filter(mentor => mentor.is_verified);
};

export const selectTopRatedMentors = (state) => {
  const mentors = state.mentors.publicMentors;
  return mentors.filter(mentor => mentor.rating >= 4.5).sort((a, b) => b.rating - a.rating);
};

export default mentorsSlice.reducer;
