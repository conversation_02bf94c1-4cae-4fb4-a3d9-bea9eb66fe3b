import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../../utils/api/API_URL";

const BASE_URL = `${URL}/api/student/dashboard`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks for student dashboard APIs

// Fetch complete dashboard data
export const fetchStudentDashboard = createAsyncThunk(
  "studentDashboard/fetchDashboard",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch dashboard summary
export const fetchStudentDashboardSummary = createAsyncThunk(
  "studentDashboard/fetchSummary",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/summary`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch quick actions
export const fetchStudentQuickActions = createAsyncThunk(
  "studentDashboard/fetchQuickActions",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/quick-actions`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch performance data
export const fetchStudentPerformance = createAsyncThunk(
  "studentDashboard/fetchPerformance",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/performance`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch schedule
export const fetchStudentSchedule = createAsyncThunk(
  "studentDashboard/fetchSchedule",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/schedule`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

const studentDashboardSlice = createSlice({
  name: "studentDashboard",
  initialState: {
    // Complete dashboard data
    dashboardData: null,
    
    // Summary data
    summary: {
      total_classes: 0,
      pending_assignments: 0,
      upcoming_exams: 0,
      overall_grade: 0.0,
      unread_notifications: 0,
      total_points: 0,
      current_level: 1,
      quick_actions_count: 0,
      last_updated: null
    },
    
    // Quick actions
    quickActions: [],
    
    // Performance data
    performance: {
      overall_grade: 0.0,
      subject_grades: {},
      recent_scores: [],
      improvement_trend: "stable",
      rank_in_class: null
    },
    
    // Study metrics
    studyMetrics: {
      total_points: 0,
      level: 1,
      tasks_completed: 0,
      exams_taken: 0,
      average_grade: 0.0,
      badges_earned: []
    },
    
    // Schedule
    schedule: [],
    
    // Student info
    student: null,
    
    // Classes
    classes: [],
    
    // Exams
    exams: [],
    
    // Assignments
    assignments: [],
    
    // Recent activity
    recentActivity: [],
    
    // Loading states
    loading: {
      dashboard: false,
      summary: false,
      quickActions: false,
      performance: false,
      schedule: false
    },
    
    // Error states
    error: {
      dashboard: null,
      summary: null,
      quickActions: null,
      performance: null,
      schedule: null
    },
    
    // Last updated timestamps
    lastUpdated: {
      dashboard: null,
      summary: null,
      quickActions: null,
      performance: null,
      schedule: null
    }
  },
  reducers: {
    clearErrors: (state) => {
      state.error = {
        dashboard: null,
        summary: null,
        quickActions: null,
        performance: null,
        schedule: null
      };
    },
    clearDashboardData: (state) => {
      state.dashboardData = null;
      state.student = null;
      state.classes = [];
      state.exams = [];
      state.assignments = [];
      state.recentActivity = [];
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch complete dashboard
      .addCase(fetchStudentDashboard.pending, (state) => {
        state.loading.dashboard = true;
        state.error.dashboard = null;
      })
      .addCase(fetchStudentDashboard.fulfilled, (state, action) => {
        state.loading.dashboard = false;
        state.dashboardData = action.payload.data;
        
        // Extract individual data sections
        if (action.payload.data) {
          state.student = action.payload.data.student;
          state.classes = action.payload.data.classes || [];
          state.exams = action.payload.data.exams || [];
          state.assignments = action.payload.data.assignments || [];
          state.recentActivity = action.payload.data.recent_activity || [];
          state.schedule = action.payload.data.schedule || [];
          
          if (action.payload.data.performance) {
            state.performance = action.payload.data.performance;
          }
          
          if (action.payload.data.study_metrics) {
            state.studyMetrics = action.payload.data.study_metrics;
          }
          
          if (action.payload.data.quick_actions) {
            state.quickActions = action.payload.data.quick_actions;
          }
        }
        
        state.lastUpdated.dashboard = action.payload.data?.last_updated || new Date().toISOString();
      })
      .addCase(fetchStudentDashboard.rejected, (state, action) => {
        state.loading.dashboard = false;
        state.error.dashboard = action.payload;
      })

      // Fetch summary
      .addCase(fetchStudentDashboardSummary.pending, (state) => {
        state.loading.summary = true;
        state.error.summary = null;
      })
      .addCase(fetchStudentDashboardSummary.fulfilled, (state, action) => {
        state.loading.summary = false;
        state.summary = action.payload.data;
        state.lastUpdated.summary = action.payload.data?.last_updated || new Date().toISOString();
      })
      .addCase(fetchStudentDashboardSummary.rejected, (state, action) => {
        state.loading.summary = false;
        state.error.summary = action.payload;
      })

      // Fetch quick actions
      .addCase(fetchStudentQuickActions.pending, (state) => {
        state.loading.quickActions = true;
        state.error.quickActions = null;
      })
      .addCase(fetchStudentQuickActions.fulfilled, (state, action) => {
        state.loading.quickActions = false;
        state.quickActions = action.payload.data.quick_actions || [];
        state.lastUpdated.quickActions = action.payload.data?.last_updated || new Date().toISOString();
      })
      .addCase(fetchStudentQuickActions.rejected, (state, action) => {
        state.loading.quickActions = false;
        state.error.quickActions = action.payload;
      })

      // Fetch performance
      .addCase(fetchStudentPerformance.pending, (state) => {
        state.loading.performance = true;
        state.error.performance = null;
      })
      .addCase(fetchStudentPerformance.fulfilled, (state, action) => {
        state.loading.performance = false;
        if (action.payload.data.performance) {
          state.performance = action.payload.data.performance;
        }
        if (action.payload.data.study_metrics) {
          state.studyMetrics = action.payload.data.study_metrics;
        }
        state.lastUpdated.performance = action.payload.data?.last_updated || new Date().toISOString();
      })
      .addCase(fetchStudentPerformance.rejected, (state, action) => {
        state.loading.performance = false;
        state.error.performance = action.payload;
      })

      // Fetch schedule
      .addCase(fetchStudentSchedule.pending, (state) => {
        state.loading.schedule = true;
        state.error.schedule = null;
      })
      .addCase(fetchStudentSchedule.fulfilled, (state, action) => {
        state.loading.schedule = false;
        state.schedule = action.payload.data.schedule || [];
        state.lastUpdated.schedule = action.payload.data?.last_updated || new Date().toISOString();
      })
      .addCase(fetchStudentSchedule.rejected, (state, action) => {
        state.loading.schedule = false;
        state.error.schedule = action.payload;
      });
  },
});

export const { clearErrors, clearDashboardData } = studentDashboardSlice.actions;
export default studentDashboardSlice.reducer;
