import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

import URL from "../../utils/api/API_URL";

const BASE_URL = `${URL}/api/subjects`;

// Get token from localStorage
const getAuthToken = () => localStorage.getItem("token");

// Thunks

// Fetch all subjects with pagination
export const fetchSubjects = createAsyncThunk(
  "subjects/fetchSubjects",
  async ({ skip = 0, limit = 100 } = {}, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/?skip=${skip}&limit=${limit}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data; // { subjects: [...], total: N }
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const createSubject = createAsyncThunk(
  "subjects/createSubject",
  async (subjectData, thunkAPI) => {
    const URLS = [
      `${BASE_URL}/`,
      `${URL}/api/subject/`,
      `${BASE_URL}/create/`,
    ];
    let lastError;
    for (const url of URLS) {
      try {
        const res = await axios.post(url, subjectData, {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`,
            "Content-Type": "application/json",
          },
        });
        return res.data;
      } catch (err) {
        lastError = err;
        // If 405, try next URL
        if (err.response && err.response.status !== 405) {
          break;
        }
      }
    }
    return thunkAPI.rejectWithValue(
      lastError?.response?.data || lastError?.message || "All endpoints failed for subject creation."
    );
  }
);

export const fetchSubjectById = createAsyncThunk(
  "subjects/fetchSubjectById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update subject
export const updateSubject = createAsyncThunk(
  "subjects/updateSubject",
  async ({ id, subjectData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${id}`, subjectData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch subject hierarchy
export const fetchSubjectHierarchy = createAsyncThunk(
  "subjects/fetchSubjectHierarchy",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}/hierarchy`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Delete subject (handle new response shape)
export const deleteSubject = createAsyncThunk(
  "subjects/deleteSubject",
  async (id, thunkAPI) => {
    try {
      const res = await axios.delete(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return { id, detail: res.data.detail };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  subjects: [],
  total: 0,
  currentSubject: null,
  hierarchy: null,
  loading: false,
  error: null,
  deleteDetail: null,
};

// Slice
const subjectSlice = createSlice({
  name: "subjects",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch All
      .addCase(fetchSubjects.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubjects.fulfilled, (state, action) => {
        state.loading = false;
        state.subjects = action.payload.subjects;
        state.total = action.payload.total;
      })
      .addCase(fetchSubjects.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create
      .addCase(createSubject.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSubject.fulfilled, (state, action) => {
        state.loading = false;
        state.subjects.push(action.payload);
      })
      .addCase(createSubject.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch by ID
      .addCase(fetchSubjectById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubjectById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSubject = action.payload;
      })
      .addCase(fetchSubjectById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update
      .addCase(updateSubject.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSubject.fulfilled, (state, action) => {
        state.loading = false;
        // Update the subject in the array
        const idx = state.subjects.findIndex(s => s.id === action.payload.id);
        if (idx !== -1) {
          state.subjects[idx] = action.payload;
        }
        // If currentSubject is the updated one, update it too
        if (state.currentSubject && state.currentSubject.id === action.payload.id) {
          state.currentSubject = action.payload;
        }
      })
      .addCase(updateSubject.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch Hierarchy
      .addCase(fetchSubjectHierarchy.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubjectHierarchy.fulfilled, (state, action) => {
        state.loading = false;
        state.hierarchy = action.payload;
      })
      .addCase(fetchSubjectHierarchy.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete
      .addCase(deleteSubject.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.deleteDetail = null;
      })
      .addCase(deleteSubject.fulfilled, (state, action) => {
        state.loading = false;
        state.subjects = state.subjects.filter(
          (subj) => subj.id !== action.payload.id
        );
        state.deleteDetail = action.payload.detail;
      })
      .addCase(deleteSubject.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default subjectSlice.reducer;
