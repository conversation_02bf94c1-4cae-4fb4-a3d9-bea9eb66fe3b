import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Base URL for subscription endpoints
const BASE_URL = import.meta.env.VITE_API_URL || 'https://edufair.duckdns.org';
const API_BASE = `${BASE_URL}/api/subscriptions`;

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token');
};

// Async Thunks for API calls

// 1. Get Available Plans
export const fetchSubscriptionPlans = createAsyncThunk(
  'subscription/fetchPlans',
  async ({ user_type, is_active = true, skip = 0, limit = 100 } = {}, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString(),
        is_active: is_active.toString()
      });

      if (user_type) {
        params.append('user_type', user_type);
      }

      const res = await axios.get(`${API_BASE}/plans?${params}`);
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Get My Subscription
export const fetchMySubscription = createAsyncThunk(
  'subscription/fetchMySubscription',
  async (_, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/my-subscription`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      if (err.response?.status === 404) {
        return null; // No subscription found
      }
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Upgrade Subscription
export const upgradeSubscription = createAsyncThunk(
  'subscription/upgrade',
  async ({ plan_id }, { rejectWithValue }) => {
    try {
      const res = await axios.post(`${API_BASE}/upgrade`, 
        { plan_id },
        { headers: { Authorization: `Bearer ${getAuthToken()}` } }
      );
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 4. Get My Usage
export const fetchMyUsage = createAsyncThunk(
  'subscription/fetchMyUsage',
  async (_, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/usage/my-usage`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      if (err.response?.status === 404) {
        return null; // No subscription found
      }
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 5. Update Usage Metrics
export const updateUsageMetric = createAsyncThunk(
  'subscription/updateUsage',
  async ({ metric_name, increment = 1 }, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams({
        metric_name,
        increment: increment.toString()
      });

      const res = await axios.post(`${API_BASE}/usage/update?${params}`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 6. Get Subscription Analytics (Admin Only)
export const fetchSubscriptionAnalytics = createAsyncThunk(
  'subscription/fetchAnalytics',
  async (_, { rejectWithValue }) => {
    try {
      const res = await axios.get(`${API_BASE}/analytics/statistics`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  // Available plans
  plans: [],
  plansLoading: false,
  plansError: null,

  // Current user subscription
  mySubscription: null,
  subscriptionLoading: false,
  subscriptionError: null,

  // Usage data
  usage: null,
  usageLoading: false,
  usageError: null,

  // Analytics (admin)
  analytics: null,
  analyticsLoading: false,
  analyticsError: null,

  // UI state
  upgradeLoading: false,
  upgradeError: null,
  upgradeSuccess: false,
};

// Subscription Slice
const subscriptionSlice = createSlice({
  name: 'subscription',
  initialState,
  reducers: {
    clearErrors: (state) => {
      state.plansError = null;
      state.subscriptionError = null;
      state.usageError = null;
      state.analyticsError = null;
      state.upgradeError = null;
    },
    clearUpgradeStatus: (state) => {
      state.upgradeSuccess = false;
      state.upgradeError = null;
    },
    resetSubscriptionState: (state) => {
      return initialState;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch Plans
      .addCase(fetchSubscriptionPlans.pending, (state) => {
        state.plansLoading = true;
        state.plansError = null;
      })
      .addCase(fetchSubscriptionPlans.fulfilled, (state, action) => {
        state.plansLoading = false;
        state.plans = action.payload;
      })
      .addCase(fetchSubscriptionPlans.rejected, (state, action) => {
        state.plansLoading = false;
        state.plansError = action.payload;
      })

      // Fetch My Subscription
      .addCase(fetchMySubscription.pending, (state) => {
        state.subscriptionLoading = true;
        state.subscriptionError = null;
      })
      .addCase(fetchMySubscription.fulfilled, (state, action) => {
        state.subscriptionLoading = false;
        state.mySubscription = action.payload;
      })
      .addCase(fetchMySubscription.rejected, (state, action) => {
        state.subscriptionLoading = false;
        state.subscriptionError = action.payload;
      })

      // Upgrade Subscription
      .addCase(upgradeSubscription.pending, (state) => {
        state.upgradeLoading = true;
        state.upgradeError = null;
        state.upgradeSuccess = false;
      })
      .addCase(upgradeSubscription.fulfilled, (state, action) => {
        state.upgradeLoading = false;
        state.upgradeSuccess = true;
        state.mySubscription = action.payload.subscription;
      })
      .addCase(upgradeSubscription.rejected, (state, action) => {
        state.upgradeLoading = false;
        state.upgradeError = action.payload;
      })

      // Fetch My Usage
      .addCase(fetchMyUsage.pending, (state) => {
        state.usageLoading = true;
        state.usageError = null;
      })
      .addCase(fetchMyUsage.fulfilled, (state, action) => {
        state.usageLoading = false;
        state.usage = action.payload;
      })
      .addCase(fetchMyUsage.rejected, (state, action) => {
        state.usageLoading = false;
        state.usageError = action.payload;
      })

      // Update Usage Metric
      .addCase(updateUsageMetric.fulfilled, (state) => {
        // Optionally refresh usage data after update
        // Could dispatch fetchMyUsage here if needed
      })

      // Fetch Analytics
      .addCase(fetchSubscriptionAnalytics.pending, (state) => {
        state.analyticsLoading = true;
        state.analyticsError = null;
      })
      .addCase(fetchSubscriptionAnalytics.fulfilled, (state, action) => {
        state.analyticsLoading = false;
        state.analytics = action.payload;
      })
      .addCase(fetchSubscriptionAnalytics.rejected, (state, action) => {
        state.analyticsLoading = false;
        state.analyticsError = action.payload;
      });
  }
});

// Actions
export const { clearErrors, clearUpgradeStatus, resetSubscriptionState } = subscriptionSlice.actions;

// Selectors
export const selectPlans = (state) => state.subscription.plans;
export const selectPlansLoading = (state) => state.subscription.plansLoading;
export const selectPlansError = (state) => state.subscription.plansError;

export const selectMySubscription = (state) => state.subscription.mySubscription;
export const selectSubscriptionLoading = (state) => state.subscription.subscriptionLoading;
export const selectSubscriptionError = (state) => state.subscription.subscriptionError;

export const selectUsage = (state) => state.subscription.usage;
export const selectUsageLoading = (state) => state.subscription.usageLoading;
export const selectUsageError = (state) => state.subscription.usageError;

export const selectAnalytics = (state) => state.subscription.analytics;
export const selectAnalyticsLoading = (state) => state.subscription.analyticsLoading;
export const selectAnalyticsError = (state) => state.subscription.analyticsError;

export const selectUpgradeLoading = (state) => state.subscription.upgradeLoading;
export const selectUpgradeError = (state) => state.subscription.upgradeError;
export const selectUpgradeSuccess = (state) => state.subscription.upgradeSuccess;

// Helper selectors
export const selectHasActiveSubscription = (state) => {
  const subscription = state.subscription.mySubscription;
  return subscription && subscription.status === 'active';
};

export const selectCanUseFeature = (feature) => (state) => {
  const subscription = state.subscription.mySubscription;
  if (!subscription || subscription.status !== 'active') return false;
  return subscription.plan?.features?.[feature] || false;
};

export const selectIsOverLimit = (limit) => (state) => {
  const usage = state.subscription.usage;
  if (!usage) return false;
  return usage.is_over_limit || false;
};

export default subscriptionSlice.reducer;
