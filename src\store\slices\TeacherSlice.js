import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../../utils/api/API_URL";

const BASE_URL = `${URL}/api`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks

// Fetch teacher profile by /api/teachers/{id}
export const fetchTeacherById = createAsyncThunk(
  "teacher/fetchTeacherById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/teachers/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update teacher profile
export const updateTeacherById = createAsyncThunk(
  "teacher/updateTeacherById",
  async (data, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/teachers/`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Delete teacher profile by /api/{id}
export const deleteTeacherById = createAsyncThunk(
  "teacher/deleteTeacherById",
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/teachers/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch teacher profile by /api/user/{id}
export const fetchTeacherByUserId = createAsyncThunk(
  "teacher/fetchTeacherByUserId",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/teachers/teacherProfile/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update teacher profile by /api/user/{id}
export const updateTeacherByUserId = createAsyncThunk(
  "teacher/updateTeacherByUserId",
  async ({ id, data }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/user/${id}`, data, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Delete teacher profile by /api/user/{id}
export const deleteTeacherByUserId = createAsyncThunk(
  "teacher/deleteTeacherByUserId",
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/user/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Create teacher profile
export const createTeacherProfile = createAsyncThunk(
  "teacher/createTeacherProfile",
  async (teacherData, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/teachers/`, teacherData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);


// Initial State
const initialState = {
  teacher: null,
  loading: false,
  error: null,
};

const teacherSlice = createSlice({
  name: "teacher",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch by /api/{id}
      .addCase(fetchTeacherById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTeacherById.fulfilled, (state, action) => {
        state.loading = false;
        state.teacher = action.payload;
      })
      .addCase(fetchTeacherById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update by /api/{id}
      .addCase(updateTeacherById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateTeacherById.fulfilled, (state, action) => {
        state.loading = false;
        state.teacher = action.payload;
      })
      .addCase(updateTeacherById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete by /api/{id}
      .addCase(deleteTeacherById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteTeacherById.fulfilled, (state) => {
        state.loading = false;
        state.teacher = null;
      })
      .addCase(deleteTeacherById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch by /api/user/{id}
      .addCase(fetchTeacherByUserId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTeacherByUserId.fulfilled, (state, action) => {
        state.loading = false;
        state.teacher = action.payload;
      })
      .addCase(fetchTeacherByUserId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update by /api/user/{id}
      .addCase(updateTeacherByUserId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateTeacherByUserId.fulfilled, (state, action) => {
        state.loading = false;
        state.teacher = action.payload;
      })
      .addCase(updateTeacherByUserId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete by /api/user/{id}
      .addCase(deleteTeacherByUserId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteTeacherByUserId.fulfilled, (state) => {
        state.loading = false;
        state.teacher = null;
      })
      .addCase(deleteTeacherByUserId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create teacher profile
      .addCase(createTeacherProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createTeacherProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.teacher = action.payload;
      })
      .addCase(createTeacherProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default teacherSlice.reducer;
