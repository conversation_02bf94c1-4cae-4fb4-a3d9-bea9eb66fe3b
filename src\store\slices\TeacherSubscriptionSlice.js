import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../../utils/api/API_URL";

const BASE_URL = `${URL}/api`;
const getAuthToken = () => localStorage.getItem("token");

// === Thunks ===

// 1. Create Subscription
export const createTeacherSubscription = createAsyncThunk(
  "teacherSubscription/create",
  async (payload, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/`, payload, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Get All Subscriptions (with optional filters)
export const fetchAllTeacherSubscriptions = createAsyncThunk(
  "teacherSubscription/fetchAll",
  async (params = {}, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
        params,
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Get Subscription By Id
export const fetchTeacherSubscriptionById = createAsyncThunk(
  "teacherSubscription/fetchById",
  async (subscription_id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${subscription_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 4. Update Subscription By Id
export const updateTeacherSubscription = createAsyncThunk(
  "teacherSubscription/update",
  async ({ subscription_id, data }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${subscription_id}`, data, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 5. Delete Subscription By Id
export const deleteTeacherSubscription = createAsyncThunk(
  "teacherSubscription/delete",
  async (subscription_id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/${subscription_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return subscription_id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 6. Get Subscription By Profile Id
export const fetchTeacherSubscriptionByProfileId = createAsyncThunk(
  "teacherSubscription/fetchByProfileId",
  async (profile_id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/profile/${profile_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 7. Update Subscription By Profile Id
export const updateTeacherSubscriptionByProfileId = createAsyncThunk(
  "teacherSubscription/updateByProfileId",
  async ({ profile_id, data }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/profile/${profile_id}`, data, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 8. Delete Subscription By Profile Id
export const deleteTeacherSubscriptionByProfileId = createAsyncThunk(
  "teacherSubscription/deleteByProfileId",
  async (profile_id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/profile/${profile_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return profile_id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 9. Get Active Subscriptions
export const fetchActiveTeacherSubscriptions = createAsyncThunk(
  "teacherSubscription/fetchActive",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/active/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 10. Get Expired Subscriptions
export const fetchExpiredTeacherSubscriptions = createAsyncThunk(
  "teacherSubscription/fetchExpired",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/expired/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 11. Cancel Subscription
export const cancelTeacherSubscription = createAsyncThunk(
  "teacherSubscription/cancel",
  async (subscription_id, thunkAPI) => {
    try {
      const res = await axios.patch(`${BASE_URL}/${subscription_id}/cancel`, null, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 12. Renew Subscription
export const renewTeacherSubscription = createAsyncThunk(
  "teacherSubscription/renew",
  async ({ subscription_id, new_end_date }, thunkAPI) => {
    try {
      const res = await axios.patch(
        `${BASE_URL}/${subscription_id}/renew`,
        null,
        {
          headers: { Authorization: `Bearer ${getAuthToken()}` },
          params: { new_end_date },
        }
      );
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 13. Get Subscription By User Id
export const fetchTeacherProfileByUserId = createAsyncThunk(
  "teacherSubscription/fetchProfileByUserId",
  async (user_id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/user/${user_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 14. Update Profile By User Id
export const updateTeacherProfileByUserId = createAsyncThunk(
  "teacherSubscription/updateProfileByUserId",
  async ({ user_id, data }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/user/${user_id}`, data, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 15. Delete Profile By User Id
export const deleteTeacherProfileByUserId = createAsyncThunk(
  "teacherSubscription/deleteProfileByUserId",
  async (user_id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/user/${user_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return user_id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// === Initial State ===
const initialState = {
  subscriptions: [],
  subscription: null,
  loading: false,
  error: null,
};

// === Slice ===
const teacherSubscriptionSlice = createSlice({
  name: "teacherSubscription",
  initialState,
  reducers: {
    clearTeacherSubscriptionState: (state) => {
      state.loading = false;
      state.error = null;
      state.subscription = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createTeacherSubscription.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(createTeacherSubscription.fulfilled, (state, action) => { state.loading = false; state.subscriptions.push(action.payload); })
      .addCase(createTeacherSubscription.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get All
      .addCase(fetchAllTeacherSubscriptions.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchAllTeacherSubscriptions.fulfilled, (state, action) => { state.loading = false; state.subscriptions = action.payload; })
      .addCase(fetchAllTeacherSubscriptions.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get By Id
      .addCase(fetchTeacherSubscriptionById.pending, (state) => { state.loading = true; state.error = null; state.subscription = null; })
      .addCase(fetchTeacherSubscriptionById.fulfilled, (state, action) => { state.loading = false; state.subscription = action.payload; })
      .addCase(fetchTeacherSubscriptionById.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Update By Id
      .addCase(updateTeacherSubscription.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(updateTeacherSubscription.fulfilled, (state, action) => { state.loading = false; state.subscription = action.payload; })
      .addCase(updateTeacherSubscription.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Delete By Id
      .addCase(deleteTeacherSubscription.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(deleteTeacherSubscription.fulfilled, (state, action) => { state.loading = false; state.subscriptions = state.subscriptions.filter(s => s.id !== action.payload); })
      .addCase(deleteTeacherSubscription.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get By Profile Id
      .addCase(fetchTeacherSubscriptionByProfileId.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchTeacherSubscriptionByProfileId.fulfilled, (state, action) => { state.loading = false; state.subscription = action.payload; })
      .addCase(fetchTeacherSubscriptionByProfileId.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Update By Profile Id
      .addCase(updateTeacherSubscriptionByProfileId.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(updateTeacherSubscriptionByProfileId.fulfilled, (state, action) => { state.loading = false; state.subscription = action.payload; })
      .addCase(updateTeacherSubscriptionByProfileId.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Delete By Profile Id
      .addCase(deleteTeacherSubscriptionByProfileId.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(deleteTeacherSubscriptionByProfileId.fulfilled, (state, action) => { state.loading = false; state.subscriptions = state.subscriptions.filter(s => s.profile_id !== action.payload); })
      .addCase(deleteTeacherSubscriptionByProfileId.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get Active
      .addCase(fetchActiveTeacherSubscriptions.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchActiveTeacherSubscriptions.fulfilled, (state, action) => { state.loading = false; state.subscriptions = action.payload; })
      .addCase(fetchActiveTeacherSubscriptions.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get Expired
      .addCase(fetchExpiredTeacherSubscriptions.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchExpiredTeacherSubscriptions.fulfilled, (state, action) => { state.loading = false; state.subscriptions = action.payload; })
      .addCase(fetchExpiredTeacherSubscriptions.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Cancel
      .addCase(cancelTeacherSubscription.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(cancelTeacherSubscription.fulfilled, (state, action) => { state.loading = false; state.subscription = action.payload; })
      .addCase(cancelTeacherSubscription.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Renew
      .addCase(renewTeacherSubscription.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(renewTeacherSubscription.fulfilled, (state, action) => { state.loading = false; state.subscription = action.payload; })
      .addCase(renewTeacherSubscription.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get Profile By User Id
      .addCase(fetchTeacherProfileByUserId.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchTeacherProfileByUserId.fulfilled, (state, action) => { state.loading = false; state.subscription = action.payload; })
      .addCase(fetchTeacherProfileByUserId.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Update Profile By User Id
      .addCase(updateTeacherProfileByUserId.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(updateTeacherProfileByUserId.fulfilled, (state, action) => { state.loading = false; state.subscription = action.payload; })
      .addCase(updateTeacherProfileByUserId.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Delete Profile By User Id
      .addCase(deleteTeacherProfileByUserId.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(deleteTeacherProfileByUserId.fulfilled, (state, action) => { state.loading = false; state.subscriptions = state.subscriptions.filter(s => s.user_id !== action.payload); })
      .addCase(deleteTeacherProfileByUserId.rejected, (state, action) => { state.loading = false; state.error = action.payload; });
  },
});

export const { clearTeacherSubscriptionState } = teacherSubscriptionSlice.actions;
export default teacherSubscriptionSlice.reducer;
