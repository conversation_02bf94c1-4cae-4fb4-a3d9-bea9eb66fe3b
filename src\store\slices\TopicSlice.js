import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../../utils/api/API_URL";

const BASE_URL = `${URL}/api/topics`;
const getAuthToken = () => localStorage.getItem("token");

// Thunks

// Fetch all topics with pagination
export const fetchTopics = createAsyncThunk(
  "topics/fetchTopics",
  async ({ skip = 0, limit = 100 } = {}, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/?skip=${skip}&limit=${limit}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data; // { topics: [...], total: N }
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Create topic
export const createTopic = createAsyncThunk(
  "topics/createTopic",
  async (topicData, thunkAPI) => {
    try {
      // Ensure required fields are present
      const requiredData = {
        name: topicData.name,
        chapter_id: topicData.chapter_id,
        description: topicData.description || "", // Provide default empty string if missing
      };
      
      console.log('Creating topic with data:', requiredData);
      
      const res = await axios.post(`${BASE_URL}/`, requiredData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      console.error('Error creating topic:', err.response?.data);
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch topic by ID
export const fetchTopicById = createAsyncThunk(
  "topics/fetchTopicById",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Update topic
export const updateTopic = createAsyncThunk(
  "topics/updateTopic",
  async ({ id, topicData }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${id}`, topicData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json",
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Delete topic
export const deleteTopic = createAsyncThunk(
  "topics/deleteTopic",
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch topics by chapter with pagination
export const fetchTopicsByChapter = createAsyncThunk(
  "topics/fetchTopicsByChapter",
  async ({ chapterId, skip = 0, limit = 100 }, thunkAPI) => {
    try {
      const res = await axios.get(
        `${BASE_URL}/chapters/${chapterId}/topics?skip=${skip}&limit=${limit}`,
        {
          headers: { Authorization: `Bearer ${getAuthToken()}` },
        }
      );
      return res.data; // { topics: [...], total: N }
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch subtopics for a topic
export const fetchTopicSubtopics = createAsyncThunk(
  "topics/fetchTopicSubtopics",
  async (id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${id}/subtopics`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data; // topic object with subtopics
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  topics: [],
  total: 0,
  currentTopic: null,
  subtopics: [],
  topicsByChapter: [],
  topicsByChapterTotal: 0,
  loading: false,
  error: null,
};

// Slice
const topicSlice = createSlice({
  name: "topics",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch All
      .addCase(fetchTopics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTopics.fulfilled, (state, action) => {
        state.loading = false;
        state.topics = action.payload.topics;
        state.total = action.payload.total;
      })
      .addCase(fetchTopics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create
      .addCase(createTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createTopic.fulfilled, (state, action) => {
        state.loading = false;
        state.topics.push(action.payload);
      })
      .addCase(createTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch by ID
      .addCase(fetchTopicById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTopicById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentTopic = action.payload;
      })
      .addCase(fetchTopicById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update
      .addCase(updateTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateTopic.fulfilled, (state, action) => {
        state.loading = false;
        const idx = state.topics.findIndex(t => t.id === action.payload.id);
        if (idx !== -1) {
          state.topics[idx] = action.payload;
        }
        if (state.currentTopic && state.currentTopic.id === action.payload.id) {
          state.currentTopic = action.payload;
        }
      })
      .addCase(updateTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete
      .addCase(deleteTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteTopic.fulfilled, (state, action) => {
        state.loading = false;
        state.topics = state.topics.filter(
          (topic) => topic.id !== action.payload
        );
      })
      .addCase(deleteTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch topics by chapter
      .addCase(fetchTopicsByChapter.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTopicsByChapter.fulfilled, (state, action) => {
        state.loading = false;
        // Support both array and object API responses
        if (Array.isArray(action.payload)) {
          state.topicsByChapter = action.payload;
          state.topicsByChapterTotal = action.payload.length;
        } else {
          state.topicsByChapter = action.payload.topics;
          state.topicsByChapterTotal = action.payload.total;
        }
      })
      .addCase(fetchTopicsByChapter.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Fetch subtopics for a topic
      .addCase(fetchTopicSubtopics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTopicSubtopics.fulfilled, (state, action) => {
        state.loading = false;
        state.subtopics = action.payload.subtopics || [];
        // Optionally update currentTopic if needed
        state.currentTopic = action.payload;
      })
      .addCase(fetchTopicSubtopics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default topicSlice.reducer;
