/**
 * Admin Session Management Redux Slice
 * Manages admin-side exam session monitoring and control
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import URL from '../../../utils/api/API_URL';

const API_BASE = `${URL}/api/exams/session`;

// Async thunks for admin session operations

// Get session details
export const getSessionDetails = createAsyncThunk(
  'adminSession/getSessionDetails',
  async ({ sessionId }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const response = await fetch(`${API_BASE}/admin/exam-session/${sessionId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.detail || 'Failed to fetch session details');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// List all active sessions
export const listActiveSessions = createAsyncThunk(
  'adminSession/listActiveSessions',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const response = await fetch(`${API_BASE}/admin/exam-sessions`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.detail || 'Failed to fetch active sessions');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Terminate session
export const terminateSession = createAsyncThunk(
  'adminSession/terminateSession',
  async ({ sessionId, reason }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const response = await fetch(`${API_BASE}/admin/exam-session/${sessionId}/terminate?reason=${encodeURIComponent(reason)}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.detail || 'Failed to terminate session');
      }

      const data = await response.json();
      return { sessionId, reason, ...data };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  // Session data
  activeSessions: [],
  sessionDetails: {},
  terminatedSessions: [],
  
  // Loading states
  loading: false,
  loadingDetails: false,
  terminating: false,
  
  // Error handling
  error: null,
  
  // UI state
  selectedSession: null,
  showTerminateModal: false,
  showDetailsModal: false,
  
  // Filters and sorting
  filters: {
    status: 'all', // all, active, submitted, expired, disqualified
    examId: null,
    studentId: null
  },
  sortBy: 'start_time',
  sortOrder: 'desc'
};

const adminSessionSlice = createSlice({
  name: 'adminSession',
  initialState,
  reducers: {
    // UI management
    setSelectedSession: (state, action) => {
      state.selectedSession = action.payload;
    },
    
    setShowTerminateModal: (state, action) => {
      state.showTerminateModal = action.payload;
    },
    
    setShowDetailsModal: (state, action) => {
      state.showDetailsModal = action.payload;
    },
    
    // Filters and sorting
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    setSortBy: (state, action) => {
      state.sortBy = action.payload;
    },
    
    setSortOrder: (state, action) => {
      state.sortOrder = action.payload;
    },
    
    // Clear error
    clearError: (state) => {
      state.error = null;
    },
    
    // Update session status in real-time
    updateSessionStatus: (state, action) => {
      const { sessionId, status, lastHeartbeat } = action.payload;
      const sessionIndex = state.activeSessions.findIndex(
        session => session.session_id === sessionId
      );
      
      if (sessionIndex !== -1) {
        state.activeSessions[sessionIndex].status = status;
        if (lastHeartbeat) {
          state.activeSessions[sessionIndex].last_heartbeat = lastHeartbeat;
        }
      }
    },
    
    // Remove session from active list
    removeFromActive: (state, action) => {
      const sessionId = action.payload;
      state.activeSessions = state.activeSessions.filter(
        session => session.session_id !== sessionId
      );
    },
    
    // Add to terminated sessions
    addToTerminated: (state, action) => {
      state.terminatedSessions.push(action.payload);
    }
  },
  
  extraReducers: (builder) => {
    // Get session details
    builder
      .addCase(getSessionDetails.pending, (state) => {
        state.loadingDetails = true;
        state.error = null;
      })
      .addCase(getSessionDetails.fulfilled, (state, action) => {
        state.loadingDetails = false;
        state.sessionDetails[action.meta.arg.sessionId] = action.payload;
      })
      .addCase(getSessionDetails.rejected, (state, action) => {
        state.loadingDetails = false;
        state.error = action.payload;
      });

    // List active sessions
    builder
      .addCase(listActiveSessions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(listActiveSessions.fulfilled, (state, action) => {
        state.loading = false;
        state.activeSessions = action.payload;
      })
      .addCase(listActiveSessions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Terminate session
    builder
      .addCase(terminateSession.pending, (state) => {
        state.terminating = true;
        state.error = null;
      })
      .addCase(terminateSession.fulfilled, (state, action) => {
        state.terminating = false;
        
        const { sessionId, reason } = action.payload;
        
        // Remove from active sessions
        const sessionIndex = state.activeSessions.findIndex(
          session => session.session_id === sessionId
        );
        
        if (sessionIndex !== -1) {
          const terminatedSession = {
            ...state.activeSessions[sessionIndex],
            terminated_at: new Date().toISOString(),
            termination_reason: reason,
            status: 'terminated'
          };
          
          state.terminatedSessions.push(terminatedSession);
          state.activeSessions.splice(sessionIndex, 1);
        }
        
        // Clear UI state
        state.selectedSession = null;
        state.showTerminateModal = false;
      })
      .addCase(terminateSession.rejected, (state, action) => {
        state.terminating = false;
        state.error = action.payload;
      });
  }
});

export const {
  setSelectedSession,
  setShowTerminateModal,
  setShowDetailsModal,
  setFilters,
  setSortBy,
  setSortOrder,
  clearError,
  updateSessionStatus,
  removeFromActive,
  addToTerminated
} = adminSessionSlice.actions;

// Selectors
export const selectActiveSessions = (state) => state.adminSession.activeSessions;
export const selectSessionDetails = (state) => state.adminSession.sessionDetails;
export const selectTerminatedSessions = (state) => state.adminSession.terminatedSessions;
export const selectSelectedSession = (state) => state.adminSession.selectedSession;
export const selectIsLoading = (state) => state.adminSession.loading;
export const selectIsLoadingDetails = (state) => state.adminSession.loadingDetails;
export const selectIsTerminating = (state) => state.adminSession.terminating;
export const selectError = (state) => state.adminSession.error;
export const selectFilters = (state) => state.adminSession.filters;
export const selectSortBy = (state) => state.adminSession.sortBy;
export const selectSortOrder = (state) => state.adminSession.sortOrder;

// Filtered and sorted sessions selector
export const selectFilteredSessions = (state) => {
  const { activeSessions, filters, sortBy, sortOrder } = state.adminSession;
  
  let filtered = activeSessions;
  
  // Apply filters
  if (filters.status !== 'all') {
    filtered = filtered.filter(session => session.status === filters.status);
  }
  
  if (filters.examId) {
    filtered = filtered.filter(session => session.exam_id === filters.examId);
  }
  
  if (filters.studentId) {
    filtered = filtered.filter(session => session.student_id === filters.studentId);
  }
  
  // Apply sorting
  filtered.sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];
    
    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
  
  return filtered;
};

export default adminSessionSlice.reducer;
