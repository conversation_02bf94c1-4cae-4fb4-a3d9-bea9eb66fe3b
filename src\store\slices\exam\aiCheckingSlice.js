import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import AICheckingService from '../../../services/exam/api/AICheckingService';

// Async thunks for AI checking operations

// Check exam with AI (simplified API)
export const checkExamWithAI = createAsyncThunk(
  'aiChecking/checkExam',
  async ({ examId, studentId }, { rejectWithValue }) => {
    try {
      const result = await AICheckingService.checkExamWithAI(examId, studentId);

      // Handle disqualified students
      if (result.is_disqualified) {
        console.warn('⚠️ Student is disqualified - AI checking prevented');
        return { examId, studentId, result, isDisqualified: true };
      }

      return { examId, studentId, result };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Get AI results (simplified API)
export const getAIResults = createAsyncThunk(
  'aiChecking/getResults',
  async ({ examId, studentId = null }, { rejectWithValue }) => {
    try {
      const result = studentId
        ? await AICheckingService.getAIResults(examId, studentId)
        : await AICheckingService.getStudentAIResults(examId);

      // Handle disqualified students
      if (result.is_disqualified) {
        console.warn('⚠️ Student is disqualified - returning disqualification info');
        return { examId, studentId, result, isDisqualified: true };
      }

      return { examId, studentId, result };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Wait for AI results (with polling) - simplified API
export const waitForAIResults = createAsyncThunk(
  'aiChecking/waitForResults',
  async ({ examId, studentId = null, maxWaitTime = 90000, pollInterval = 3000 }, { rejectWithValue }) => {
    try {
      const result = await AICheckingService.waitForAIResults(examId, studentId, maxWaitTime, pollInterval);
      return { examId, studentId, result };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Get all student AI results (this endpoint doesn't exist in the new API)
// Keeping for backward compatibility but it may not be used
export const getAllStudentAIResults = createAsyncThunk(
  'aiChecking/getAllResults',
  async (_, { rejectWithValue }) => {
    try {
      // Note: This endpoint doesn't exist in the new simplified API
      // You would need to call getStudentAIResults for each exam individually
      throw new Error('getAllStudentAIResults is not available in the simplified API');
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Test connection (simplified API)
export const testGeminiConnection = createAsyncThunk(
  'aiChecking/testConnection',
  async (_, { rejectWithValue }) => {
    try {
      const result = await AICheckingService.testConnection();
      return result;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Initial state
const initialState = {
  // Current checking operation
  currentExamId: null,
  currentStudentId: null,
  checkingStatus: 'idle', // idle, checking, waiting, completed, error

  // Results storage
  results: {}, // examId -> result mapping

  // All student results
  allResults: [],

  // Loading states
  loading: false,
  waiting: false,

  // Error handling
  error: null,
  lastError: null,

  // Connection test
  connectionTest: null,

  // Progress tracking
  progress: {
    step: null, // 'triggering', 'waiting', 'completed'
    message: null,
    percentage: 0
  }
};

// AI Checking slice
const aiCheckingSlice = createSlice({
  name: 'aiChecking',
  initialState,
  reducers: {
    // Reset checking state
    resetChecking: (state) => {
      state.currentExamId = null;
      state.currentStudentId = null;
      state.checkingStatus = 'idle';
      state.loading = false;
      state.waiting = false;
      state.error = null;
      state.progress = {
        step: null,
        message: null,
        percentage: 0
      };
    },
    
    // Clear error
    clearError: (state) => {
      state.error = null;
    },
    
    // Update progress
    updateProgress: (state, action) => {
      state.progress = { ...state.progress, ...action.payload };
    },
    
    // Cache result
    cacheResult: (state, action) => {
      const { examId, result } = action.payload;
      state.results[examId] = result;
    }
  },
  extraReducers: (builder) => {
    // Check exam with AI
    builder
      .addCase(checkExamWithAI.pending, (state, action) => {
        state.loading = true;
        state.checkingStatus = 'checking';
        state.error = null;
        state.currentExamId = action.meta.arg.examId;
        state.currentStudentId = action.meta.arg.studentId;
        state.progress = {
          step: 'triggering',
          message: 'Starting AI evaluation...',
          percentage: 10
        };
      })
      .addCase(checkExamWithAI.fulfilled, (state) => {
        state.loading = false;
        state.checkingStatus = 'waiting';
        state.progress = {
          step: 'waiting',
          message: 'AI is evaluating your exam...',
          percentage: 30
        };
      })
      .addCase(checkExamWithAI.rejected, (state, action) => {
        state.loading = false;
        state.checkingStatus = 'error';
        state.error = action.payload;
        state.lastError = action.payload;
        state.progress = {
          step: 'error',
          message: 'Failed to start AI evaluation',
          percentage: 0
        };
      });

    // Wait for AI results
    builder
      .addCase(waitForAIResults.pending, (state) => {
        state.waiting = true;
        state.checkingStatus = 'waiting';
        state.error = null;
        state.progress = {
          step: 'waiting',
          message: 'Waiting for AI evaluation to complete...',
          percentage: 50
        };
      })
      .addCase(waitForAIResults.fulfilled, (state, action) => {
        state.waiting = false;
        state.checkingStatus = 'completed';
        const { examId, result } = action.payload;
        state.results[examId] = result;
        state.progress = {
          step: 'completed',
          message: 'AI evaluation completed!',
          percentage: 100
        };
      })
      .addCase(waitForAIResults.rejected, (state, action) => {
        state.waiting = false;
        state.checkingStatus = 'error';
        state.error = action.payload;
        state.lastError = action.payload;
        state.progress = {
          step: 'error',
          message: 'AI evaluation timed out or failed',
          percentage: 0
        };
      });

    // Get AI results
    builder
      .addCase(getAIResults.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAIResults.fulfilled, (state, action) => {
        state.loading = false;
        const { examId, result } = action.payload;
        state.results[examId] = result;
      })
      .addCase(getAIResults.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.lastError = action.payload;
      });

    // Get all student AI results
    builder
      .addCase(getAllStudentAIResults.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllStudentAIResults.fulfilled, (state, action) => {
        state.loading = false;
        state.allResults = action.payload.results || [];
      })
      .addCase(getAllStudentAIResults.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.lastError = action.payload;
      });

    // Test Gemini connection
    builder
      .addCase(testGeminiConnection.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(testGeminiConnection.fulfilled, (state, action) => {
        state.loading = false;
        state.connectionTest = action.payload;
      })
      .addCase(testGeminiConnection.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.lastError = action.payload;
        state.connectionTest = null;
      });
  }
});

// Export actions
export const {
  resetChecking,
  clearError,
  updateProgress,
  cacheResult
} = aiCheckingSlice.actions;

// Selectors
export const selectAIChecking = (state) => state.aiChecking;
export const selectAIResults = (examId) => (state) => state.aiChecking.results[examId];
export const selectAllAIResults = (state) => state.aiChecking.allResults;
export const selectCheckingStatus = (state) => state.aiChecking.checkingStatus;
export const selectProgress = (state) => state.aiChecking.progress;
export const selectCurrentExamId = (state) => state.aiChecking.currentExamId;
export const selectCurrentStudentId = (state) => state.aiChecking.currentStudentId;

export default aiCheckingSlice.reducer;
