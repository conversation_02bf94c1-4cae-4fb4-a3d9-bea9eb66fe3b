/**
 * Teacher Reconnection Redux Slice
 * Manages teacher-side reconnection request handling
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import URL from '../../../utils/api/API_URL';

const API_BASE = `${URL}/api/exams/session`;

// Async thunks for teacher reconnection operations

// Get pending reconnection requests
export const getPendingReconnectionRequests = createAsyncThunk(
  'teacherReconnection/getPendingRequests',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const response = await fetch(`${API_BASE}/teacher/reconnection-requests`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.detail || 'Failed to fetch reconnection requests');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Approve or deny reconnection request
export const approveReconnectionRequest = createAsyncThunk(
  'teacherReconnection/approveRequest',
  async ({ requestId, approved, reason }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      const response = await fetch(`${API_BASE}/teacher/reconnection-requests/${requestId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({
          request_id: requestId,
          approved,
          reason
        })
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.detail || 'Failed to process reconnection request');
      }

      const data = await response.json();
      return { requestId, approved, reason, ...data };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  // Reconnection requests
  pendingRequests: [],
  processedRequests: [],
  
  // Loading states
  loading: false,
  processing: false,
  
  // Error handling
  error: null,
  
  // UI state
  selectedRequest: null,
  showApprovalModal: false
};

const teacherReconnectionSlice = createSlice({
  name: 'teacherReconnection',
  initialState,
  reducers: {
    // UI management
    setSelectedRequest: (state, action) => {
      state.selectedRequest = action.payload;
    },
    
    setShowApprovalModal: (state, action) => {
      state.showApprovalModal = action.payload;
    },
    
    // Clear error
    clearError: (state) => {
      state.error = null;
    },
    
    // Remove processed request from pending
    removeFromPending: (state, action) => {
      const requestId = action.payload;
      state.pendingRequests = state.pendingRequests.filter(
        request => request.request_id !== requestId
      );
    },
    
    // Add to processed requests
    addToProcessed: (state, action) => {
      state.processedRequests.push(action.payload);
    }
  },
  
  extraReducers: (builder) => {
    // Get pending reconnection requests
    builder
      .addCase(getPendingReconnectionRequests.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getPendingReconnectionRequests.fulfilled, (state, action) => {
        state.loading = false;
        state.pendingRequests = action.payload;
      })
      .addCase(getPendingReconnectionRequests.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Approve/deny reconnection request
    builder
      .addCase(approveReconnectionRequest.pending, (state) => {
        state.processing = true;
        state.error = null;
      })
      .addCase(approveReconnectionRequest.fulfilled, (state, action) => {
        state.processing = false;
        
        const { requestId, approved, reason } = action.payload;
        
        // Remove from pending requests
        state.pendingRequests = state.pendingRequests.filter(
          request => request.request_id !== requestId
        );
        
        // Add to processed requests
        const processedRequest = {
          request_id: requestId,
          approved,
          reason,
          processed_at: new Date().toISOString(),
          status: approved ? 'approved' : 'denied'
        };
        state.processedRequests.push(processedRequest);
        
        // Clear UI state
        state.selectedRequest = null;
        state.showApprovalModal = false;
      })
      .addCase(approveReconnectionRequest.rejected, (state, action) => {
        state.processing = false;
        state.error = action.payload;
      });
  }
});

export const {
  setSelectedRequest,
  setShowApprovalModal,
  clearError,
  removeFromPending,
  addToProcessed
} = teacherReconnectionSlice.actions;

// Selectors
export const selectPendingRequests = (state) => state.teacherReconnection.pendingRequests;
export const selectProcessedRequests = (state) => state.teacherReconnection.processedRequests;
export const selectSelectedRequest = (state) => state.teacherReconnection.selectedRequest;
export const selectIsLoading = (state) => state.teacherReconnection.loading;
export const selectIsProcessing = (state) => state.teacherReconnection.processing;
export const selectError = (state) => state.teacherReconnection.error;
export const selectShowApprovalModal = (state) => state.teacherReconnection.showApprovalModal;

export default teacherReconnectionSlice.reducer;
