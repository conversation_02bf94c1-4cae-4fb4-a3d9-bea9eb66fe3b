import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

import API_BASE_URL from "../../utils/api/API_URL";

const getAuthToken = () => localStorage.getItem("token");

// Thunk: Get all users
export const fetchAllUsers = createAsyncThunk("users/fetchAll", async (_, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch users");
  }
});

// Thunk: Create new user
export const createUser = createAsyncThunk("users/createUser", async (userData, thunkAPI) => {
  try {
    const res = await axios.post(`${API_BASE_URL}/api/users/`, userData, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
        "Content-Type": "application/json",
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to create user");
  }
});

// Thunk: Get single user by ID
export const fetchSingleUser = createAsyncThunk("users/fetchSingle", async (userId, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/${userId}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch user");
  }
});

// ✅ Thunk: Get current authenticated user (me)
export const fetchCurrentUser = createAsyncThunk("users/fetchMe", async (_, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/me`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch current user");
  }
});

// Thunk: Update user profile
export const updateUserProfile = createAsyncThunk("users/updateProfile", async (profileData, thunkAPI) => {
  try {
    const res = await axios.put(`${API_BASE_URL}/api/users/me`, profileData, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
        "Content-Type": "application/json",
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to update profile");
  }
});

// Thunk: Upload profile picture using the enhanced API with image objects
export const uploadProfilePicture = createAsyncThunk("users/uploadProfilePicture", async (file, thunkAPI) => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const res = await axios.post(`${API_BASE_URL}/api/files/profile-picture`, formData, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
        // Don't set Content-Type for FormData - browser sets it automatically
      },
    });

    // After successful upload, fetch updated user data to get image objects
    const userRes = await axios.get(`${API_BASE_URL}/api/users/me`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });

    return {
      message: res.data.message || "Profile picture uploaded successfully",
      ...userRes.data // This includes profile_picture_data with base64 image objects
    };
  } catch (err) {
    // Handle backend error gracefully - file might still be uploaded
    if (err.response?.status === 500) {
      // Backend error but file was likely uploaded - fetch updated user data
      try {
        const userRes = await axios.get(`${API_BASE_URL}/api/users/me`, {
          headers: { Authorization: `Bearer ${getAuthToken()}` },
        });

        return {
          message: "Profile picture uploaded successfully (with backend warning)",
          ...userRes.data
        };
      } catch (fetchErr) {
        return thunkAPI.rejectWithValue("Upload may have succeeded but couldn't verify. Please refresh the page.");
      }
    }

    return thunkAPI.rejectWithValue(err.response?.data || err.message || "Failed to upload profile picture");
  }
});

// Thunk: Delete profile picture using the documented API endpoint
export const deleteProfilePicture = createAsyncThunk("users/deleteProfilePicture", async (_, thunkAPI) => {
  try {
    const res = await axios.delete(`${API_BASE_URL}/api/files/profile-picture`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });

    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || err.message || "Failed to delete profile picture");
  }
});

// Thunk: Generate SAS URL
export const generateSasUrl = createAsyncThunk("users/generateSasUrl", async (blob_name, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/generate_sas_url/${blob_name}`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to generate SAS URL");
  }
});

// Thunk: Get all Students
export const fetchAllStudents = createAsyncThunk("users/fetchAllStudents", async (_, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/students/all`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch students");
  }
});

// Thunk: Get all Teachers
export const fetchAllTeachers = createAsyncThunk("users/fetchAllTeachers", async (_, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/teachers/all`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch teachers");
  }
});

// Thunk: Get all Sponsors
export const fetchAllSponsors = createAsyncThunk("users/fetchAllSponsors", async (_, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/sponsors/all`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch sponsors");
  }
});

// Thunk: Get all Institutes
export const fetchAllInstitutes = createAsyncThunk("users/fetchAllInstitutes", async (_, thunkAPI) => {
  try {
    const res = await axios.get(`${API_BASE_URL}/api/users/institutes/all`, {
      headers: {
        Authorization: `Bearer ${getAuthToken()}`,
      },
    });
    return res.data;
  } catch (err) {
    return thunkAPI.rejectWithValue(err.response?.data || "Failed to fetch institutes");
  }
});

const userSlice = createSlice({
  name: "users",
  initialState: {
    allUsers: [],
    allStudents: [],
    allTeachers: [],
    allSponsors: [],
    allInstitutes: [],
    selectedUser: null,
    currentUser: null,
    sasUrl: null,
    loading: false,
    error: null,
    success: null,
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSuccess: (state) => {
      state.success = null;
    },
    clearSasUrl: (state) => {
      state.sasUrl = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // --- Fetch All Users ---
      .addCase(fetchAllUsers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.allUsers = action.payload;
      })
      .addCase(fetchAllUsers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Create User ---
      .addCase(createUser.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(createUser.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "User created successfully";
        state.allUsers.push(action.payload);
      })
      .addCase(createUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Fetch Single User ---
      .addCase(fetchSingleUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSingleUser.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedUser = action.payload;
      })
      .addCase(fetchSingleUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Fetch Current User (me) ---
      .addCase(fetchCurrentUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCurrentUser.fulfilled, (state, action) => {
        state.loading = false;
        state.currentUser = action.payload;
      })
      .addCase(fetchCurrentUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Update User Profile ---
      .addCase(updateUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.success = "Profile updated successfully";
        state.currentUser = action.payload;
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Upload Profile Picture ---
      .addCase(uploadProfilePicture.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(uploadProfilePicture.fulfilled, (state, action) => {
        state.loading = false;
        state.success = action.payload.message || "Profile picture uploaded successfully";
        // Update current user with new profile picture data
        if (state.currentUser) {
          state.currentUser = {
            ...state.currentUser,
            ...action.payload // This will include all the profile picture fields returned by the API
          };
        }
      })
      .addCase(uploadProfilePicture.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Delete Profile Picture ---
      .addCase(deleteProfilePicture.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = null;
      })
      .addCase(deleteProfilePicture.fulfilled, (state, action) => {
        state.loading = false;
        state.success = action.payload.message || "Profile picture deleted successfully";
        // Remove profile picture from current user
        if (state.currentUser) {
          state.currentUser = {
            ...state.currentUser,
            ...action.payload // This will include the updated user data without profile picture
          };
        }
      })
      .addCase(deleteProfilePicture.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Generate SAS URL ---
      .addCase(generateSasUrl.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(generateSasUrl.fulfilled, (state, action) => {
        state.loading = false;
        state.sasUrl = action.payload;
      })
      .addCase(generateSasUrl.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Fetch All Students ---
      .addCase(fetchAllStudents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllStudents.fulfilled, (state, action) => {
        state.loading = false;
        state.allStudents = action.payload;
      })
      .addCase(fetchAllStudents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Fetch All Teachers ---
      .addCase(fetchAllTeachers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllTeachers.fulfilled, (state, action) => {
        state.loading = false;
        state.allTeachers = action.payload;
      })
      .addCase(fetchAllTeachers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Fetch All Sponsors ---
      .addCase(fetchAllSponsors.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllSponsors.fulfilled, (state, action) => {
        state.loading = false;
        state.allSponsors = action.payload;
      })
      .addCase(fetchAllSponsors.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // --- Fetch All Institutes ---
      .addCase(fetchAllInstitutes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllInstitutes.fulfilled, (state, action) => {
        state.loading = false;
        state.allInstitutes = action.payload;
      })
      .addCase(fetchAllInstitutes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

// Selector functions
export const selectAllUsers = (state) => state.users.allUsers;
export const selectAllStudents = (state) => state.users.allStudents;
export const selectAllTeachers = (state) => state.users.allTeachers;
export const selectAllSponsors = (state) => state.users.allSponsors;
export const selectAllInstitutes = (state) => state.users.allInstitutes;
export const selectSelectedUser = (state) => state.users.selectedUser;
export const selectCurrentUser = (state) => state.users.currentUser;
export const selectSasUrl = (state) => state.users.sasUrl;
export const selectUsersLoading = (state) => state.users.loading;
export const selectUsersError = (state) => state.users.error;
export const selectUsersSuccess = (state) => state.users.success;

export const { clearError, clearSuccess, clearSasUrl } = userSlice.actions;
export default userSlice.reducer;
