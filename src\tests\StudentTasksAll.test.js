import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import component to test
import StudentTasksAll from '../pages/student/StudentTasksAll';

// Import slice
import taskSlice from '../store/slices/TaskSlice';
import userSlice from '../store/slices/userSlice';

// Mock theme provider
jest.mock('../providers/ThemeContext', () => ({
  useThemeProvider: () => ({
    currentTheme: 'light'
  })
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Sample task data matching the API response structure
const sampleTasks = [
  {
    id: "0523eabc-1b19-4b63-b3f6-617c9b740d0a",
    name: "Task test",
    description: "This is a test task description",
    status: "pending",
    deadline: "2025-08-02T04:00:00",
    accept_after_deadline: true,
    created_at: "2025-08-01T10:48:57.352278Z",
    updated_at: "2025-08-01T10:48:57.352284Z",
    subject: {
      name: "Physics",
      id: "fb91addf-d8f6-4478-9dbc-798654606f8f"
    },
    chapters: [],
    topics: [],
    subtopics: []
  },
  {
    id: "test-task-2",
    name: "Mathematics Assignment",
    description: "Solve calculus problems",
    status: "submitted",
    deadline: "2025-08-05T10:00:00",
    accept_after_deadline: false,
    created_at: "2025-08-01T08:00:00.000000Z",
    updated_at: "2025-08-01T08:00:00.000000Z",
    subject: {
      name: "Mathematics",
      id: "math-subject-id"
    },
    chapters: [{ name: "Calculus" }],
    topics: [{ name: "Derivatives" }],
    subtopics: []
  }
];

// Create test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      tasks: taskSlice,
      users: userSlice,
    },
    preloadedState: {
      tasks: {
        tasks: sampleTasks,
        loading: false,
        error: null,
        currentTask: null,
        submissions: [],
        ...initialState.tasks
      },
      users: {
        currentUser: { id: 'test-user-id', name: 'Test User' },
        ...initialState.users
      }
    }
  });
};

// Test wrapper component
const TestWrapper = ({ children, store }) => (
  <Provider store={store}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </Provider>
);

describe('StudentTasksAll Component', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  test('renders tasks all page with header and stats', async () => {
    const store = createTestStore();
    
    render(
      <TestWrapper store={store}>
        <StudentTasksAll />
      </TestWrapper>
    );

    // Check header
    expect(screen.getByText('All Tasks')).toBeInTheDocument();
    expect(screen.getByText('Complete overview of all your assigned tasks')).toBeInTheDocument();

    // Check stats cards
    expect(screen.getByText('Total')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByText('Submitted')).toBeInTheDocument();
    expect(screen.getByText('Overdue')).toBeInTheDocument();

    // Check task count
    expect(screen.getByText('2')).toBeInTheDocument(); // Total tasks
  });

  test('displays tasks in grid view by default', async () => {
    const store = createTestStore();
    
    render(
      <TestWrapper store={store}>
        <StudentTasksAll />
      </TestWrapper>
    );

    // Check if tasks are displayed
    expect(screen.getByText('Task test')).toBeInTheDocument();
    expect(screen.getByText('Mathematics Assignment')).toBeInTheDocument();
    expect(screen.getByText('Physics')).toBeInTheDocument();
    expect(screen.getByText('Mathematics')).toBeInTheDocument();
  });

  test('filters tasks by search term', async () => {
    const store = createTestStore();
    
    render(
      <TestWrapper store={store}>
        <StudentTasksAll />
      </TestWrapper>
    );

    // Find search input and type
    const searchInput = screen.getByPlaceholderText('Search tasks...');
    fireEvent.change(searchInput, { target: { value: 'Mathematics' } });

    // Should show only Mathematics task
    expect(screen.getByText('Mathematics Assignment')).toBeInTheDocument();
    expect(screen.queryByText('Task test')).not.toBeInTheDocument();
  });

  test('filters tasks by status', async () => {
    const store = createTestStore();
    
    render(
      <TestWrapper store={store}>
        <StudentTasksAll />
      </TestWrapper>
    );

    // Find status filter and select 'submitted'
    const statusFilter = screen.getByDisplayValue('All Status');
    fireEvent.change(statusFilter, { target: { value: 'submitted' } });

    // Should show only submitted task
    expect(screen.getByText('Mathematics Assignment')).toBeInTheDocument();
    expect(screen.queryByText('Task test')).not.toBeInTheDocument();
  });

  test('switches between grid and list view', async () => {
    const store = createTestStore();
    
    render(
      <TestWrapper store={store}>
        <StudentTasksAll />
      </TestWrapper>
    );

    // Find view mode buttons
    const listViewButton = screen.getByRole('button', { name: /list view/i });
    
    // Switch to list view
    fireEvent.click(listViewButton);

    // Tasks should still be visible but in list format
    expect(screen.getByText('Task test')).toBeInTheDocument();
    expect(screen.getByText('Mathematics Assignment')).toBeInTheDocument();
  });

  test('navigates back to tasks page when back button is clicked', async () => {
    const store = createTestStore();
    
    render(
      <TestWrapper store={store}>
        <StudentTasksAll />
      </TestWrapper>
    );

    // Find and click back button
    const backButton = screen.getByRole('button', { name: /back/i });
    fireEvent.click(backButton);

    // Should navigate to tasks page
    expect(mockNavigate).toHaveBeenCalledWith('/student/tasks');
  });

  test('navigates to task detail when task is clicked', async () => {
    const store = createTestStore();
    
    render(
      <TestWrapper store={store}>
        <StudentTasksAll />
      </TestWrapper>
    );

    // Find and click a task
    const taskCard = screen.getByText('Task test').closest('div[role="button"], div[onClick]') || 
                     screen.getByText('Task test').closest('div');
    
    if (taskCard) {
      fireEvent.click(taskCard);
      expect(mockNavigate).toHaveBeenCalledWith('/student/task/0523eabc-1b19-4b63-b3f6-617c9b740d0a');
    }
  });

  test('shows loading state', async () => {
    const store = createTestStore({
      tasks: { tasks: [], loading: true, error: null }
    });
    
    render(
      <TestWrapper store={store}>
        <StudentTasksAll />
      </TestWrapper>
    );

    expect(screen.getByText('Loading your tasks...')).toBeInTheDocument();
  });

  test('shows error state', async () => {
    const store = createTestStore({
      tasks: { tasks: [], loading: false, error: 'Failed to load tasks' }
    });
    
    render(
      <TestWrapper store={store}>
        <StudentTasksAll />
      </TestWrapper>
    );

    expect(screen.getByText('Failed to load tasks')).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  test('shows empty state when no tasks match filters', async () => {
    const store = createTestStore();
    
    render(
      <TestWrapper store={store}>
        <StudentTasksAll />
      </TestWrapper>
    );

    // Search for something that doesn't exist
    const searchInput = screen.getByPlaceholderText('Search tasks...');
    fireEvent.change(searchInput, { target: { value: 'NonexistentTask' } });

    expect(screen.getByText('No tasks found')).toBeInTheDocument();
    expect(screen.getByText('Try adjusting your filters to see more tasks.')).toBeInTheDocument();
  });
});
