import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import components to test
import TeacherTasks from '../pages/teacher/TeacherTasks';
import StudentTasks from '../pages/student/StudentTasks';
import TaskSubmission from '../components/task/TaskSubmission';
import TaskGrading from '../components/task/TaskGrading';
import TaskAttachments from '../components/task/TaskAttachments';

// Import slice
import taskSlice from '../store/slices/TaskSlice';
import userSlice from '../store/slices/userSlice';

// Mock providers
import { ThemeProvider } from '../providers/ThemeContext';

// Mock data
const mockTasks = [
  {
    id: '1',
    name: 'Math Assignment 1',
    description: 'Complete exercises 1-10',
    subject: 'Mathematics',
    deadline: '2024-12-31T23:59:59Z',
    status: 'active',
    submission_status: 'not_submitted',
    teacher_name: '<PERSON>',
    created_at: '2024-01-01T00:00:00Z',
    assigned_students: 25,
    pending_submissions: 5
  },
  {
    id: '2',
    name: 'Science Project',
    description: 'Research and present on renewable energy',
    subject: 'Science',
    deadline: '2024-11-30T23:59:59Z',
    status: 'active',
    submission_status: 'submitted',
    teacher_name: 'Jane Smith',
    created_at: '2024-01-15T00:00:00Z',
    assigned_students: 20,
    pending_submissions: 0,
    grade: 85,
    max_grade: 100
  }
];

const mockSubmissions = [
  {
    id: '1',
    task_id: '1',
    student_id: 'student1',
    student_name: 'Alice Johnson',
    submission_text: 'Here is my completed assignment...',
    status: 'submitted',
    submitted_at: '2024-01-20T10:00:00Z',
    grade: null,
    feedback: null
  },
  {
    id: '2',
    task_id: '1',
    student_id: 'student2',
    student_name: 'Bob Wilson',
    submission_text: 'My solution to the problems...',
    status: 'graded',
    submitted_at: '2024-01-19T15:30:00Z',
    grade: 92,
    max_grade: 100,
    feedback: 'Excellent work! Well done.',
    graded_at: '2024-01-21T09:00:00Z'
  }
];

const mockUser = {
  id: 'user1',
  name: 'Test User',
  role: 'student'
};

// Create test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      tasks: taskSlice.reducer,
      user: userSlice.reducer,
    },
    preloadedState: {
      tasks: {
        tasks: mockTasks,
        submissions: mockSubmissions,
        loading: false,
        error: null,
        task: null,
        currentSubmission: null,
        submissionLoading: false,
        submissionError: null,
        grading: {
          loading: false,
          error: null,
          success: false
        },
        attachments: [],
        attachmentLoading: false,
        attachmentError: null,
        statusUpdate: {
          loading: false,
          error: null,
          success: false
        }
      },
      user: {
        currentUser: mockUser,
        isAuthenticated: true
      },
      ...initialState
    }
  });
};

// Test wrapper component
const TestWrapper = ({ children, store = createTestStore() }) => {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('Task Management System', () => {
  
  describe('TeacherTasks Component', () => {
    test('renders task list correctly', () => {
      render(
        <TestWrapper>
          <TeacherTasks />
        </TestWrapper>
      );

      expect(screen.getByText('Task Management')).toBeInTheDocument();
      expect(screen.getByText('Math Assignment 1')).toBeInTheDocument();
      expect(screen.getByText('Science Project')).toBeInTheDocument();
    });

    test('displays task statistics', () => {
      render(
        <TestWrapper>
          <TeacherTasks />
        </TestWrapper>
      );

      expect(screen.getByText('Total Tasks')).toBeInTheDocument();
      expect(screen.getByText('Active Tasks')).toBeInTheDocument();
      expect(screen.getByText('Pending Grading')).toBeInTheDocument();
    });

    test('filters tasks by search term', async () => {
      render(
        <TestWrapper>
          <TeacherTasks />
        </TestWrapper>
      );

      const searchInput = screen.getByPlaceholderText(/search tasks/i);
      fireEvent.change(searchInput, { target: { value: 'Math' } });

      await waitFor(() => {
        expect(screen.getByText('Math Assignment 1')).toBeInTheDocument();
        expect(screen.queryByText('Science Project')).not.toBeInTheDocument();
      });
    });

    test('creates new task when button clicked', () => {
      render(
        <TestWrapper>
          <TeacherTasks />
        </TestWrapper>
      );

      const createButton = screen.getByText(/create task/i);
      fireEvent.click(createButton);

      expect(screen.getByText(/create new task/i)).toBeInTheDocument();
    });
  });

  describe('StudentTasks Component', () => {
    test('renders student task dashboard', () => {
      render(
        <TestWrapper>
          <StudentTasks />
        </TestWrapper>
      );

      expect(screen.getByText('My Tasks')).toBeInTheDocument();
      expect(screen.getByText('Math Assignment 1')).toBeInTheDocument();
      expect(screen.getByText('Science Project')).toBeInTheDocument();
    });

    test('displays task status badges correctly', () => {
      render(
        <TestWrapper>
          <StudentTasks />
        </TestWrapper>
      );

      expect(screen.getByText('Not Started')).toBeInTheDocument();
      expect(screen.getByText('Submitted')).toBeInTheDocument();
    });

    test('shows grade when task is graded', () => {
      render(
        <TestWrapper>
          <StudentTasks />
        </TestWrapper>
      );

      expect(screen.getByText('85/100')).toBeInTheDocument();
    });
  });

  describe('TaskSubmission Component', () => {
    const mockTask = mockTasks[0];
    const mockOnUpdate = jest.fn();

    test('renders submission form', () => {
      render(
        <TestWrapper>
          <TaskSubmission 
            task={mockTask}
            studentId="student1"
            onSubmissionUpdate={mockOnUpdate}
          />
        </TestWrapper>
      );

      expect(screen.getByText(/task submission/i)).toBeInTheDocument();
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    test('shows deadline warning for overdue tasks', () => {
      const overdueTask = {
        ...mockTask,
        deadline: '2020-01-01T00:00:00Z' // Past date
      };

      render(
        <TestWrapper>
          <TaskSubmission 
            task={overdueTask}
            studentId="student1"
            onSubmissionUpdate={mockOnUpdate}
          />
        </TestWrapper>
      );

      expect(screen.getByText(/overdue/i)).toBeInTheDocument();
    });

    test('enables submit button when text is entered', async () => {
      render(
        <TestWrapper>
          <TaskSubmission 
            task={mockTask}
            studentId="student1"
            onSubmissionUpdate={mockOnUpdate}
          />
        </TestWrapper>
      );

      const textArea = screen.getByRole('textbox');
      fireEvent.change(textArea, { target: { value: 'My submission text' } });

      await waitFor(() => {
        const submitButton = screen.getByText(/submit/i);
        expect(submitButton).not.toBeDisabled();
      });
    });
  });

  describe('TaskGrading Component', () => {
    const mockTask = mockTasks[0];
    const mockSubmission = mockSubmissions[0];
    const mockOnComplete = jest.fn();

    test('renders grading interface', () => {
      render(
        <TestWrapper>
          <TaskGrading 
            task={mockTask}
            submission={mockSubmission}
            onGradingComplete={mockOnComplete}
          />
        </TestWrapper>
      );

      expect(screen.getByText(/grading/i)).toBeInTheDocument();
      expect(screen.getByText(mockSubmission.student_name)).toBeInTheDocument();
      expect(screen.getByText(mockSubmission.submission_text)).toBeInTheDocument();
    });

    test('shows existing grade when submission is already graded', () => {
      const gradedSubmission = mockSubmissions[1];
      
      render(
        <TestWrapper>
          <TaskGrading 
            task={mockTask}
            submission={gradedSubmission}
            onGradingComplete={mockOnComplete}
          />
        </TestWrapper>
      );

      expect(screen.getByDisplayValue('92')).toBeInTheDocument();
      expect(screen.getByText('Excellent work! Well done.')).toBeInTheDocument();
    });

    test('validates grade input', async () => {
      render(
        <TestWrapper>
          <TaskGrading 
            task={mockTask}
            submission={mockSubmission}
            onGradingComplete={mockOnComplete}
          />
        </TestWrapper>
      );

      // Start grading
      const editButton = screen.getByText(/grade submission/i);
      fireEvent.click(editButton);

      // Enter invalid grade
      const gradeInput = screen.getByLabelText(/grade/i);
      fireEvent.change(gradeInput, { target: { value: '150' } });

      const submitButton = screen.getByText(/submit grade/i);
      fireEvent.click(submitButton);

      // Should show validation error
      await waitFor(() => {
        expect(screen.getByText(/valid grade/i)).toBeInTheDocument();
      });
    });
  });

  describe('TaskAttachments Component', () => {
    test('renders file upload area', () => {
      render(
        <TestWrapper>
          <TaskAttachments 
            taskId="1"
            attachmentType="task"
            canUpload={true}
          />
        </TestWrapper>
      );

      expect(screen.getByText(/drag.*drop.*files/i)).toBeInTheDocument();
      expect(screen.getByText(/click to browse/i)).toBeInTheDocument();
    });

    test('shows read-only mode when upload is disabled', () => {
      render(
        <TestWrapper>
          <TaskAttachments 
            taskId="1"
            attachmentType="task"
            canUpload={false}
          />
        </TestWrapper>
      );

      expect(screen.queryByText(/drag.*drop/i)).not.toBeInTheDocument();
      expect(screen.getByText(/no files attached/i)).toBeInTheDocument();
    });
  });

  describe('Integration Tests', () => {
    test('task workflow from creation to grading', async () => {
      // This would be a more complex integration test
      // Testing the full workflow of task creation, assignment, submission, and grading
      // For now, we'll just verify the components can work together
      
      const store = createTestStore();
      
      render(
        <TestWrapper store={store}>
          <TeacherTasks />
        </TestWrapper>
      );

      expect(screen.getByText('Task Management')).toBeInTheDocument();
      expect(screen.getByText('Math Assignment 1')).toBeInTheDocument();
    });
  });
});
