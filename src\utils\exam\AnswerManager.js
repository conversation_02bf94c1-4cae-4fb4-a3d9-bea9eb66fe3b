/**
 * Answer Manager Utility
 * Handles enhanced answer data format with timing information
 */

import QuestionTimer from './QuestionTimer';

class AnswerManager {
  constructor() {
    this.timer = new QuestionTimer();
    this.answers = {};
    this.lastSaveTime = null;
    this.autoSaveInterval = null;
    this.autoSaveDelay = 2000; // 2 seconds
  }

  /**
   * Initialize the answer manager
   * @param {Object} existingAnswers - Existing answers to load
   */
  initialize(existingAnswers = {}) {
    this.answers = this.normalizeAnswers(existingAnswers);
    this.timer.reset();
    this.startAutoSave();
    console.log('📝 Answer manager initialized with', Object.keys(this.answers).length, 'existing answers');
  }

  /**
   * Normalize answers to enhanced format
   * @param {Object} answers - Raw answers object
   * @returns {Object} Normalized answers
   */
  normalizeAnswers(answers) {
    const normalized = {};
    
    Object.keys(answers).forEach(questionId => {
      const answer = answers[questionId];
      
      if (typeof answer === 'string') {
        // Convert simple format to enhanced format
        normalized[questionId] = {
          answer: answer,
          text: answer, // compatibility alias
          time_spent_seconds: 0,
          submitted_at: new Date().toISOString(),
          last_modified: new Date().toISOString()
        };
      } else if (typeof answer === 'object' && answer !== null) {
        // Already enhanced format, ensure all fields exist
        normalized[questionId] = {
          answer: answer.answer || answer.text || '',
          text: answer.text || answer.answer || '', // compatibility alias
          time_spent_seconds: answer.time_spent_seconds || 0,
          submitted_at: answer.submitted_at || new Date().toISOString(),
          last_modified: new Date().toISOString(),
          ...answer // preserve any additional fields
        };
      }
    });
    
    return normalized;
  }

  /**
   * Navigate to a question (start timing)
   * @param {string} questionId - The question ID
   */
  navigateToQuestion(questionId) {
    this.timer.startQuestion(questionId);
    console.log(`🧭 Navigated to question: ${questionId}`);
  }

  /**
   * Save an answer for a question
   * @param {string} questionId - The question ID
   * @param {string|Object} answerData - The answer data
   * @param {boolean} autoSave - Whether to trigger auto-save
   */
  saveAnswer(questionId, answerData, autoSave = true) {
    if (!questionId) {
      console.error('❌ Cannot save answer: questionId is required');
      return;
    }

    const currentTime = new Date().toISOString();
    const timeSpent = this.timer.getQuestionTime(questionId);

    // Handle both string and object answer formats
    let answerText = '';
    if (typeof answerData === 'string') {
      answerText = answerData;
    } else if (typeof answerData === 'object' && answerData !== null) {
      answerText = answerData.answer || answerData.text || '';
    }

    // Create enhanced answer object
    this.answers[questionId] = {
      answer: answerText,
      text: answerText, // compatibility alias
      time_spent_seconds: timeSpent,
      submitted_at: this.answers[questionId]?.submitted_at || currentTime,
      last_modified: currentTime
    };

    console.log(`💾 Saved answer for question ${questionId}:`, {
      length: answerText.length,
      timeSpent: timeSpent
    });

    // Trigger auto-save if enabled
    if (autoSave) {
      this.scheduleAutoSave();
    }

    return this.answers[questionId];
  }

  /**
   * Get answer for a question
   * @param {string} questionId - The question ID
   * @param {boolean} simpleFormat - Whether to return simple string format
   * @returns {string|Object} The answer
   */
  getAnswer(questionId, simpleFormat = false) {
    const answer = this.answers[questionId];
    
    if (!answer) {
      return simpleFormat ? '' : null;
    }
    
    if (simpleFormat) {
      return answer.answer || answer.text || '';
    }
    
    return answer;
  }

  /**
   * Get all answers
   * @param {boolean} simpleFormat - Whether to return simple format
   * @returns {Object} All answers
   */
  getAllAnswers(simpleFormat = false) {
    if (simpleFormat) {
      const simple = {};
      Object.keys(this.answers).forEach(questionId => {
        simple[questionId] = this.answers[questionId].answer || this.answers[questionId].text || '';
      });
      return simple;
    }
    
    return { ...this.answers };
  }

  /**
   * Prepare answers for submission
   * @returns {Object} Enhanced answers with final timing data
   */
  prepareForSubmission() {
    // Update all timing data
    const allTimes = this.timer.getAllTimes();
    const submissionTime = new Date().toISOString();
    
    // Update timing for all answers
    Object.keys(this.answers).forEach(questionId => {
      this.answers[questionId].time_spent_seconds = allTimes[questionId] || 0;
      this.answers[questionId].final_submission_time = submissionTime;
    });

    console.log('🚀 Prepared answers for submission:', {
      totalAnswers: Object.keys(this.answers).length,
      totalTime: this.timer.getTotalSessionTime(),
      timingData: allTimes
    });

    return {
      answers: { ...this.answers },
      timingData: this.timer.exportForSubmission(),
      submissionMetadata: {
        submitted_at: submissionTime,
        total_answers: Object.keys(this.answers).length,
        total_session_time: this.timer.getTotalSessionTime()
      }
    };
  }

  /**
   * Schedule auto-save
   */
  scheduleAutoSave() {
    if (this.autoSaveInterval) {
      clearTimeout(this.autoSaveInterval);
    }

    this.autoSaveInterval = setTimeout(() => {
      this.triggerAutoSave();
    }, this.autoSaveDelay);
  }

  /**
   * Trigger auto-save callback
   */
  triggerAutoSave() {
    this.lastSaveTime = Date.now();
    
    // Emit auto-save event
    if (this.onAutoSave) {
      this.onAutoSave(this.getAllAnswers());
    }
    
    console.log('💾 Auto-save triggered');
  }

  /**
   * Start auto-save mechanism
   */
  startAutoSave() {
    console.log('🔄 Auto-save mechanism started');
  }

  /**
   * Stop auto-save mechanism
   */
  stopAutoSave() {
    if (this.autoSaveInterval) {
      clearTimeout(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }
    console.log('⏹️ Auto-save mechanism stopped');
  }

  /**
   * Set auto-save callback
   * @param {Function} callback - Callback function for auto-save
   */
  setAutoSaveCallback(callback) {
    this.onAutoSave = callback;
  }

  /**
   * Pause timing (when exam is paused or student navigates away)
   */
  pauseTiming() {
    this.timer.pauseTiming();
  }

  /**
   * Resume timing (when exam is resumed)
   */
  resumeTiming() {
    this.timer.resumeTiming();
  }

  /**
   * Get timing statistics
   * @returns {Object} Timing statistics
   */
  getTimingStatistics() {
    return this.timer.getStatistics();
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.stopAutoSave();
    this.timer.reset();
    console.log('🧹 Answer manager cleaned up');
  }
}

export default AnswerManager;
