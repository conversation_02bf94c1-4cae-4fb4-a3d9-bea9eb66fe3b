/**
 * Question Timer Utility
 * Tracks time spent on individual questions during exam sessions
 */

class QuestionTimer {
  constructor() {
    this.questionTimes = {};
    this.currentQuestion = null;
    this.startTime = null;
    this.totalSessionTime = 0;
    this.sessionStartTime = Date.now();
  }

  /**
   * Start timing a question
   * @param {string} questionId - The question ID to start timing
   */
  startQuestion(questionId) {
    // Save time for previous question
    if (this.currentQuestion && this.startTime) {
      this.saveQuestionTime(this.currentQuestion);
    }

    // Start timing new question
    this.currentQuestion = questionId;
    this.startTime = Date.now();
    
    console.log(`⏱️ Started timing question: ${questionId}`);
  }

  /**
   * Save the time spent on a question
   * @param {string} questionId - The question ID to save time for
   */
  saveQuestionTime(questionId) {
    if (this.startTime) {
      const timeSpent = Math.floor((Date.now() - this.startTime) / 1000);
      this.questionTimes[questionId] = (this.questionTimes[questionId] || 0) + timeSpent;
      
      console.log(`⏱️ Saved ${timeSpent}s for question ${questionId} (total: ${this.questionTimes[questionId]}s)`);
    }
  }

  /**
   * Get time spent on a specific question
   * @param {string} questionId - The question ID
   * @returns {number} Time spent in seconds
   */
  getQuestionTime(questionId) {
    return this.questionTimes[questionId] || 0;
  }

  /**
   * Get all question times
   * @returns {Object} Object with questionId as keys and time in seconds as values
   */
  getAllTimes() {
    // Save current question time before returning
    if (this.currentQuestion && this.startTime) {
      this.saveQuestionTime(this.currentQuestion);
    }
    
    return { ...this.questionTimes };
  }

  /**
   * Pause timing (when student navigates away or exam is paused)
   */
  pauseTiming() {
    if (this.currentQuestion && this.startTime) {
      this.saveQuestionTime(this.currentQuestion);
      this.startTime = null;
      console.log(`⏸️ Paused timing for question: ${this.currentQuestion}`);
    }
  }

  /**
   * Resume timing (when student returns to exam)
   */
  resumeTiming() {
    if (this.currentQuestion && !this.startTime) {
      this.startTime = Date.now();
      console.log(`▶️ Resumed timing for question: ${this.currentQuestion}`);
    }
  }

  /**
   * Get total session time
   * @returns {number} Total session time in seconds
   */
  getTotalSessionTime() {
    return Math.floor((Date.now() - this.sessionStartTime) / 1000);
  }

  /**
   * Reset all timers
   */
  reset() {
    this.questionTimes = {};
    this.currentQuestion = null;
    this.startTime = null;
    this.sessionStartTime = Date.now();
    console.log('🔄 Question timer reset');
  }

  /**
   * Get timing statistics
   * @returns {Object} Timing statistics
   */
  getStatistics() {
    const times = this.getAllTimes();
    const totalQuestionTime = Object.values(times).reduce((sum, time) => sum + time, 0);
    const totalSessionTime = this.getTotalSessionTime();
    
    return {
      totalQuestionTime,
      totalSessionTime,
      averageTimePerQuestion: Object.keys(times).length > 0 ? totalQuestionTime / Object.keys(times).length : 0,
      questionsAnswered: Object.keys(times).length,
      currentQuestion: this.currentQuestion,
      questionTimes: times
    };
  }

  /**
   * Export timer data for submission
   * @returns {Object} Timer data formatted for API submission
   */
  exportForSubmission() {
    const allTimes = this.getAllTimes();
    const stats = this.getStatistics();
    
    return {
      questionTimes: allTimes,
      totalSessionTime: stats.totalSessionTime,
      totalQuestionTime: stats.totalQuestionTime,
      averageTimePerQuestion: stats.averageTimePerQuestion,
      questionsAnswered: stats.questionsAnswered,
      exportedAt: new Date().toISOString()
    };
  }
}

export default QuestionTimer;
