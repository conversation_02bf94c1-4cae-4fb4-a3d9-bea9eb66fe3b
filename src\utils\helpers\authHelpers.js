/**
 * Authentication helper functions
 */

/**
 * Get authentication token from localStorage
 * @returns {string|null} The authentication token or null if not found
 */
export const getAuthToken = () => {
  try {
    return localStorage.getItem('token');
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

/**
 * Set authentication token in localStorage
 * @param {string} token - The authentication token to store
 */
export const setAuthToken = (token) => {
  try {
    localStorage.setItem('token', token);
  } catch (error) {
    console.error('Error setting auth token:', error);
  }
};

/**
 * Remove authentication token from localStorage
 */
export const removeAuthToken = () => {
  try {
    localStorage.removeItem('token');
  } catch (error) {
    console.error('Error removing auth token:', error);
  }
};

/**
 * Get user role from localStorage
 * @returns {string|null} The user role or null if not found
 */
export const getUserRole = () => {
  try {
    return localStorage.getItem('role');
  } catch (error) {
    console.error('Error getting user role:', error);
    return null;
  }
};

/**
 * Set user role in localStorage
 * @param {string} role - The user role to store
 */
export const setUserRole = (role) => {
  try {
    localStorage.setItem('role', role);
  } catch (error) {
    console.error('Error setting user role:', error);
  }
};

/**
 * Get user data from localStorage
 * @returns {Object|null} The user data object or null if not found
 */
export const getUserData = () => {
  try {
    const userData = localStorage.getItem('userdata');
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error getting user data:', error);
    return null;
  }
};

/**
 * Set user data in localStorage
 * @param {Object} userData - The user data object to store
 */
export const setUserData = (userData) => {
  try {
    localStorage.setItem('userdata', JSON.stringify(userData));
  } catch (error) {
    console.error('Error setting user data:', error);
  }
};

/**
 * Clear all authentication data from localStorage
 */
export const clearAuthData = () => {
  try {
    localStorage.removeItem('token');
    localStorage.removeItem('role');
    localStorage.removeItem('userdata');
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
};

/**
 * Check if user is authenticated
 * @returns {boolean} True if user has a valid token, false otherwise
 */
export const isAuthenticated = () => {
  const token = getAuthToken();
  const role = getUserRole();
  return !!(token && role);
};

/**
 * Get authorization header for API requests
 * @returns {Object} Authorization header object or empty object if no token
 */
export const getAuthHeader = () => {
  const token = getAuthToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
};

/**
 * Create headers with authentication for API requests
 * @param {Object} additionalHeaders - Additional headers to include
 * @returns {Object} Headers object with authentication
 */
export const createAuthHeaders = (additionalHeaders = {}) => {
  return {
    'Content-Type': 'application/json',
    ...getAuthHeader(),
    ...additionalHeaders
  };
};

/**
 * Get current user information
 * @returns {Object|null} Current user object or null if not found
 */
export const getCurrentUser = () => {
  try {
    const userData = getUserData();
    if (userData) {
      return userData;
    }

    // Fallback: try to get user info from token or other sources
    const role = getUserRole();
    if (role) {
      return { role };
    }

    return null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};
