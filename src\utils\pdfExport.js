import jsPDF from 'jspdf';

/**
 * Export exam to PDF
 * @param {Object} exam - The exam object
 * @param {boolean} includeAnswers - Whether to include answers in the PDF
 */
export const exportExamToPDF = (exam, includeAnswers = false) => {
  const pdf = new jsPDF();
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  const margin = 20;
  const lineHeight = 7;
  let yPosition = margin;

  // Helper function to add text with word wrapping
  const addText = (text, fontSize = 12, isBold = false) => {
    pdf.setFontSize(fontSize);
    pdf.setFont('helvetica', isBold ? 'bold' : 'normal');

    const lines = pdf.splitTextToSize(text, pageWidth - 2 * margin);

    // Check if we need a new page
    if (yPosition + (lines.length * lineHeight) > pageHeight - margin) {
      pdf.addPage();
      yPosition = margin;
    }

    lines.forEach(line => {
      pdf.text(line, margin, yPosition);
      yPosition += lineHeight;
    });

    return yPosition;
  };

  // Helper function to add spacing
  const addSpacing = (space = 10) => {
    yPosition += space;
    if (yPosition > pageHeight - margin) {
      pdf.addPage();
      yPosition = margin;
    }
  };

  // Helper function to add answer lines for students
  const addAnswerLines = (questionType, numLines = 1) => {
    if (includeAnswers) return; // Don't add answer lines if showing answers

    const lineWidth = pageWidth - 2 * margin - 10;
    const lineSpacing = 8;

    for (let i = 0; i < numLines; i++) {
      // Check if we need a new page
      if (yPosition + lineSpacing > pageHeight - margin) {
        pdf.addPage();
        yPosition = margin;
      }

      // Draw answer line
      pdf.setLineWidth(0.5);
      pdf.line(margin + 10, yPosition + 3, margin + lineWidth, yPosition + 3);
      yPosition += lineSpacing;
    }

    addSpacing(5);
  };

  // Helper function to add MCQ answer options for students
  const addMCQAnswerSpace = (options) => {
    if (includeAnswers) return; // Don't add answer spaces if showing answers

    addSpacing(3);
    addText('Choose the correct answer:', 10, true);
    addSpacing(3);

    options.forEach((option, optIndex) => {
      const optionLetter = String.fromCharCode(65 + optIndex); // A, B, C, D

      // Check if we need a new page
      if (yPosition + lineHeight > pageHeight - margin) {
        pdf.addPage();
        yPosition = margin;
      }

      // Add option with checkbox
      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'normal');

      // Draw checkbox
      const checkboxSize = 4;
      pdf.rect(margin, yPosition - 3, checkboxSize, checkboxSize);

      // Add option text
      const optionText = `${optionLetter}) ${option.option_text || option}`;
      pdf.text(optionText, margin + checkboxSize + 5, yPosition);
      yPosition += lineHeight + 2;
    });

    addSpacing(5);
  };

  try {
    // Title
    addText(exam.title || 'Exam', 18, true);
    addSpacing(10);

    // Student Information Section (only for student PDFs)
    if (!includeAnswers) {
      addText('Student Information:', 14, true);
      addSpacing(5);

      // Name field
      addText('Name: ________________________________', 12);
      addSpacing(8);

      // Roll Number field
      addText('Roll Number: _________________________', 12);
      addSpacing(8);

      // Date field
      addText('Date: ________________________________', 12);
      addSpacing(15);
    }

    // Basic Exam Details (simplified)
    addText('Instructions:', 14, true);
    addSpacing(3);
    addText('• Read all questions carefully before answering.', 11);
    addText('• Write your answers clearly in the spaces provided.', 11);
    addText('• For multiple choice questions, mark the correct option clearly.', 11);

    if (exam.total_duration) {
      addText(`• Time allowed: ${exam.total_duration} minutes`, 11);
    }

    if (exam.total_marks) {
      addText(`• Total marks: ${exam.total_marks}`, 11);
    }

    addSpacing(15);

    // Questions
    if (exam.questions && exam.questions.length > 0) {
      exam.questions.forEach((question, index) => {
        // Question number and text
        addText(`${index + 1}. ${question.text || question.question_text || 'No question text'}`, 12, false);

        // Add marks if available
        if (question.marks) {
          addText(`[${question.marks} marks]`, 10);
        }

        addSpacing(8);

        // Determine question type and add appropriate answer space
        const questionType = question.Type || question.question_type || '';
        const isMultipleChoice = questionType.toLowerCase() === 'mcqs' || questionType.toLowerCase() === 'mcq';

        if (isMultipleChoice && question.options) {
          // For MCQ questions - show options with checkboxes for students
          if (includeAnswers) {
            // For teachers - show options with correct answer marked
            addText('Options:', 11, true);
            question.options.forEach((option, optIndex) => {
              const optionLetter = String.fromCharCode(65 + optIndex); // A, B, C, D
              let optionText = `${optionLetter}) ${option.option_text || option}`;

              // Mark correct answer if including answers
              if (option.is_correct) {
                optionText += ' ✓ (Correct Answer)';
              }

              addText(optionText, 11);
            });
          } else {
            // For students - show options with checkboxes
            addMCQAnswerSpace(question.options);
          }
        } else {
          // For non-MCQ questions
          if (includeAnswers) {
            // For teachers - show the correct answer
            const correctAnswer = question.answer || question.correct_answer;
            if (correctAnswer) {
              addText('Answer:', 11, true);
              addText(correctAnswer, 11);
            }
          } else {
            // For students - add answer lines based on question type
            let numLines = 2; // Default for short answer

            if (questionType.toLowerCase().includes('long') ||
                questionType.toLowerCase().includes('essay') ||
                questionType.toLowerCase().includes('descriptive')) {
              numLines = 6; // Long answer questions
            } else if (questionType.toLowerCase().includes('short')) {
              numLines = 2; // Short answer questions
            } else if (questionType.toLowerCase().includes('fill') ||
                      questionType.toLowerCase().includes('blank')) {
              numLines = 1; // Fill in the blanks
            }

            addAnswerLines(questionType, numLines);
          }
        }

        addSpacing(15);
      });
    } else {
      addText('No questions available for this exam.', 12);
    }

    // Footer
    const totalPages = pdf.internal.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i);
      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');

      // Page number
      pdf.text(`Page ${i} of ${totalPages}`, pageWidth - margin - 30, pageHeight - 10);

      if (includeAnswers) {
        // For teacher version - include generation timestamp and answer note
        const timestamp = new Date().toLocaleString();
        pdf.text(`Generated: ${timestamp}`, margin, pageHeight - 10);
        pdf.text('(Answer Key)', pageWidth / 2 - 20, pageHeight - 10);
      } else {
        // For student version - add signature line
        pdf.text('Student Signature: ________________________', margin, pageHeight - 10);
      }
    }

    // Generate filename
    const examTitle = (exam.title || 'Exam').replace(/[^a-zA-Z0-9]/g, '_');
    const answerSuffix = includeAnswers ? '_Answer_Key' : '_Question_Paper';
    const filename = `${examTitle}${answerSuffix}.pdf`;

    // Save the PDF
    pdf.save(filename);

    return { success: true, filename };
  } catch (error) {
    console.error('Error generating PDF:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Export exam question paper for students
 * Includes answer spaces, student info fields, and instructions
 * Removes subject/class details and shows only essential information
 */
export const exportExamForStudents = (exam) => {
  return exportExamToPDF(exam, false);
};

/**
 * Export exam answer key for teachers
 * Includes correct answers and all question details
 */
export const exportExamForTeachers = (exam) => {
  return exportExamToPDF(exam, true);
};
