/**
 * Subscription Error Handling Utilities
 * Provides standardized error handling for subscription-related API responses
 */

/**
 * Check if an error is subscription-related
 * @param {Object} error - Error object from API response
 * @returns {boolean} - Whether the error is subscription-related
 */
export const isSubscriptionError = (error) => {
  if (!error) return false;
  
  const errorMessage = error.detail || error.message || '';
  const subscriptionKeywords = [
    'subscription',
    'plan',
    'upgrade',
    'limit',
    'feature not available',
    'expired',
    'premium',
    'basic'
  ];
  
  return subscriptionKeywords.some(keyword => 
    errorMessage.toLowerCase().includes(keyword)
  );
};

/**
 * Parse subscription error and extract useful information
 * @param {Object} error - Error object from API response
 * @returns {Object} - Parsed error information
 */
export const parseSubscriptionError = (error) => {
  const errorMessage = error.detail || error.message || 'Unknown error';
  
  // Feature not available error
  if (errorMessage.includes('not available in your')) {
    const match = errorMessage.match(/not available in your (.+?)\. Please upgrade to one of: (.+)/);
    if (match) {
      return {
        type: 'FEATURE_NOT_AVAILABLE',
        currentPlan: match[1],
        suggestedPlans: match[2].split(', '),
        message: errorMessage,
        actionRequired: 'upgrade'
      };
    }
  }
  
  // Usage limit exceeded error
  if (errorMessage.includes('reached your') && errorMessage.includes('limit')) {
    const match = errorMessage.match(/reached your (.+?) limit \((.+?)\)/);
    if (match) {
      return {
        type: 'USAGE_LIMIT_EXCEEDED',
        limitType: match[1],
        currentUsage: match[2],
        message: errorMessage,
        actionRequired: 'upgrade'
      };
    }
  }
  
  // Subscription expired error
  if (errorMessage.includes('expired')) {
    return {
      type: 'SUBSCRIPTION_EXPIRED',
      message: errorMessage,
      actionRequired: 'renew'
    };
  }
  
  // Plan not available error
  if (errorMessage.includes('not available for') && errorMessage.includes('users')) {
    return {
      type: 'PLAN_NOT_AVAILABLE',
      message: errorMessage,
      actionRequired: 'select_different_plan'
    };
  }
  
  // Home tutoring subscription required
  if (errorMessage.includes('does not allow home tutoring')) {
    return {
      type: 'HOME_TUTORING_NOT_ALLOWED',
      message: errorMessage,
      actionRequired: 'upgrade',
      suggestedPlans: ['Teacher Home Tutor']
    };
  }
  
  // No active subscription
  if (errorMessage.includes('No active subscription')) {
    return {
      type: 'NO_ACTIVE_SUBSCRIPTION',
      message: errorMessage,
      actionRequired: 'subscribe'
    };
  }
  
  // Generic subscription error
  return {
    type: 'SUBSCRIPTION_ERROR',
    message: errorMessage,
    actionRequired: 'check_subscription'
  };
};

/**
 * Get user-friendly error message for subscription errors
 * @param {Object} parsedError - Parsed error from parseSubscriptionError
 * @returns {string} - User-friendly error message
 */
export const getSubscriptionErrorMessage = (parsedError) => {
  switch (parsedError.type) {
    case 'FEATURE_NOT_AVAILABLE':
      return `This feature requires a premium subscription. Please upgrade from ${parsedError.currentPlan} to access this feature.`;
    
    case 'USAGE_LIMIT_EXCEEDED':
      return `You've reached your ${parsedError.limitType} limit (${parsedError.currentUsage}). Upgrade your plan to continue.`;
    
    case 'SUBSCRIPTION_EXPIRED':
      return 'Your subscription has expired. Please renew to continue using premium features.';
    
    case 'PLAN_NOT_AVAILABLE':
      return 'This plan is not available for your account type. Please select a different plan.';
    
    case 'HOME_TUTORING_NOT_ALLOWED':
      return 'Home tutoring requires a special subscription plan. Upgrade to Teacher Home Tutor to enable this feature.';
    
    case 'NO_ACTIVE_SUBSCRIPTION':
      return 'You need an active subscription to use this feature. Please subscribe to continue.';
    
    default:
      return parsedError.message || 'A subscription-related error occurred. Please check your subscription status.';
  }
};

/**
 * Get suggested actions for subscription errors
 * @param {Object} parsedError - Parsed error from parseSubscriptionError
 * @returns {Array} - Array of suggested action objects
 */
export const getSubscriptionErrorActions = (parsedError) => {
  const actions = [];
  
  switch (parsedError.actionRequired) {
    case 'upgrade':
      actions.push({
        label: 'Upgrade Plan',
        type: 'primary',
        action: 'upgrade',
        data: { suggestedPlans: parsedError.suggestedPlans }
      });
      actions.push({
        label: 'View Plans',
        type: 'secondary',
        action: 'view_plans'
      });
      break;
    
    case 'renew':
      actions.push({
        label: 'Renew Subscription',
        type: 'primary',
        action: 'renew'
      });
      actions.push({
        label: 'View Subscription',
        type: 'secondary',
        action: 'view_subscription'
      });
      break;
    
    case 'subscribe':
      actions.push({
        label: 'Choose Plan',
        type: 'primary',
        action: 'choose_plan'
      });
      actions.push({
        label: 'Learn More',
        type: 'secondary',
        action: 'learn_more'
      });
      break;
    
    case 'select_different_plan':
      actions.push({
        label: 'View Available Plans',
        type: 'primary',
        action: 'view_plans'
      });
      break;
    
    default:
      actions.push({
        label: 'Check Subscription',
        type: 'primary',
        action: 'view_subscription'
      });
      break;
  }
  
  return actions;
};

/**
 * Handle subscription error with automatic action suggestions
 * @param {Object} error - Error object from API response
 * @param {Function} onAction - Callback function for handling actions
 * @returns {Object} - Complete error handling object
 */
export const handleSubscriptionError = (error, onAction = null) => {
  if (!isSubscriptionError(error)) {
    return null; // Not a subscription error
  }
  
  const parsedError = parseSubscriptionError(error);
  const userMessage = getSubscriptionErrorMessage(parsedError);
  const suggestedActions = getSubscriptionErrorActions(parsedError);
  
  return {
    isSubscriptionError: true,
    type: parsedError.type,
    message: userMessage,
    originalMessage: parsedError.message,
    actions: suggestedActions,
    onAction,
    
    // Helper methods
    executeAction: (actionType, data = {}) => {
      if (onAction) {
        onAction(actionType, { ...data, errorType: parsedError.type });
      }
    },
    
    // Quick action methods
    upgrade: () => onAction?.('upgrade', { suggestedPlans: parsedError.suggestedPlans }),
    viewPlans: () => onAction?.('view_plans'),
    viewSubscription: () => onAction?.('view_subscription'),
    renew: () => onAction?.('renew'),
    choosePlan: () => onAction?.('choose_plan')
  };
};

/**
 * Create a subscription error toast configuration
 * @param {Object} error - Error object from API response
 * @returns {Object} - Toast configuration object
 */
export const createSubscriptionErrorToast = (error) => {
  const handledError = handleSubscriptionError(error);
  
  if (!handledError) return null;
  
  return {
    type: 'error',
    title: 'Subscription Required',
    message: handledError.message,
    duration: 8000, // Longer duration for subscription errors
    actions: handledError.actions.slice(0, 2), // Limit to 2 actions in toast
    persistent: handledError.type === 'SUBSCRIPTION_EXPIRED' // Keep expired subscription errors visible
  };
};

export default {
  isSubscriptionError,
  parseSubscriptionError,
  getSubscriptionErrorMessage,
  getSubscriptionErrorActions,
  handleSubscriptionError,
  createSubscriptionErrorToast
};
