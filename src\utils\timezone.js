/**
 * Timezone Utility Functions
 *
 * EduFair exam system uses UTC for all exam scheduling to ensure consistent
 * timing across different geographical locations.
 *
 * Backend stores all times in UTC, frontend converts between local and UTC.
 * Uses IP-based timezone detection for better user experience.
 */

// Cache for timezone data
let cachedTimezoneData = null;
let timezoneDetectionPromise = null;

/**
 * Detect user's timezone using IP geolocation with fallback to browser
 * @returns {Promise<Object>} Timezone data with timezone, country, city info
 */
export async function detectUserTimezone() {
  // Return cached data if available
  if (cachedTimezoneData) {
    return cachedTimezoneData;
  }

  // Return existing promise if detection is in progress
  if (timezoneDetectionPromise) {
    return timezoneDetectionPromise;
  }

  timezoneDetectionPromise = (async () => {
    try {
      // Try multiple IP geolocation services for better reliability
      const services = [
        'https://ipapi.co/json/',
        'https://api.ipgeolocation.io/ipgeo?apiKey=free', // Free tier
        'http://ip-api.com/json/?fields=status,country,countryCode,region,regionName,city,timezone,query'
      ];

      for (const serviceUrl of services) {
        try {
          const response = await fetch(serviceUrl);

          if (response.ok) {
            const data = await response.json();

            // Handle different API response formats
            let timezone, country, city, region, countryCode, ip;

            if (serviceUrl.includes('ipapi.co')) {
              timezone = data.timezone;
              country = data.country_name;
              city = data.city;
              region = data.region;
              countryCode = data.country_code;
              ip = data.ip;
            } else if (serviceUrl.includes('ipgeolocation.io')) {
              timezone = data.time_zone?.name;
              country = data.country_name;
              city = data.city;
              region = data.state_prov;
              countryCode = data.country_code2;
              ip = data.ip;
            } else if (serviceUrl.includes('ip-api.com')) {
              if (data.status === 'success') {
                timezone = data.timezone;
                country = data.country;
                city = data.city;
                region = data.regionName;
                countryCode = data.countryCode;
                ip = data.query;
              }
            }

            if (timezone && country) {
              cachedTimezoneData = {
                timezone,
                country,
                countryCode: countryCode || 'XX',
                region: region || 'Unknown',
                city: city || 'Unknown',
                ip: ip || 'Unknown',
                source: 'ip-geolocation',
                detected: true
              };

              console.log('🌍 Timezone detected via IP:', cachedTimezoneData);
              return cachedTimezoneData;
            }
          }
        } catch (serviceError) {
          console.warn(`Service ${serviceUrl} failed:`, serviceError);
          continue; // Try next service
        }
      }
    } catch (error) {
      console.warn('All IP-based timezone detection services failed:', error);
    }

    // Fallback to browser timezone
    const browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    cachedTimezoneData = {
      timezone: browserTimezone,
      country: 'Unknown',
      countryCode: 'XX',
      region: 'Unknown',
      city: 'Unknown',
      ip: 'Unknown',
      source: 'browser',
      detected: false
    };

    console.log('🌐 Using browser timezone fallback:', cachedTimezoneData);
    return cachedTimezoneData;
  })();

  return timezoneDetectionPromise;
}

/**
 * Get user's timezone (with caching and IP detection)
 * @returns {string} User's timezone (e.g., "Asia/Karachi", "America/New_York")
 */
export function getUserTimezone() {
  // Return cached timezone if available
  if (cachedTimezoneData) {
    return cachedTimezoneData.timezone;
  }

  // Fallback to browser timezone for synchronous calls
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Get user's location info (async)
 * @returns {Promise<Object>} Location data
 */
export async function getUserLocation() {
  const timezoneData = await detectUserTimezone();
  return {
    country: timezoneData.country,
    city: timezoneData.city,
    region: timezoneData.region,
    timezone: timezoneData.timezone
  };
}

/**
 * Convert local time to UTC for API calls
 * @param {string} localDateTime - Local datetime string (e.g., "2024-01-15T19:00")
 * @returns {string|null} UTC datetime string without 'Z' (e.g., "2024-01-15T14:00:00") or null if invalid
 */
export function convertLocalToUTC(localDateTime) {
  try {
    const localDate = new Date(localDateTime);
    
    if (isNaN(localDate.getTime())) {
      throw new Error('Invalid local date');
    }

    // Convert to UTC ISO string and remove 'Z'
    return localDate.toISOString().slice(0, 19);
  } catch (error) {
    console.error('Error converting local to UTC:', error);
    return null;
  }
}

/**
 * Convert UTC time to local time for display
 * @param {string} utcDateTime - UTC datetime string (e.g., "2024-01-15T14:00:00")
 * @param {string} timezone - Target timezone (optional, defaults to user's timezone)
 * @returns {string} Formatted local time string
 */
export function convertUTCToLocal(utcDateTime, timezone = null) {
  try {
    // Add 'Z' if not present to ensure it's treated as UTC
    const utcString = utcDateTime.endsWith('Z') ? utcDateTime : utcDateTime + 'Z';
    const utcDate = new Date(utcString);
    
    if (isNaN(utcDate.getTime())) {
      throw new Error('Invalid UTC date');
    }

    const targetTimezone = timezone || getUserTimezone();
    
    return new Intl.DateTimeFormat('en-US', {
      timeZone: targetTimezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(utcDate);
  } catch (error) {
    console.error('Error converting UTC to local:', error);
    return 'Invalid Date';
  }
}

/**
 * Convert UTC time to local datetime-local input format
 * @param {string} utcDateTime - UTC datetime string
 * @returns {string} Local datetime in format "YYYY-MM-DDTHH:mm"
 */
export function convertUTCToLocalInput(utcDateTime) {
  try {
    const utcString = utcDateTime.endsWith('Z') ? utcDateTime : utcDateTime + 'Z';
    const utcDate = new Date(utcString);
    
    if (isNaN(utcDate.getTime())) {
      throw new Error('Invalid UTC date');
    }

    // Convert to local time for datetime-local input
    const year = utcDate.getFullYear();
    const month = String(utcDate.getMonth() + 1).padStart(2, '0');
    const day = String(utcDate.getDate()).padStart(2, '0');
    const hours = String(utcDate.getHours()).padStart(2, '0');
    const minutes = String(utcDate.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  } catch (error) {
    console.error('Error converting UTC to local input:', error);
    return '';
  }
}

/**
 * Format datetime with timezone information for display (enhanced with location)
 * @param {string} utcDateTime - UTC datetime string
 * @param {string} timezone - Target timezone (optional)
 * @param {boolean} showLocation - Whether to show city/country info
 * @returns {Promise<string>} Formatted string with timezone and location info
 */
export async function formatDateTimeWithTimezone(utcDateTime, timezone = null, showLocation = false) {
  try {
    const utcString = utcDateTime.endsWith('Z') ? utcDateTime : utcDateTime + 'Z';
    const utcDate = new Date(utcString);

    if (isNaN(utcDate.getTime())) {
      throw new Error('Invalid UTC date');
    }

    const timezoneData = await detectUserTimezone();
    const targetTimezone = timezone || timezoneData.timezone;

    const formattedDate = new Intl.DateTimeFormat('en-US', {
      timeZone: targetTimezone,
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).format(utcDate);

    if (showLocation && timezoneData.detected) {
      return `${formattedDate} (${timezoneData.city}, ${timezoneData.country})`;
    } else {
      // Get timezone abbreviation
      const timezoneName = new Intl.DateTimeFormat('en-US', {
        timeZone: targetTimezone,
        timeZoneName: 'short'
      }).formatToParts(utcDate).find(part => part.type === 'timeZoneName')?.value || targetTimezone;

      return `${formattedDate} ${timezoneName}`;
    }
  } catch (error) {
    console.error('Error formatting datetime with timezone:', error);
    return 'Invalid Date';
  }
}

/**
 * Synchronous version for immediate display (uses cached data)
 * @param {string} utcDateTime - UTC datetime string
 * @param {string} timezone - Target timezone (optional)
 * @returns {string} Formatted string
 */
export function formatDateTimeSync(utcDateTime, timezone = null) {
  try {
    // Check if utcDateTime is valid
    if (!utcDateTime || typeof utcDateTime !== 'string') {
      return 'Not set';
    }

    const utcString = utcDateTime.endsWith('Z') ? utcDateTime : utcDateTime + 'Z';
    const utcDate = new Date(utcString);

    if (isNaN(utcDate.getTime())) {
      throw new Error('Invalid UTC date');
    }

    const targetTimezone = timezone || getUserTimezone();

    const formattedDate = new Intl.DateTimeFormat('en-US', {
      timeZone: targetTimezone,
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).format(utcDate);

    // Show location if we have cached data
    if (cachedTimezoneData && cachedTimezoneData.detected) {
      return `${formattedDate} (${cachedTimezoneData.city}, ${cachedTimezoneData.country})`;
    } else {
      // Get timezone abbreviation
      const timezoneName = new Intl.DateTimeFormat('en-US', {
        timeZone: targetTimezone,
        timeZoneName: 'short'
      }).formatToParts(utcDate).find(part => part.type === 'timeZoneName')?.value || targetTimezone;

      return `${formattedDate} ${timezoneName}`;
    }
  } catch (error) {
    console.error('Error formatting datetime:', error);
    return 'Invalid Date';
  }
}

/**
 * Check if a datetime string is valid
 * @param {string} dateTimeString - Datetime string to validate
 * @returns {boolean} True if valid, false otherwise
 */
export function isValidDateTime(dateTimeString) {
  try {
    const date = new Date(dateTimeString);
    return !isNaN(date.getTime());
  } catch (error) {
    return false;
  }
}

/**
 * Get current UTC time as ISO string without 'Z'
 * @returns {string} Current UTC time (e.g., "2024-01-15T14:30:00")
 */
export function getCurrentUTC() {
  return new Date().toISOString().slice(0, 19);
}

/**
 * Safe conversion wrapper with error handling
 * @param {string} localDateTime - Local datetime string
 * @returns {string|null} UTC string or null if conversion fails
 */
export function safeConvertToUTC(localDateTime) {
  try {
    const date = new Date(localDateTime);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date format');
    }
    return date.toISOString().slice(0, 19);
  } catch (error) {
    console.error('Date conversion error:', error);
    return null;
  }
}

/**
 * Example usage for updating exam time
 * @param {string} examId - Exam ID
 * @param {string} localStartTime - Local start time
 * @param {number} duration - Duration in minutes
 * @returns {Promise} API response
 */
export async function updateExamTime(examId, localStartTime, duration) {
  // Convert local time to UTC
  const utcStartTime = convertLocalToUTC(localStartTime);
  
  if (!utcStartTime) {
    throw new Error('Invalid start time format');
  }
  
  // Send to API
  const response = await fetch(`/api/exams/${examId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: JSON.stringify({
      start_time: utcStartTime,
      total_duration: duration
    })
  });
  
  return response.json();
}

// Timezone examples for reference
export const TIMEZONE_EXAMPLES = {
  'Asia/Karachi': 'Pakistan Standard Time (UTC+5)',
  'America/New_York': 'Eastern Time (UTC-5/-4)',
  'Europe/London': 'Greenwich Mean Time (UTC+0/+1)',
  'Asia/Kolkata': 'India Standard Time (UTC+5:30)',
  'America/Los_Angeles': 'Pacific Time (UTC-8/-7)',
  'Asia/Tokyo': 'Japan Standard Time (UTC+9)',
  'Australia/Sydney': 'Australian Eastern Time (UTC+10/+11)'
};
